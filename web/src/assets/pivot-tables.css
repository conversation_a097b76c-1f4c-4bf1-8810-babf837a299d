/* ================================
   PIVOT TABLE SHARED STYLES
   ================================ */

/* Container styles */
.pivot-table-container {
  width: 100%;
  overflow-x: auto;
  color: #2b2b2b;
}

.pivot-table {
  border-radius: 0.5rem;
  border: 1px solid #e8e8e8;
  min-width: 100%;
}

/* ================================
   STATUS COLORS - UNIFIED
   ================================ */

/* SKU Detail Table Status Colors */
/*:deep(.status-normal) {*/
/*  color: #52c41a;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.status-warning) {*/
/*  color: #faad14;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.status-error) {*/
/*  color: #ff4d4f;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.status-default) {*/
/*  color: #666;*/
/*  font-weight: 500;*/
/*}*/

/*!* Metric Table Status Colors (có thể khác SKU Detail) *!*/
/*:deep(.metric-status-normal) {*/
/*  color: #52c41a;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.metric-status-warning) {*/
/*  color: #faad14;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.metric-status-error) {*/
/*  color: #ff4d4f;*/
/*  font-weight: 500;*/
/*}*/

/*:deep(.metric-status-default) {*/
/*  color: #666;*/
/*  font-weight: 500;*/
/*}*/

/* ================================
   MAIN TABLE STYLES
   ================================ */

.main-table-container {
  width: 100%;
}

.main-table {
  width: 100%;
}

.main-table-detail {
  margin-left: 32px;
}

/* ================================
   RESPONSIVE UTILITIES
   ================================ */

.table-responsive {
  overflow-x: auto;
  overflow-y: visible;
}

.table-fit-width {
  width: 100%;
  max-width: 100%;
}

/* ================================
   HOVER EFFECTS INTEGRATION
   ================================ */

.pivot-table-container.table-hover-effects {
  /* Styles được load từ table-hover.css */
}
