<script setup lang="ts">
type Props = {
  color: string
}

defineProps<Props>()
</script>

<template>
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.5 14V8H9.5V14H7.5V6H3.5V14H2V1H1V14C1 14.2652 1.10536 14.5196 1.29289 14.7071C1.48043 14.8946 1.73478 15 2 15H15V14H13.5ZM6.5 14H4.5V7H6.5V14ZM12.5 14H10.5V9H12.5V14Z" :fill="color"/>
    <path d="M11.3928 7C11.1808 7.0001 10.9743 6.93239 10.8034 6.80675L5.60205 2.9935L4.12095 5L3.3187 4.40325L4.8093 2.40325C4.96432 2.1945 5.19463 2.05442 5.45129 2.01278C5.70795 1.97114 5.97073 2.03122 6.1838 2.18025L11.3945 6.00025L13.1967 3.5696L14 4.1653L12.1978 6.59585C12.1045 6.72144 11.9831 6.82342 11.8433 6.89361C11.7035 6.9638 11.5492 7.00023 11.3928 7Z" :fill="color"/>
  </svg>
</template>
