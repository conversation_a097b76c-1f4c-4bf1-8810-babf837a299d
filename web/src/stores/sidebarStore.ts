// src/stores/sidebarStore.ts
import { defineStore } from 'pinia'
import type { GetCategoriesRequest } from '@/views/analytics/views/replenishment/domains'
import categoryService from '@/views/analytics/views/replenishment/services/CategoryService.ts'

export interface TreeSelectNode {
  value: string
  title: string
  children?: TreeSelectNode[]
  selectable?: boolean
  disabled?: boolean
  loading?: boolean
  loaded?: boolean
  error?: boolean
  errorMessage?: string
}

interface SidebarState {
  collapsed: boolean
  selectedFilters: string[]
  treeData: TreeSelectNode[]
  originalTreeData: TreeSelectNode[]
  searchValue: string
  loading: boolean
  error: string | null
  loadedNodes: Set<string> // Track which nodes have been loaded
}

export const useSidebarStore = defineStore('sidebar', {
  state: (): SidebarState => ({
    collapsed: true,
    selectedFilters: [],
    treeData: [],
    originalTreeData: [],
    searchValue: '',
    loading: false,
    error: null,
    loadedNodes: new Set()
  }),

  getters: {
    isCollapsed: (state) => state.collapsed,
    getSelectedFilters: (state) => state.selectedFilters,
    getTreeData: (state) => state.treeData,
    getSearchValue: (state) => state.searchValue,
    isLoading: (state) => state.loading,
    getError: (state) => state.error,

    // Get all available keys for select all functionality
    getAllKeys: (state) => {
      const getAllKeys = (nodes: TreeSelectNode[]): string[] => {
        let keys: string[] = []
        nodes.forEach(node => {
          // Chỉ lấy keys của nodes thực, không lấy placeholder
          if (!node.value.includes('_placeholder') && node.selectable !== false) {
            keys.push(node.value)
          }
          if (node.children && node.children.length > 0) {
            keys = keys.concat(getAllKeys(node.children))
          }
        })
        return keys
      }
      return getAllKeys(state.originalTreeData)
    },

    // Get filtered tree data based on search
    getFilteredTreeData: (state) => {
      if (!state.searchValue.trim()) {
        return state.originalTreeData
      }

      const filterTreeData = (nodes: TreeSelectNode[], searchTerm: string): TreeSelectNode[] => {
        const filteredNodes: TreeSelectNode[] = []

        nodes.forEach(node => {
          const nodeMatches = node.title.toLowerCase().includes(searchTerm.toLowerCase())
          let filteredChildren: TreeSelectNode[] = []

          if (node.children && node.children.length > 0) {
            filteredChildren = filterTreeData(node.children, searchTerm)
          }

          // Include node if it matches or has matching children
          if (nodeMatches || filteredChildren.length > 0) {
            filteredNodes.push({
              ...node,
              children: filteredChildren
            })
          }
        })

        return filteredNodes
      }

      return filterTreeData(state.originalTreeData, state.searchValue)
    }
  },

  actions: {
    toggleSidebar() {
      this.collapsed = !this.collapsed
    },

    updateSelectedFilters(filters: string[]) {
      // Filter out placeholder keys
      this.selectedFilters = filters.filter(key => !key.includes('_placeholder'))
    },

    selectAllFilters() {
      this.selectedFilters = this.getAllKeys
    },

    clearAllFilters() {
      this.selectedFilters = []
    },

    updateSearchValue(searchValue: string) {
      this.searchValue = searchValue
      this.treeData = this.getFilteredTreeData
    },

    resetSearch() {
      this.searchValue = ''
      this.treeData = this.originalTreeData
    },

    // Initialize - load root categories
    async init() {
      if (this.originalTreeData.length > 0) {
        return // Already initialized
      }

      this.loading = true
      this.error = null

      try {
        const request: GetCategoriesRequest = {} // Load root categories (no parentId)
        const categories = await categoryService.getCategoryListing(request)

        this.originalTreeData = categories
        this.treeData = categories
        this.loadedNodes.add('root')
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to load categories'
        console.error('Error loading root categories:', error)
      } finally {
        this.loading = false
      }
    },

    // Load children for a specific node
    async loadChildren(nodeValue: string) {
      // Check if already loading or loaded
      if (this.loadedNodes.has(nodeValue)) {
        return
      }

      // Find the node in tree data
      const node = this.findNodeByValue(nodeValue)
      if (!node) {
        console.error('Node not found:', nodeValue)
        return
      }

      // Set loading state for this node
      node.loading = true
      node.error = false
      node.errorMessage = undefined

      try {
        const request: GetCategoriesRequest = { parentId: nodeValue }
        const children = await categoryService.getCategoryListing(request)

        // Replace placeholder children with actual children
        node.children = children.length > 0 ? children : undefined
        node.loaded = true
        this.loadedNodes.add(nodeValue)

        // Update tree data to trigger reactivity
        this.updateTreeDataReactively()
      } catch (error) {
        node.error = true
        node.errorMessage = error instanceof Error ? error.message : 'Failed to load children'
        console.error(`Error loading children for node ${nodeValue}:`, error)

        // Keep placeholder on error
        if (node.children && node.children.length === 1 && node.children[0].value.includes('_placeholder')) {
          node.children[0].title = 'Error loading'
          node.children[0].disabled = true
        }
      } finally {
        node.loading = false
      }
    },

    // Helper method to update tree data reactively
    updateTreeDataReactively() {
      this.treeData = [...this.originalTreeData]
    },

    // Update tree data from external source (API call, etc.)
    updateTreeData(newTreeData: TreeSelectNode[]) {
      this.originalTreeData = newTreeData
      this.treeData = newTreeData
    },

    // Apply filters - can emit event or call API
    applyFilters() {
      console.log('Applying filters:', this.selectedFilters)
      // Add your API call or emit logic here
      return this.selectedFilters
    },

    // Cancel and reset all filters and search
    cancelFilters() {
      this.selectedFilters = []
      this.searchValue = ''
      this.treeData = this.originalTreeData
    },

    // Find node by value recursively
    findNodeByValue(value: string): TreeSelectNode | null {
      const findNode = (nodes: TreeSelectNode[], targetValue: string): TreeSelectNode | null => {
        for (const node of nodes) {
          if (node.value === targetValue) {
            return node
          }
          if (node.children) {
            const found = findNode(node.children, targetValue)
            if (found) return found
          }
        }
        return null
      }

      return findNode(this.originalTreeData, value)
    },

    // Get selected node titles for display
    getSelectedNodeTitles(): string[] {
      return this.selectedFilters
        .map(value => this.findNodeByValue(value)?.title)
        .filter(Boolean) as string[]
    }
  }
})
