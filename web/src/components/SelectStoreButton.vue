<script setup lang="ts">

import { h, ref } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SelectStoreModal
  from '@/views/analytics/views/inventory-overview/segments/monitor-metric-segment/components/modals/SelectStoreModal.vue'
import { useSelectedStore } from '@/views/analytics/views/inventory-overview/stores/SelectedStoreStore';

const store = useSelectedStore();

const isModalVisible = ref(false);

const showModal = () => {
  isModalVisible.value = true;
};

const handleSave = (data: string[]) => {
  store.setSelectedStoreIds(data);
  isModalVisible.value = false;
};

const handleClose = () => {
  isModalVisible.value = false;
};
</script>

<template>
  <AFlex gap="small" align="center">
    <ATypography>Đã chọn <span class="total-selected-text">{{ store.selectedStoreIds.length }} siêu thị</span></ATypography>
    <AButton type="primary" :icon ="h(PlusOutlined)" @click="showModal">Chọn siêu thị</AButton>
  </AFlex>

  <SelectStoreModal v-if="isModalVisible" :is-open="isModalVisible" @close="handleClose" @save="handleSave"/>
</template>

<style scoped>
.total-selected-text {
  font-weight: 700;
  color: #007bff;
}
</style>
