import type { VNode } from 'vue'

// Base column definition
export interface BaseTableColumn {
  /** Column title */
  title: string
  /** Data index for accessing record data */
  dataIndex?: string
  /** Unique key for the column */
  key: string
  /** Column width */
  width?: number | string
  /** Minimum width */
  minWidth?: number
  /** Maximum width */
  maxWidth?: number
  /** Text alignment */
  align?: 'left' | 'center' | 'right'
  /** Whether column is sortable */
  sortable?: boolean
  /** Whether column is filterable */
  filterable?: boolean
  /** Whether column is resizable */
  resizable?: boolean
  /** Whether to show ellipsis for long text */
  ellipsis?: boolean
  /** Fixed column position */
  fixed?: 'left' | 'right'
  /** Custom render function */
  customRender?: (params: {
    text: any
    record: any
    index: number
    column: BaseTableColumn
  }) => VNode | string | number
  /** Custom header render function */
  customHeaderRender?: (params: {
    title: string
    column: BaseTableColumn
  }) => VNode | string
  /** Filter options for dropdown filter */
  filters?: Array<{ text: string; value: any }>
  /** Custom filter function */
  onFilter?: (value: any, record: any) => boolean
  /** Sort function */
  sorter?: boolean | ((a: any, b: any) => number)
  /** Default sort order */
  defaultSortOrder?: 'ascend' | 'descend'
  /** CSS class name for column */
  className?: string
  /** Whether column is hidden */
  hidden?: boolean
  /** Nested columns for grouped headers */
  children?: BaseTableColumn[]
}

// Table configuration
export interface TableConfig {
  /** Whether table is bordered */
  bordered?: boolean
  /** Table size */
  size?: 'small' | 'middle' | 'large'
  /** Whether to show header */
  showHeader?: boolean
  /** Whether rows are striped */
  striped?: boolean
  /** Whether to show hover effect */
  hoverable?: boolean
  /** Row selection configuration */
  rowSelection?: RowSelectionConfig
  /** Pagination configuration */
  pagination?: PaginationConfig | false
  /** Scroll configuration */
  scroll?: {
    x?: number | string | true
    y?: number | string
  }
  /** Loading state */
  loading?: boolean
  /** Empty state configuration */
  empty?: {
    description?: string
    image?: string
  }
  /** Row expansion configuration */
  expandable?: ExpandableConfig
  /** Virtual scrolling for large datasets */
  virtual?: boolean
  /** Sticky header */
  stickyHeader?: boolean
  /** Table height */
  height?: number | string
  /** Maximum height */
  maxHeight?: number | string
}

// Row selection configuration
export interface RowSelectionConfig {
  /** Selection type */
  type?: 'checkbox' | 'radio'
  /** Selected row keys */
  selectedRowKeys?: (string | number)[]
  /** Callback when selection changes */
  onChange?: (selectedRowKeys: (string | number)[], selectedRows: any[]) => void
  /** Callback when select/deselect one row */
  onSelect?: (record: any, selected: boolean, selectedRows: any[], nativeEvent: Event) => void
  /** Callback when select/deselect all rows */
  onSelectAll?: (selected: boolean, selectedRows: any[], changeRows: any[]) => void
  /** Function to determine if row is selectable */
  getCheckboxProps?: (record: any) => { disabled?: boolean; name?: string }
  /** Whether to show select all checkbox */
  showSelectAll?: boolean
  /** Custom selection column width */
  columnWidth?: number | string
  /** Fixed selection column */
  fixed?: boolean
}

// Pagination configuration
export interface PaginationConfig {
  /** Current page number */
  current?: number
  /** Total number of items */
  total?: number
  /** Number of items per page */
  pageSize?: number
  /** Available page sizes */
  pageSizeOptions?: string[]
  /** Whether to show page size selector */
  showSizeChanger?: boolean
  /** Whether to show quick jumper */
  showQuickJumper?: boolean
  /** Whether to show total count */
  showTotal?: boolean | ((total: number, range: [number, number]) => string)
  /** Position of pagination */
  position?: 'top' | 'bottom' | 'both'
  /** Callback when page changes */
  onChange?: (page: number, pageSize: number) => void
  /** Callback when page size changes */
  onShowSizeChange?: (current: number, size: number) => void
}

// Row expansion configuration
export interface ExpandableConfig {
  /** Expanded row keys */
  expandedRowKeys?: (string | number)[]
  /** Default expanded row keys */
  defaultExpandedRowKeys?: (string | number)[]
  /** Whether all rows are expandable */
  expandRowByClick?: boolean
  /** Custom expand icon */
  expandIcon?: (props: {
    expanded: boolean
    onExpand: (record: any, event: Event) => void
    record: any
  }) => VNode
  /** Expanded row render function */
  expandedRowRender?: (record: any, index: number, indent: number, expanded: boolean) => VNode
  /** Callback when expand state changes */
  onExpand?: (expanded: boolean, record: any) => void
  /** Callback when expanded rows change */
  onExpandedRowsChange?: (expandedRows: (string | number)[]) => void
  /** Function to determine if row is expandable */
  rowExpandable?: (record: any) => boolean
  /** Indentation size for expanded content */
  indentSize?: number
  /** Whether to show expand column */
  showExpandColumn?: boolean
  /** Expand column width */
  columnWidth?: number | string
  /** Fixed expand column */
  fixed?: boolean | 'left' | 'right'
}

// Sort configuration
export interface SortConfig {
  /** Sort field */
  field?: string
  /** Sort order */
  order?: 'ascend' | 'descend' | null
}

// Filter configuration
export interface FilterConfig {
  /** Filter field */
  field: string
  /** Filter value */
  value: any
  /** Filter operator */
  operator?: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between'
}

// Table events
export interface TableEvents {
  /** Row click event */
  onRowClick?: (record: any, index: number, event: Event) => void
  /** Row double click event */
  onRowDoubleClick?: (record: any, index: number, event: Event) => void
  /** Row context menu event */
  onRowContextMenu?: (record: any, index: number, event: Event) => void
  /** Row mouse enter event */
  onRowMouseEnter?: (record: any, index: number, event: Event) => void
  /** Row mouse leave event */
  onRowMouseLeave?: (record: any, index: number, event: Event) => void
  /** Header click event */
  onHeaderClick?: (column: BaseTableColumn, event: Event) => void
  /** Sort change event */
  onSortChange?: (sort: SortConfig) => void
  /** Filter change event */
  onFilterChange?: (filters: FilterConfig[]) => void
  /** Column resize event */
  onColumnResize?: (column: BaseTableColumn, width: number) => void
}

// Table data source
export type TableDataSource = any[]

// Table props
export interface BaseTableProps {
  /** Table columns */
  columns: BaseTableColumn[]
  /** Table data source */
  dataSource: TableDataSource
  /** Row key field or function */
  rowKey?: string | ((record: any) => string | number)
  /** Table configuration */
  config?: TableConfig
  /** Table events */
  events?: TableEvents
  /** CSS class name */
  className?: string
  /** Inline styles */
  style?: Record<string, any>
}
