# BaseTable Configuration Guide

## Overview

BaseTable is a wrapper around Ant Design Vue Table with enhanced features and standardized configurations. This guide covers all available configuration options and best practices.

## Quick Start

### Basic Usage

```vue
<template>
  <BaseTable
    :columns="columns"
    :data-source="dataSource"
    :config="tableConfig"
    row-key="id"
  />
</template>

<script setup lang="ts">
import BaseTable from '@/components/table/BaseTable.vue'
import { createDefaultTableConfig } from '@/components/table/configs/DefaultTableConfig'

const tableConfig = createDefaultTableConfig({
  pagination: {
    pageSize: 100
  }
})
</script>
```

## Default Configurations

### 1. createDefaultTableConfig()
Basic configuration suitable for most tables.

```typescript
const config = createDefaultTableConfig({
  // Override any default settings
  size: 'small',
  pagination: { pageSize: 20 }
})
```

**Default settings:**
- `bordered: true`
- `size: 'middle'`
- `striped: true`
- `scroll: { x: 'max-content', y: 600 }`
- `pagination: { pageSize: 50, showSizeChanger: true }`

### 2. createExpandableTableConfig()
For tables with expandable rows.

```typescript
const config = createExpandableTableConfig(
  (record, index) => h(MyExpandComponent, { record }),
  {
    scroll: { y: 800 } // Override scroll height
  }
)
```

### 3. createSelectableTableConfig()
For tables with row selection.

```typescript
const config = createSelectableTableConfig(
  (selectedKeys, selectedRows) => {
    console.log('Selected:', selectedKeys)
  }
)
```

### 4. createDataTableConfig()
Optimized for data-heavy tables.

```typescript
const config = createDataTableConfig({
  scroll: { x: 1600, y: 800 }
})
```

### 5. createCompactTableConfig()
For dashboard or summary tables.

```typescript
const config = createCompactTableConfig()
```

## Configuration Options

### Core Properties

```typescript
interface TableConfig {
  loading?: boolean
  bordered?: boolean
  size?: 'small' | 'middle' | 'large'
  striped?: boolean
  scroll?: {
    x?: number | string
    y?: number | string
  }
  pagination?: PaginationConfig
  rowSelection?: RowSelectionConfig
  expandable?: ExpandableConfig
  locale?: LocaleConfig
}
```

### Pagination Configuration

```typescript
interface PaginationConfig {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
  pageSizeOptions?: string[]
  size?: 'small' | 'default'
  onChange?: (page: number, pageSize: number) => void
  onShowSizeChange?: (current: number, size: number) => void
}
```

### Expandable Configuration

```typescript
interface ExpandableConfig {
  expandedRowKeys?: (string | number)[]
  expandedRowRender?: (record: any, index: number) => any
  expandRowByClick?: boolean
  rowExpandable?: (record: any) => boolean
  showExpandColumn?: boolean
  columnWidth?: number
  fixed?: boolean | 'left' | 'right'
  onExpand?: (expanded: boolean, record: any) => void
  onExpandedRowsChange?: (expandedRowKeys: (string | number)[]) => void
}
```

## Column Configuration

### Basic Column

```typescript
interface BaseTableColumn {
  title: string
  key: string
  dataIndex?: string
  width?: number
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  ellipsis?: boolean
  customRender?: (params: RenderParams) => any
  children?: BaseTableColumn[] // For nested headers
}
```

### Custom Render Example

```typescript
const columns: BaseTableColumn[] = [
  {
    title: 'Status',
    key: 'status',
    customRender: ({ text, record }) => 
      h(Tag, { color: getStatusColor(text) }, () => text)
  },
  {
    title: 'Actions',
    key: 'actions',
    customRender: ({ record }) => 
      h(Button, { onClick: () => handleEdit(record) }, () => 'Edit')
  }
]
```

## Best Practices

### 1. Use Default Configs
Always start with a default configuration and override only what you need.

```typescript
// ✅ Good
const config = createDataTableConfig({
  pagination: { pageSize: 100 }
})

// ❌ Avoid
const config = {
  loading: false,
  bordered: true,
  size: 'middle',
  // ... manually defining everything
}
```

### 2. Responsive Design
Use appropriate scroll configurations for different screen sizes.

```typescript
const config = createDefaultTableConfig({
  scroll: {
    x: 1200, // Minimum width before horizontal scroll
    y: 600   // Fixed height with vertical scroll
  }
})
```

### 3. Performance Optimization
For large datasets, use virtual scrolling and appropriate page sizes.

```typescript
const config = createDataTableConfig({
  pagination: {
    pageSize: 50, // Reasonable page size
    showSizeChanger: true,
    pageSizeOptions: ['20', '50', '100']
  }
})
```

### 4. Expandable Tables
When using expandable rows, hide the default expand column if using custom triggers.

```typescript
const config = createExpandableTableConfig(
  (record) => h(MyExpandComponent, { record }),
  {
    expandable: {
      showExpandColumn: false, // Hide default expand column
      expandRowByClick: false  // Use custom click handlers
    }
  }
)
```

## Common Patterns

### 1. Search + Pagination Table

```vue
<template>
  <div>
    <Input.Search 
      v-model:value="searchValue" 
      @search="handleSearch"
      style="margin-bottom: 16px"
    />
    <BaseTable
      :columns="columns"
      :data-source="filteredData"
      :config="tableConfig"
      row-key="id"
    />
  </div>
</template>

<script setup lang="ts">
const tableConfig = createDataTableConfig({
  pagination: {
    onChange: handlePaginationChange,
    onShowSizeChange: handlePageSizeChange
  }
})
</script>
```

### 2. Expandable + Selectable Table

```typescript
const config = createSelectableTableConfig(
  handleSelectionChange,
  {
    expandable: {
      expandedRowRender: (record) => h(DetailComponent, { record }),
      showExpandColumn: false
    }
  }
)
```

### 3. Compact Dashboard Table

```typescript
const config = createCompactTableConfig({
  pagination: false, // No pagination for small datasets
  scroll: { y: 300 }
})
```

## Troubleshooting

### Common Issues

1. **Expand column still showing**: Use CSS to hide it completely
2. **Scroll not working**: Check container height and scroll config
3. **Performance issues**: Reduce page size and use virtual scrolling
4. **Responsive issues**: Set appropriate scroll.x values

### Debug Tips

1. Use browser dev tools to inspect table structure
2. Check console for configuration warnings
3. Test with different data sizes
4. Verify column widths sum correctly
