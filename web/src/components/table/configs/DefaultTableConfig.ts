import type { TableConfig } from '@/components/table/types/TableTypes'

/**
 * Default Table Configuration
 *
 * This provides sensible defaults for most table use cases.
 * Individual tables can override specific properties as needed.
 */
export const createDefaultTableConfig = (overrides: Partial<TableConfig> = {}): TableConfig => {
  const defaultConfig: TableConfig = {
    // Loading state
    loading: false,

    // Table appearance
    bordered: true,
    size: 'middle',
    striped: true,

    // Scroll configuration
    scroll: {
      x: 'max-content', // Auto horizontal scroll
      y: 600 // Fixed height with vertical scroll
    },

    // Pagination configuration
    pagination: {
      current: 1,
      pageSize: 50,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
      pageSizeOptions: ['20', '50', '100', '200'],
    },

    // Selection configuration (disabled by default)
    rowSelection: undefined,

    // Expandable configuration (disabled by default)
    expandable: undefined,
  }

  // Deep merge with overrides
  return mergeConfig(defaultConfig, overrides)
}

/**
 * Default configuration for expandable tables
 */
export const createExpandableTableConfig = (
  expandedRowRender: (record: any, index: number) => any,
  overrides: Partial<TableConfig> = {}
): TableConfig => {
  const expandableConfig: Partial<TableConfig> = {
    expandable: {
      expandedRowRender,
      expandRowByClick: true, // Enable click to expand
      rowExpandable: () => true,
      showExpandColumn: false, // Hide default expand column by default
      columnWidth: 50,
      fixed: false
    }
  }

  return createDefaultTableConfig({
    ...expandableConfig,
    ...overrides
  })
}

/**
 * Default configuration for selectable tables
 */
export const createSelectableTableConfig = (
  onSelectionChange: (selectedRowKeys: (string | number)[], selectedRows: any[]) => void,
  overrides: Partial<TableConfig> = {}
): TableConfig => {
  const selectableConfig: Partial<TableConfig> = {
    rowSelection: {
      type: 'checkbox',
      onChange: onSelectionChange,
      getCheckboxProps: (record) => ({
        disabled: false,
        name: record.id || record.key
      })
    }
  }

  return createDefaultTableConfig({
    ...selectableConfig,
    ...overrides
  })
}

/**
 * Default configuration for data tables with common features
 */
export const createDataTableConfig = (overrides: Partial<TableConfig> = {}): TableConfig => {
  return createDefaultTableConfig({
    bordered: true,
    striped: true,
    size: 'middle',
    scroll: {
      x: 1200,
      y: 600
    },
    pagination: {
      current: 1,
      pageSize: 50,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    ...overrides
  })
}

/**
 * Compact table configuration for dashboards
 */
export const createCompactTableConfig = (overrides: Partial<TableConfig> = {}): TableConfig => {
  return createDefaultTableConfig({
    size: 'small',
    bordered: false,
    striped: false,
    pagination: {
      showSizeChanger: false,
      showQuickJumper: false,
      pageSize: 20
    },
    ...overrides
  })
}

/**
 * Deep merge utility for config objects
 */
function mergeConfig(target: any, source: any): any {
  const result = { ...target }

  for (const key in source) {
    if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeConfig(target[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }

  return result
}

/**
 * Export default config as default export
 */
export default createDefaultTableConfig
