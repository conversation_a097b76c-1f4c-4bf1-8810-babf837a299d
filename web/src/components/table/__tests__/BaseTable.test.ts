import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import BaseTable from '../BaseTable.vue'
import type { BaseTableColumn, TableConfig } from '../types/TableTypes'

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  ATable: {
    name: 'ATable',
    template: '<div class="ant-table"><slot /></div>',
    props: ['columns', 'dataSource', 'rowKey', 'loading', 'pagination', 'scroll']
  },
  AAlert: {
    name: '<PERSON><PERSON><PERSON>',
    template: '<div class="ant-alert"><slot /></div>',
    props: ['message', 'type', 'showIcon', 'closable']
  },
  AEmpty: {
    name: 'AEmpty',
    template: '<div class="ant-empty"><slot /></div>',
    props: ['description', 'image']
  }
}))

describe('BaseTable', () => {
  const sampleColumns: BaseTableColumn[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      sortable: true
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      sortable: true,
      filterable: true
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 100,
      sortable: true
    }
  ]

  const sampleData = [
    { id: 1, name: 'John Doe', age: 30 },
    { id: 2, name: 'Jane Smith', age: 25 },
    { id: 3, name: 'Bob Johnson', age: 35 }
  ]

  let wrapper: any

  beforeEach(() => {
    wrapper = mount(BaseTable, {
      props: {
        columns: sampleColumns,
        dataSource: sampleData,
        rowKey: 'id'
      },
      global: {
        stubs: {
          ATable: true,
          AAlert: true,
          AEmpty: true
        }
      }
    })
  })

  it('renders correctly with basic props', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.base-table-container').exists()).toBe(true)
  })

  it('applies correct CSS classes based on config', async () => {
    const config: TableConfig = {
      size: 'small',
      bordered: true,
      striped: true,
      hoverable: false
    }

    await wrapper.setProps({ config })

    const container = wrapper.find('.base-table-container')
    expect(container.classes()).toContain('base-table--small')
    expect(container.classes()).toContain('base-table--bordered')
    expect(container.classes()).toContain('base-table--striped')
    expect(container.classes()).not.toContain('base-table--hoverable')
  })

  it('shows loading state correctly', async () => {
    const config: TableConfig = {
      loading: true
    }

    await wrapper.setProps({ config })

    const container = wrapper.find('.base-table-container')
    expect(container.classes()).toContain('base-table--loading')
  })

  it('handles row selection configuration', async () => {
    const config: TableConfig = {
      rowSelection: {
        type: 'checkbox',
        showSelectAll: true
      }
    }

    await wrapper.setProps({ config })
    await nextTick()

    // Check if row selection config is properly computed
    const vm = wrapper.vm
    expect(vm.rowSelectionConfig).toBeDefined()
    expect(vm.rowSelectionConfig.type).toBe('checkbox')
  })

  it('handles pagination configuration', async () => {
    const config: TableConfig = {
      pagination: {
        current: 1,
        pageSize: 10,
        total: 100,
        showSizeChanger: true
      }
    }

    await wrapper.setProps({ config })
    await nextTick()

    const vm = wrapper.vm
    expect(vm.paginationConfig).toBeDefined()
    expect(vm.paginationConfig.pageSize).toBe(10)
    expect(vm.paginationConfig.showSizeChanger).toBe(true)
  })

  it('emits events correctly', async () => {
    // Test sort change event
    const vm = wrapper.vm
    vm.handleTableChange({}, {}, { field: 'name', order: 'ascend' })

    await nextTick()
    expect(wrapper.emitted('sortChange')).toBeTruthy()
    expect(wrapper.emitted('sortChange')[0]).toEqual([{ field: 'name', order: 'ascend' }])
  })

  it('handles custom row props correctly', () => {
    const vm = wrapper.vm
    const record = sampleData[0]
    const customProps = vm.getCustomRowProps(record, 0)

    expect(customProps).toHaveProperty('onClick')
    expect(customProps).toHaveProperty('onDblclick')
    expect(customProps).toHaveProperty('onContextmenu')
  })

  it('generates correct row keys', () => {
    const vm = wrapper.vm

    // Test with default rowKey prop
    expect(vm.getRowKey(sampleData[0])).toBe(1)

    // Test with function rowKey
    wrapper.setProps({ rowKey: (record: any) => `key-${record.id}` })
    expect(vm.getRowKey(sampleData[0])).toBe('key-1')
  })

  it('applies correct row class names', () => {
    const vm = wrapper.vm
    const record = sampleData[0]

    // Mock selected state
    vm.tableState.selectedRowKeys.value = [1]

    const className = vm.getRowClassName(record, 0)
    expect(className).toContain('base-table__row--selected')
  })

  it('handles scroll configuration', async () => {
    const config: TableConfig = {
      scroll: {
        x: 1200,
        y: 400
      }
    }

    await wrapper.setProps({ config })
    await nextTick()

    const vm = wrapper.vm
    expect(vm.scrollConfig.x).toBe(1200)
    expect(vm.scrollConfig.y).toBe(400)
  })

  it('shows error alert when error prop is provided', async () => {
    const vm = wrapper.vm
    vm.error = 'Test error message'

    await nextTick()

    const alert = wrapper.find('.ant-alert')
    expect(alert.exists()).toBe(true)
  })

  it('handles table height configuration', async () => {
    const config: TableConfig = {
      height: 400,
      maxHeight: 600
    }

    await wrapper.setProps({ config })
    await nextTick()

    const vm = wrapper.vm
    expect(vm.tableContainerStyle.height).toBe('400px')
    expect(vm.tableContainerStyle.maxHeight).toBe('600px')
  })

  it('exposes correct methods', () => {
    const vm = wrapper.vm

    expect(vm.tableState).toBeDefined()
    expect(vm.tableColumns).toBeDefined()
    expect(typeof vm.clearSelection).toBe('function')
    expect(typeof vm.clearFilters).toBe('function')
    expect(typeof vm.clearSort).toBe('function')
    expect(typeof vm.expandAll).toBe('function')
    expect(typeof vm.collapseAll).toBe('function')
    expect(typeof vm.refresh).toBe('function')
  })

  it('handles nested columns correctly', async () => {
    const nestedColumns: BaseTableColumn[] = [
      {
        title: 'Personal Info',
        key: 'personal',
        children: [
          {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            width: 150
          },
          {
            title: 'Age',
            dataIndex: 'age',
            key: 'age',
            width: 100
          }
        ]
      }
    ]

    await wrapper.setProps({ columns: nestedColumns })
    await nextTick()

    const vm = wrapper.vm
    expect(vm.antdColumns).toBeDefined()
    expect(vm.antdColumns[0].children).toBeDefined()
  })

  it('handles custom render functions', async () => {
    const columnsWithCustomRender: BaseTableColumn[] = [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        customRender: ({ text, record, index, column }) => `Custom: ${text}`
      }
    ]

    await wrapper.setProps({ columns: columnsWithCustomRender })
    await nextTick()

    const vm = wrapper.vm
    const antdColumn = vm.antdColumns[0]
    expect(antdColumn.customRender).toBeDefined()
  })

  it('handles filter configuration correctly', async () => {
    const columnsWithFilters: BaseTableColumn[] = [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        filters: [
          { text: 'John', value: 'John' },
          { text: 'Jane', value: 'Jane' }
        ],
        onFilter: (value, record) => record.name.includes(value)
      }
    ]

    await wrapper.setProps({ columns: columnsWithFilters })
    await nextTick()

    const vm = wrapper.vm
    const antdColumn = vm.antdColumns[0]
    expect(antdColumn.filters).toBeDefined()
    expect(antdColumn.onFilter).toBeDefined()
  })
})
