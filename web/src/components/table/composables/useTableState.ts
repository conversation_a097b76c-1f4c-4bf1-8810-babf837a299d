import { ref, computed, watch, type Ref } from 'vue'
import type {
  BaseTableColumn,
  SortConfig,
  FilterConfig,
  TableDataSource,
  RowSelectionConfig
} from '../types/TableTypes'

export interface UseTableStateOptions {
  /** Initial sort configuration */
  defaultSort?: SortConfig
  /** Initial filter configuration */
  defaultFilters?: FilterConfig[]
  /** Initial selected row keys */
  defaultSelectedRowKeys?: (string | number)[]
  /** Initial expanded row keys */
  defaultExpandedRowKeys?: (string | number)[]
}

export function useTableState(
  dataSource: Ref<TableDataSource>,
  columns: Ref<BaseTableColumn[]>,
  options: UseTableStateOptions = {}
) {
  // Sort state
  const sortConfig = ref<SortConfig>(options.defaultSort || {})

  // Filter state
  const filterConfigs = ref<FilterConfig[]>(options.defaultFilters || [])

  // Selection state
  const selectedRowKeys = ref<(string | number)[]>(options.defaultSelectedRowKeys || [])
  const selectedRows = ref<any[]>([])

  // Expansion state
  const expandedRowKeys = ref<(string | number)[]>(options.defaultExpandedRowKeys || [])

  // Loading state
  const loading = ref(false)

  // Error state
  const error = ref<string | null>(null)

  // Computed processed data
  const processedData = computed(() => {
    let result = [...dataSource.value]

    // Apply filters
    if (filterConfigs.value.length > 0) {
      result = result.filter(record => {
        return filterConfigs.value.every(filter => {
          const value = record[filter.field]
          const filterValue = filter.value

          switch (filter.operator || 'eq') {
            case 'eq':
              return value === filterValue
            case 'ne':
              return value !== filterValue
            case 'gt':
              return value > filterValue
            case 'gte':
              return value >= filterValue
            case 'lt':
              return value < filterValue
            case 'lte':
              return value <= filterValue
            case 'like':
              return String(value).toLowerCase().includes(String(filterValue).toLowerCase())
            case 'in':
              return Array.isArray(filterValue) ? filterValue.includes(value) : false
            case 'between':
              return Array.isArray(filterValue) && filterValue.length === 2
                ? value >= filterValue[0] && value <= filterValue[1]
                : false
            default:
              return true
          }
        })
      })
    }

    // Apply sorting
    if (sortConfig.value.field && sortConfig.value.order) {
      const column = columns.value.find(col => col.dataIndex === sortConfig.value.field)
      if (column) {
        result.sort((a, b) => {
          const aValue = a[sortConfig.value.field!]
          const bValue = b[sortConfig.value.field!]

          let compareResult = 0

          if (typeof column.sorter === 'function') {
            compareResult = column.sorter(a, b)
          } else {
            // Default sorting logic
            if (aValue < bValue) compareResult = -1
            else if (aValue > bValue) compareResult = 1
            else compareResult = 0
          }

          return sortConfig.value.order === 'descend' ? -compareResult : compareResult
        })
      }
    }

    return result
  })

  // Update selected rows when selectedRowKeys changes
  watch([selectedRowKeys, processedData], () => {
    selectedRows.value = processedData.value.filter(record => {
      const key = typeof record === 'object' && record !== null
        ? record.id || record.key
        : record
      return selectedRowKeys.value.includes(key)
    })
  }, { immediate: true })

  // Sort methods
  const handleSort = (field: string, order: 'ascend' | 'descend' | null | undefined) => {
    sortConfig.value = { field, order }
  }

  const clearSort = () => {
    sortConfig.value = {}
  }

  // Filter methods
  const handleFilter = (field: string, value: any, operator: FilterConfig['operator'] = 'eq') => {
    const existingIndex = filterConfigs.value.findIndex(f => f.field === field)

    if (value === null || value === undefined || value === '') {
      // Remove filter
      if (existingIndex >= 0) {
        filterConfigs.value.splice(existingIndex, 1)
      }
    } else {
      // Add or update filter
      const newFilter: FilterConfig = { field, value, operator }
      if (existingIndex >= 0) {
        filterConfigs.value[existingIndex] = newFilter
      } else {
        filterConfigs.value.push(newFilter)
      }
    }
  }

  const clearFilters = () => {
    filterConfigs.value = []
  }

  const clearFilter = (field: string) => {
    const index = filterConfigs.value.findIndex(f => f.field === field)
    if (index >= 0) {
      filterConfigs.value.splice(index, 1)
    }
  }

  // Selection methods
  const handleRowSelect = (record: any, selected: boolean) => {
    const key = typeof record === 'object' && record !== null
      ? record.id || record.key
      : record

    if (selected) {
      if (!selectedRowKeys.value.includes(key)) {
        selectedRowKeys.value.push(key)
      }
    } else {
      const index = selectedRowKeys.value.indexOf(key)
      if (index >= 0) {
        selectedRowKeys.value.splice(index, 1)
      }
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      selectedRowKeys.value = processedData.value.map(record =>
        typeof record === 'object' && record !== null
          ? record.id || record.key
          : record
      )
    } else {
      selectedRowKeys.value = []
    }
  }

  const clearSelection = () => {
    selectedRowKeys.value = []
  }

  // Expansion methods
  const handleRowExpand = (record: any, expanded: boolean) => {
    const key = typeof record === 'object' && record !== null
      ? record.id || record.key
      : record

    if (expanded) {
      if (!expandedRowKeys.value.includes(key)) {
        expandedRowKeys.value.push(key)
      }
    } else {
      const index = expandedRowKeys.value.indexOf(key)
      if (index >= 0) {
        expandedRowKeys.value.splice(index, 1)
      }
    }
  }

  const expandAll = () => {
    expandedRowKeys.value = processedData.value.map(record =>
      typeof record === 'object' && record !== null
        ? record.id || record.key
        : record
    )
  }

  const collapseAll = () => {
    expandedRowKeys.value = []
  }

  // Utility methods
  const getRowKey = (record: any, index: number): string | number => {
    if (typeof record === 'object' && record !== null) {
      return record.id || record.key || index
    }
    return record || index
  }

  const isRowSelected = (record: any): boolean => {
    const key = getRowKey(record, 0)
    return selectedRowKeys.value.includes(key)
  }

  const isRowExpanded = (record: any): boolean => {
    const key = getRowKey(record, 0)
    return expandedRowKeys.value.includes(key)
  }

  return {
    // State
    sortConfig,
    filterConfigs,
    selectedRowKeys,
    selectedRows,
    expandedRowKeys,
    loading,
    error,
    processedData,

    // Sort methods
    handleSort,
    clearSort,

    // Filter methods
    handleFilter,
    clearFilters,
    clearFilter,

    // Selection methods
    handleRowSelect,
    handleSelectAll,
    clearSelection,

    // Expansion methods
    handleRowExpand,
    expandAll,
    collapseAll,

    // Utility methods
    getRowKey,
    isRowSelected,
    isRowExpanded
  }
}
