import { computed, ref, type Ref } from 'vue'
import type { BaseTableColumn } from '../types/TableTypes'

export interface UseTableColumnsOptions {
  /** Whether columns are resizable */
  resizable?: boolean
  /** Minimum column width */
  minWidth?: number
  /** Maximum column width */
  maxWidth?: number
  /** Default column width */
  defaultWidth?: number
}

export function useTableColumns(
  columns: Ref<BaseTableColumn[]>,
  options: UseTableColumnsOptions = {}
) {
  const {
    resizable = false,
    minWidth = 50,
    maxWidth = 500,
    defaultWidth = 120
  } = options

  // Column widths state
  const columnWidths = ref<Record<string, number>>({})
  
  // Hidden columns state
  const hiddenColumns = ref<Set<string>>(new Set())

  // Processed columns with computed properties
  const processedColumns = computed(() => {
    return columns.value
      .filter(column => !hiddenColumns.value.has(column.key))
      .map(column => {
        const processedColumn = { ...column }
        
        // Apply custom width if set
        if (columnWidths.value[column.key]) {
          processedColumn.width = columnWidths.value[column.key]
        } else if (!processedColumn.width) {
          processedColumn.width = defaultWidth
        }
        
        // Add resizable property if enabled
        if (resizable) {
          processedColumn.resizable = column.resizable !== false
        }
        
        // Process nested columns recursively
        if (column.children && column.children.length > 0) {
          processedColumn.children = column.children
            .filter(child => !hiddenColumns.value.has(child.key))
            .map(child => ({
              ...child,
              width: columnWidths.value[child.key] || child.width || defaultWidth,
              resizable: resizable ? child.resizable !== false : false
            }))
        }
        
        return processedColumn
      })
  })

  // Visible columns (flattened)
  const visibleColumns = computed(() => {
    const flattenColumns = (cols: BaseTableColumn[]): BaseTableColumn[] => {
      return cols.reduce((acc, col) => {
        if (col.children && col.children.length > 0) {
          acc.push(...flattenColumns(col.children))
        } else {
          acc.push(col)
        }
        return acc
      }, [] as BaseTableColumn[])
    }
    
    return flattenColumns(processedColumns.value)
  })

  // Total table width
  const totalWidth = computed(() => {
    return visibleColumns.value.reduce((total, column) => {
      const width = typeof column.width === 'number' 
        ? column.width 
        : defaultWidth
      return total + width
    }, 0)
  })

  // Column management methods
  const setColumnWidth = (columnKey: string, width: number) => {
    const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width))
    columnWidths.value[columnKey] = constrainedWidth
  }

  const resetColumnWidth = (columnKey: string) => {
    delete columnWidths.value[columnKey]
  }

  const resetAllColumnWidths = () => {
    columnWidths.value = {}
  }

  const hideColumn = (columnKey: string) => {
    hiddenColumns.value.add(columnKey)
  }

  const showColumn = (columnKey: string) => {
    hiddenColumns.value.delete(columnKey)
  }

  const toggleColumnVisibility = (columnKey: string) => {
    if (hiddenColumns.value.has(columnKey)) {
      showColumn(columnKey)
    } else {
      hideColumn(columnKey)
    }
  }

  const showAllColumns = () => {
    hiddenColumns.value.clear()
  }

  const hideAllColumns = () => {
    columns.value.forEach(column => {
      hiddenColumns.value.add(column.key)
      if (column.children) {
        column.children.forEach(child => {
          hiddenColumns.value.add(child.key)
        })
      }
    })
  }

  // Column utilities
  const getColumnByKey = (key: string): BaseTableColumn | undefined => {
    const findColumn = (cols: BaseTableColumn[]): BaseTableColumn | undefined => {
      for (const col of cols) {
        if (col.key === key) {
          return col
        }
        if (col.children) {
          const found = findColumn(col.children)
          if (found) return found
        }
      }
      return undefined
    }
    
    return findColumn(columns.value)
  }

  const getColumnIndex = (key: string): number => {
    return visibleColumns.value.findIndex(col => col.key === key)
  }

  const isColumnVisible = (key: string): boolean => {
    return !hiddenColumns.value.has(key)
  }

  const getColumnWidth = (key: string): number => {
    return columnWidths.value[key] || defaultWidth
  }

  // Column ordering
  const columnOrder = ref<string[]>([])

  const reorderColumns = (newOrder: string[]) => {
    columnOrder.value = newOrder
  }

  const moveColumn = (fromIndex: number, toIndex: number) => {
    const currentOrder = columnOrder.value.length > 0 
      ? columnOrder.value 
      : visibleColumns.value.map(col => col.key)
    
    const newOrder = [...currentOrder]
    const [movedItem] = newOrder.splice(fromIndex, 1)
    newOrder.splice(toIndex, 0, movedItem)
    
    columnOrder.value = newOrder
  }

  // Ordered columns based on columnOrder
  const orderedColumns = computed(() => {
    if (columnOrder.value.length === 0) {
      return processedColumns.value
    }
    
    const ordered: BaseTableColumn[] = []
    const remaining = [...processedColumns.value]
    
    // Add columns in specified order
    columnOrder.value.forEach(key => {
      const index = remaining.findIndex(col => col.key === key)
      if (index >= 0) {
        ordered.push(remaining.splice(index, 1)[0])
      }
    })
    
    // Add any remaining columns
    ordered.push(...remaining)
    
    return ordered
  })

  // Column groups for nested headers
  const columnGroups = computed(() => {
    const groups: Array<{ title: string; columns: BaseTableColumn[] }> = []
    
    processedColumns.value.forEach(column => {
      if (column.children && column.children.length > 0) {
        groups.push({
          title: column.title,
          columns: column.children
        })
      }
    })
    
    return groups
  })

  // Fixed columns
  const fixedLeftColumns = computed(() => {
    return visibleColumns.value.filter(col => col.fixed === 'left')
  })

  const fixedRightColumns = computed(() => {
    return visibleColumns.value.filter(col => col.fixed === 'right')
  })

  const scrollableColumns = computed(() => {
    return visibleColumns.value.filter(col => !col.fixed)
  })

  return {
    // State
    columnWidths,
    hiddenColumns,
    columnOrder,
    
    // Computed
    processedColumns,
    visibleColumns,
    orderedColumns,
    totalWidth,
    columnGroups,
    fixedLeftColumns,
    fixedRightColumns,
    scrollableColumns,
    
    // Column management
    setColumnWidth,
    resetColumnWidth,
    resetAllColumnWidths,
    hideColumn,
    showColumn,
    toggleColumnVisibility,
    showAllColumns,
    hideAllColumns,
    
    // Column utilities
    getColumnByKey,
    getColumnIndex,
    isColumnVisible,
    getColumnWidth,
    
    // Column ordering
    reorderColumns,
    moveColumn
  }
}
