import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/vue3'
import { h } from 'vue'
import { Tag, Progress, Button, Space, Tooltip } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue'
import BaseTable from './BaseTable.vue'
import TableToolbar from './components/TableToolbar.vue'
import ExpandableRowContent from './components/ExpandableRowContent.vue'
import type { BaseTableColumn } from './types/TableTypes'

// Sample data generator
const generateSampleData = (count: number) => {
  const categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports']
  const statuses = ['active', 'inactive', 'pending']

  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    productName: `Product ${index + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    price: Math.floor(Math.random() * 1000) + 10,
    stock: Math.floor(Math.random() * 100),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    rating: Math.floor(Math.random() * 5) + 1,
    sales: Math.floor(Math.random() * 1000),
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    description: `This is a detailed description for Product ${index + 1}`,
    tags: ['tag1', 'tag2', 'tag3'].slice(0, Math.floor(Math.random() * 3) + 1)
  }))
}

const sampleData = generateSampleData(20)

// Basic columns
const basicColumns: BaseTableColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 60,
    sortable: true
  },
  {
    title: 'Product Name',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
    ellipsis: true,
    sortable: true
  },
  {
    title: 'Category',
    dataIndex: 'category',
    key: 'category',
    width: 120,
    filters: [
      { text: 'Electronics', value: 'Electronics' },
      { text: 'Clothing', value: 'Clothing' },
      { text: 'Books', value: 'Books' }
    ]
  },
  {
    title: 'Price',
    dataIndex: 'price',
    key: 'price',
    width: 100,
    align: 'right',
    sortable: true,
    customRender: ({ text }) => `$${text.toLocaleString()}`
  }
]

// Advanced columns with custom rendering
const advancedColumns: BaseTableColumn[] = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 60,
    fixed: 'left',
    sortable: true
  },
  {
    title: 'Product Info',
    key: 'productInfo',
    children: [
      {
        title: 'Name',
        dataIndex: 'productName',
        key: 'productName',
        width: 200,
        ellipsis: true,
        sortable: true,
        customRender: ({ text, record }) => h('div', [
          h('div', { style: { fontWeight: 'bold' } }, text),
          h('div', { style: { fontSize: '12px', color: '#666' } }, record.category)
        ])
      },
      {
        title: 'Category',
        dataIndex: 'category',
        key: 'category',
        width: 120
      }
    ]
  },
  {
    title: 'Price',
    dataIndex: 'price',
    key: 'price',
    width: 100,
    align: 'right',
    sortable: true,
    customRender: ({ text }) => `$${text.toLocaleString()}`
  },
  {
    title: 'Stock',
    dataIndex: 'stock',
    key: 'stock',
    width: 120,
    align: 'center',
    sortable: true,
    customRender: ({ text }) => h(Progress, {
      percent: Math.min(text, 100),
      size: 'small',
      status: text < 20 ? 'exception' : text < 50 ? 'active' : 'success',
      format: () => text
    })
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const colors = { active: 'green', inactive: 'red', pending: 'orange' }
      return h(Tag, { color: colors[text as keyof typeof colors] }, text.toUpperCase())
    }
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 150,
    fixed: 'right',
    customRender: () => h(Space, [
      h(Tooltip, { title: 'Edit' }, () =>
        h(Button, { type: 'text', icon: h(EditOutlined) })
      ),
      h(Tooltip, { title: 'View' }, () =>
        h(Button, { type: 'text', icon: h(EyeOutlined) })
      ),
      h(Tooltip, { title: 'Delete' }, () =>
        h(Button, { type: 'text', danger: true, icon: h(DeleteOutlined) })
      )
    ])
  }
]

const meta: Meta<typeof BaseTable> = {
  title: 'Components/BaseTable',
  component: BaseTable,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive table component built on top of Ant Design Vue with advanced features like sorting, filtering, row selection, expandable rows, and more.'
      }
    }
  },
  argTypes: {
    columns: {
      description: 'Table columns configuration',
      control: { type: 'object' }
    },
    dataSource: {
      description: 'Table data source',
      control: { type: 'object' }
    },
    config: {
      description: 'Table configuration options',
      control: { type: 'object' }
    }
  }
}

export default meta
type Story = StoryObj<typeof BaseTable>

// Basic table story
export const Basic: Story = {
  args: {
    columns: basicColumns,
    dataSource: sampleData.slice(0, 10),
    config: {
      size: 'middle',
      bordered: false,
      pagination: {
        pageSize: 5,
        showSizeChanger: true
      }
    }
  }
}

// Bordered table story
export const Bordered: Story = {
  args: {
    columns: basicColumns,
    dataSource: sampleData.slice(0, 10),
    config: {
      size: 'middle',
      bordered: true,
      striped: true
    }
  }
}

// Compact table story
export const Compact: Story = {
  args: {
    columns: basicColumns,
    dataSource: sampleData.slice(0, 15),
    config: {
      size: 'small',
      bordered: true,
      striped: true,
      pagination: {
        pageSize: 8,
        // size: 'small'
      }
    }
  }
}

// Table with row selection
export const WithRowSelection: Story = {
  args: {
    columns: basicColumns,
    dataSource: sampleData.slice(0, 10),
    config: {
      size: 'middle',
      rowSelection: {
        type: 'checkbox',
        showSelectAll: true
      },
      pagination: {
        pageSize: 5
      }
    }
  }
}

// Advanced table with all features
export const Advanced: Story = {
  args: {
    columns: advancedColumns,
    dataSource: sampleData,
    config: {
      size: 'middle',
      bordered: true,
      striped: true,
      hoverable: true,
      stickyHeader: true,
      rowSelection: {
        type: 'checkbox',
        showSelectAll: true,
        fixed: true
      },
      pagination: {
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      },
      scroll: {
        x: 1200,
        y: 400
      },
      expandable: {
        expandRowByClick: true,
        expandedRowRender: (record) => h(ExpandableRowContent, {
          record,
          showChart: true,
          showDetails: true,
          detailsTitle: 'Product Details',
          detailFields: [
            { key: 'description', label: 'Description', span: 2 },
            { key: 'createdAt', label: 'Created Date', type: 'date' },
            { key: 'sales', label: 'Total Sales', type: 'number' },
            { key: 'rating', label: 'Rating' }
          ]
        })
      }
    }
  },
  render: (args) => ({
    components: { BaseTable, TableToolbar },
    setup() {
      return { args }
    },
    template: `
      <div style="padding: 24px;">
        <BaseTable v-bind="args">
          <template #toolbar="{ state, columns }">
            <TableToolbar
              :state="state"
              :columns="columns"
              @refresh="() => console.log('Refresh clicked')"
              @export="(type) => console.log('Export:', type)"
            />
          </template>
        </BaseTable>
      </div>
    `
  })
}

// Loading state
export const Loading: Story = {
  args: {
    columns: basicColumns,
    dataSource: [],
    config: {
      loading: true,
      pagination: false
    }
  }
}

// Empty state
export const Empty: Story = {
  args: {
    columns: basicColumns,
    dataSource: [],
    config: {
      empty: {
        description: 'No products found'
      },
      pagination: false
    }
  }
}

// Fixed columns
export const FixedColumns: Story = {
  args: {
    columns: [
      { ...basicColumns[0], fixed: 'left' },
      ...basicColumns.slice(1, -1),
      {
        title: 'Actions',
        key: 'actions',
        width: 120,
        fixed: 'right',
        customRender: () => h(Button, { size: 'small' }, 'Action')
      }
    ],
    dataSource: sampleData.slice(0, 10),
    config: {
      scroll: { x: 800 },
      pagination: { pageSize: 5 }
    }
  }
}

// Virtual scrolling (for large datasets)
export const VirtualScrolling: Story = {
  args: {
    columns: basicColumns,
    dataSource: generateSampleData(1000),
    config: {
      virtual: true,
      scroll: { y: 400 },
      pagination: false
    }
  }
}
