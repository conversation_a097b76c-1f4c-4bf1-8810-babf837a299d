<template>
  <div
    ref="tableContainerRef"
    class="base-table-container"
    :class="[
      `base-table--${config?.size || 'middle'}`,
      {
        'base-table--bordered': config?.bordered,
        'base-table--striped': config?.striped,
        'base-table--hoverable': config?.hoverable !== false,
        'base-table--loading': config?.loading,
        'base-table--sticky-header': config?.stickyHeader
      },
      className
    ]"
    :style="[
      tableContainerStyle,
      style
    ]"
  >
    <!-- Error Display -->
    <Alert
      v-if="error"
      :message="error"
      type="error"
      show-icon
      closable
      style="margin-bottom: 16px"
      @close="error = null"
    />

    <!-- Table Toolbar (optional) -->
    <div v-if="$slots.toolbar" class="base-table__toolbar">
      <slot name="toolbar" :state="tableState" :columns="tableColumns" />
    </div>

    <!-- Main Table -->
    <Table
      ref="antTableRef"
      :columns="antdColumns"
      :data-source="tableState.processedData.value"
      :row-key="getRowKey"
      :loading="config?.loading || tableState.loading.value"
      :size="config?.size"
      :bordered="config?.bordered"
      :show-header="config?.showHeader !== false"
      :scroll="scrollConfig"
      :pagination="paginationConfig"
      :row-selection="rowSelectionConfig"
      :expandable="expandableConfig"
      :row-class-name="getRowClassName"
      :custom-row="getCustomRowProps as any"
      class="base-table__main"
      @change="handleTableChange"
      @expand="handleExpand"
      @expandedRowsChange="handleExpandedRowsChange"
    >
      <!-- Custom empty state -->
      <template #emptyText>
        <div class="base-table__empty">
          <slot name="empty" :config="config?.empty">
            <Empty
              :description="config?.empty?.description || 'No data'"
              :image="config?.empty?.image"
            />
          </slot>
        </div>
      </template>

      <!-- Expandable row content -->
      <template v-if="config?.expandable?.expandedRowRender" #expandedRowRender="{ record, index }">
        <div class="base-table__expanded-content">
          <component
            :is="config.expandable.expandedRowRender"
            :record="record"
            :index="index"
          />
        </div>
      </template>

      <!-- Custom expand icon -->
      <template v-if="config?.expandable?.expandIcon" #expandIcon="props">
        <component :is="config.expandable.expandIcon as any" v-bind="props" />
      </template>
    </Table>

    <!-- Table Footer (optional) -->
    <div v-if="$slots.footer" class="base-table__footer">
      <slot name="footer" :state="tableState" :columns="tableColumns" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Table, Alert, Empty } from 'ant-design-vue'
import type {
  BaseTableProps,
  BaseTableColumn,
  SortConfig,
  FilterConfig
} from './types/TableTypes'
import { useTableState } from './composables/useTableState'
import { useTableColumns } from './composables/useTableColumns'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'

// Props
const props = withDefaults(defineProps<BaseTableProps>(), {
  rowKey: 'id',
  config: () => ({}),
  events: () => ({}),
  className: '',
  style: () => ({})
})

// Emits
const emit = defineEmits<{
  sortChange: [sort: SortConfig]
  filterChange: [filters: FilterConfig[]]
  selectionChange: [selectedRowKeys: (string | number)[], selectedRows: any[]]
  expandChange: [expandedRowKeys: (string | number)[]]
  rowClick: [record: any, index: number, event: Event]
  rowDoubleClick: [record: any, index: number, event: Event]
  columnResize: [column: BaseTableColumn, width: number]
}>()

// Refs
const tableContainerRef = ref<HTMLDivElement>()
const antTableRef = ref()

// Error state
const error = ref<string | null>(null)

// Composables
const tableState = useTableState(
  computed(() => props.dataSource),
  computed(() => props.columns)
)

const tableColumns = useTableColumns(
  computed(() => props.columns),
  {
    resizable: true
  }
)

// Apply hover effects
useTableHoverEffects(tableContainerRef)

// Computed properties
const tableContainerStyle = computed(() => {
  const style: Record<string, any> = {}

  if (props.config?.height) {
    style.height = typeof props.config.height === 'number'
      ? `${props.config.height}px`
      : props.config.height
  }

  if (props.config?.maxHeight) {
    style.maxHeight = typeof props.config.maxHeight === 'number'
      ? `${props.config.maxHeight}px`
      : props.config.maxHeight
  }

  return style
})

const scrollConfig = computed(() => {
  const scroll = props.config?.scroll || {}

  return {
    x: scroll.x || tableColumns.totalWidth.value,
    y: scroll.y
  }
})

const paginationConfig = computed<any>(() => {
  if (props.config?.pagination === false) {
    return false
  }

  return {
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} of ${total} items`,
    ...props.config?.pagination
  }
})

const rowSelectionConfig = computed<any>(() => {
  if (!props.config?.rowSelection) {
    return undefined
  }

  return {
    type: 'checkbox',
    selectedRowKeys: tableState.selectedRowKeys.value,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: any[]) => {
      tableState.selectedRowKeys.value = selectedRowKeys
      emit('selectionChange', selectedRowKeys, selectedRows)
      props.config?.rowSelection?.onChange?.(selectedRowKeys, selectedRows)
    },
    onSelect: (record: any, selected: boolean, selectedRows: any[], nativeEvent: Event) => {
      tableState.handleRowSelect(record, selected)
      props.config?.rowSelection?.onSelect?.(record, selected, selectedRows, nativeEvent)
    },
    onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => {
      tableState.handleSelectAll(selected)
      props.config?.rowSelection?.onSelectAll?.(selected, selectedRows, changeRows)
    },
    ...props.config.rowSelection
  }
})

const expandableConfig = computed(() => {
  if (!props.config?.expandable) {
    return undefined
  }

  return {
    expandedRowKeys: tableState.expandedRowKeys.value,
    onExpand: (expanded: boolean, record: any) => {
      tableState.handleRowExpand(record, expanded)
      emit('expandChange', tableState.expandedRowKeys.value)
      props.config?.expandable?.onExpand?.(expanded, record)
    },
    onExpandedRowsChange: (expandedRows: (string | number)[]) => {
      tableState.expandedRowKeys.value = expandedRows
      emit('expandChange', expandedRows)
      props.config?.expandable?.onExpandedRowsChange?.(expandedRows)
    },
    ...props.config.expandable
  }
})

// Convert columns to Ant Design format
const antdColumns = computed(() => {
  return tableColumns.orderedColumns.value.map(column => ({
    ...column,
    sorter: column.sortable ? (column.sorter || true) : false,
    filters: column.filters,
    onFilter: column.onFilter,
    customRender: column.customRender ? ({ text, record, index }: any) => {
      return column.customRender!({ text, record, index, column })
    } : undefined,
    customHeaderRender: column.customHeaderRender ? ({ title }: any) => {
      return column.customHeaderRender!({ title, column })
    } : undefined
  }))
})

// Row utilities
const getRowKey = (record: any, index?: number): string | number => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(record)
  }
  return record[props.rowKey] || index || 0
}

const getRowClassName = (record: any, index: number): string => {
  const classes: string[] = []

  if (tableState.isRowSelected(record)) {
    classes.push('base-table__row--selected')
  }

  if (tableState.isRowExpanded(record)) {
    classes.push('base-table__row--expanded')
  }

  if (props.config?.striped && index % 2 === 1) {
    classes.push('base-table__row--striped')
  }

  return classes.join(' ')
}

const getCustomRowProps = (record: any, index: number) => {
  return {
    onClick: (event: Event) => {
      emit('rowClick', record, index, event)
      props.events?.onRowClick?.(record, index, event)
    },
    onDblclick: (event: Event) => {
      emit('rowDoubleClick', record, index, event)
      props.events?.onRowDoubleClick?.(record, index, event)
    },
    onContextmenu: (event: Event) => {
      props.events?.onRowContextMenu?.(record, index, event)
    },
    onMouseenter: (event: Event) => {
      props.events?.onRowMouseEnter?.(record, index, event)
    },
    onMouseleave: (event: Event) => {
      props.events?.onRowMouseLeave?.(record, index, event)
    }
  }
}

// Event handlers
const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  // Handle sorting
  if (sorter && sorter.field) {
    const sortConfig: SortConfig = {
      field: sorter.field,
      order: sorter.order
    }
    tableState.handleSort(sortConfig.field ?? '', sortConfig.order)
    emit('sortChange', sortConfig)
    props.events?.onSortChange?.(sortConfig)
  }

  // Handle filtering
  const filterConfigs: FilterConfig[] = Object.entries(filters)
    .filter(([_, value]) => value && (value as any[]).length > 0)
    .map(([field, value]) => ({
      field,
      value: (value as any[])[0],
      operator: 'eq' as const
    }))

  tableState.filterConfigs.value = filterConfigs
  emit('filterChange', filterConfigs)
  props.events?.onFilterChange?.(filterConfigs)
}

const handleExpand = (expanded: boolean, record: any) => {
  tableState.handleRowExpand(record, expanded)
  emit('expandChange', tableState.expandedRowKeys.value)
}

const handleExpandedRowsChange = (expandedRows: (string | number)[]) => {
  tableState.expandedRowKeys.value = expandedRows
  emit('expandChange', expandedRows)
}

// Expose methods for parent components
defineExpose({
  tableState,
  tableColumns,
  clearSelection: tableState.clearSelection,
  clearFilters: tableState.clearFilters,
  clearSort: tableState.clearSort,
  expandAll: tableState.expandAll,
  collapseAll: tableState.collapseAll,
  refresh: () => {
    // Trigger data refresh if needed
  }
})
</script>

<style scoped>
.base-table-container {
  position: relative;
  width: 100%;
}

.base-table__toolbar {
  margin-bottom: 16px;
}

.base-table__footer {
  margin-top: 16px;
}

.base-table__empty {
  padding: 32px 16px;
}

.base-table__expanded-content {
  padding: 16px;
  background-color: #fafafa;
}

/* Size variants */
.base-table--small .base-table__main {
  font-size: 12px;
}

.base-table--large .base-table__main {
  font-size: 16px;
}

/* Striped rows */
.base-table--striped :deep(.base-table__row--striped) {
  background-color: #fafafa;
}

/* Selected rows */
.base-table--hoverable :deep(.base-table__row--selected) {
  background-color: #e6f7ff;
}

/* Loading state */
.base-table--loading {
  pointer-events: none;
}

/* Sticky header */
.base-table--sticky-header :deep(.ant-table-thead th) {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
