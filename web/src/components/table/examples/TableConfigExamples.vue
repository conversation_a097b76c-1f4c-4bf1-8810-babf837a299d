<template>
  <div class="table-config-examples">
    <h1>BaseTable Configuration Examples</h1>
    
    <!-- Example 1: Basic Data Table -->
    <section class="example-section">
      <h2>1. Basic Data Table</h2>
      <BaseTable
        :columns="basicColumns"
        :data-source="sampleData"
        :config="basicTableConfig"
        row-key="id"
      />
    </section>

    <!-- Example 2: Expandable Table -->
    <section class="example-section">
      <h2>2. Expandable Table (No Expand Column)</h2>
      <BaseTable
        :columns="expandableColumns"
        :data-source="sampleData"
        :config="expandableTableConfig"
        :events="expandableEvents"
        row-key="id"
        class="expandable-table"
      />
    </section>

    <!-- Example 3: Selectable Table -->
    <section class="example-section">
      <h2>3. Selectable Table</h2>
      <BaseTable
        :columns="basicColumns"
        :data-source="sampleData"
        :config="selectableTableConfig"
        row-key="id"
      />
    </section>

    <!-- Example 4: Compact Table -->
    <section class="example-section">
      <h2>4. Compact Dashboard Table</h2>
      <BaseTable
        :columns="compactColumns"
        :data-source="sampleData.slice(0, 5)"
        :config="compactTableConfig"
        row-key="id"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { Tag, Button } from 'ant-design-vue'
import BaseTable from '@/components/table/BaseTable.vue'
import {
  createDefaultTableConfig,
  createExpandableTableConfig,
  createSelectableTableConfig,
  createCompactTableConfig
} from '@/components/table/configs/DefaultTableConfig'
import type { BaseTableColumn, TableEvents } from '@/components/table/types/TableTypes'

// Sample data
const sampleData = ref([
  {
    id: '1',
    name: 'Product A',
    category: 'Electronics',
    price: 299.99,
    stock: 150,
    status: 'active'
  },
  {
    id: '2',
    name: 'Product B',
    category: 'Clothing',
    price: 49.99,
    stock: 75,
    status: 'inactive'
  },
  {
    id: '3',
    name: 'Product C',
    category: 'Books',
    price: 19.99,
    stock: 200,
    status: 'active'
  }
])

// Expanded rows state
const expandedRowKeys = ref<(string | number)[]>([])

// Basic columns
const basicColumns: BaseTableColumn[] = [
  {
    title: 'Name',
    key: 'name',
    dataIndex: 'name',
    width: 200
  },
  {
    title: 'Category',
    key: 'category',
    dataIndex: 'category',
    width: 150
  },
  {
    title: 'Price',
    key: 'price',
    dataIndex: 'price',
    width: 100,
    align: 'right',
    customRender: ({ text }) => `$${text}`
  },
  {
    title: 'Stock',
    key: 'stock',
    dataIndex: 'stock',
    width: 100,
    align: 'right'
  },
  {
    title: 'Status',
    key: 'status',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => 
      h(Tag, { color: text === 'active' ? 'green' : 'red' }, () => text)
  }
]

// Expandable columns (with expand indicator)
const expandableColumns: BaseTableColumn[] = [
  {
    title: 'Name',
    key: 'name',
    dataIndex: 'name',
    width: 200,
    customRender: ({ record }) => 
      h('div', { 
        style: { 
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, [
        h('span', { 
          style: { 
            transform: expandedRowKeys.value.includes(record.id) ? 'rotate(90deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s'
          }
        }, '▶'),
        record.name
      ])
  },
  ...basicColumns.slice(1)
]

// Compact columns
const compactColumns: BaseTableColumn[] = [
  {
    title: 'Name',
    key: 'name',
    dataIndex: 'name'
  },
  {
    title: 'Price',
    key: 'price',
    dataIndex: 'price',
    align: 'right',
    customRender: ({ text }) => `$${text}`
  },
  {
    title: 'Status',
    key: 'status',
    dataIndex: 'status',
    customRender: ({ text }) => 
      h(Tag, { 
        color: text === 'active' ? 'green' : 'red',
        size: 'small'
      }, () => text)
  }
]

// Table configurations
const basicTableConfig = createDefaultTableConfig({
  pagination: {
    pageSize: 10,
    showTotal: (total, range) => `Showing ${range[0]}-${range[1]} of ${total} items`
  }
})

const expandableTableConfig = createExpandableTableConfig(
  (record) => h('div', { 
    style: { 
      padding: '16px',
      background: '#f5f5f5',
      borderRadius: '4px'
    }
  }, [
    h('h4', `Details for ${record.name}`),
    h('p', `Category: ${record.category}`),
    h('p', `Price: $${record.price}`),
    h('p', `Stock: ${record.stock} units`)
  ]),
  {
    pagination: { pageSize: 5 }
  }
)

const selectableTableConfig = createSelectableTableConfig(
  (selectedKeys, selectedRows) => {
    console.log('Selected:', selectedKeys, selectedRows)
  },
  {
    pagination: { pageSize: 5 }
  }
)

const compactTableConfig = createCompactTableConfig({
  pagination: false
})

// Events for expandable table
const expandableEvents: TableEvents = {
  onRowClick: (record) => {
    const isExpanded = expandedRowKeys.value.includes(record.id)
    if (isExpanded) {
      expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== record.id)
    } else {
      expandedRowKeys.value = [...expandedRowKeys.value, record.id]
    }
  }
}
</script>

<style scoped>
.table-config-examples {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 48px;
}

.example-section h2 {
  margin-bottom: 16px;
  color: #262626;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

/* Hide expand column for expandable table example */
.expandable-table :deep(.ant-table-row-expand-icon-cell),
.expandable-table :deep(.ant-table-expand-icon-col),
.expandable-table :deep(.ant-table-row-expand-icon) {
  display: none !important;
  width: 0 !important;
  padding: 0 !important;
}

.expandable-table :deep(.ant-table-tbody > tr) {
  cursor: pointer;
}

.expandable-table :deep(.ant-table-tbody > tr:hover) {
  background-color: #f5f5f5;
}
</style>
