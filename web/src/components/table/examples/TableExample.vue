<template>
  <div class="table-example">
    <h2>Advanced Table Component Demo</h2>

    <!-- Table with all features -->
    <BaseTable
      ref="tableRef"
      :columns="columns"
      :data-source="dataSource"
      :config="tableConfig"
      :events="tableEvents"
      class="demo-table"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange"
      @selection-change="handleSelectionChange"
      @expand-change="handleExpandChange"
      @row-click="handleRowClick"
    >
      <!-- Toolbar -->
      <template #toolbar="{ state, columns }">
        <TableToolbar
          :state="state"
          :columns="columns"
          :current-density="density"
          @refresh="handleRefresh"
          @density-change="handleDensityChange"
          @export="handleExport"
          @fullscreen-change="handleFullscreenChange"
        >
          <template #left>
            <Space>
              <span v-if="state.selectedRowKeys.value.length > 0" class="selection-info">
                {{ state.selectedRowKeys.value.length }} items selected
              </span>
              <span v-else class="total-info">
                Total: {{ state.processedData.value.length }} items
              </span>

              <Button
                v-if="state.selectedRowKeys.value.length > 0"
                type="primary"
                danger
                size="small"
                @click="handleBulkDelete"
              >
                Delete Selected
              </Button>
            </Space>
          </template>

          <template #center>
            <Input
              v-model:value="searchText"
              placeholder="Search products..."
              style="width: 300px"
              @change="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </Input>
          </template>
        </TableToolbar>
      </template>

      <!-- Footer -->
      <template #footer="{ state }">
        <div class="table-footer">
          <Space>
            <span>Showing {{ state.processedData.value.length }} of {{ totalRecords }} items</span>
            <Button type="link" @click="loadMore">Load More</Button>
          </Space>
        </div>
      </template>
    </BaseTable>

    <!-- Action Modals -->
    <Modal
      v-model:open="editModalVisible"
      title="Edit Product"
      @ok="handleEditSave"
      @cancel="editModalVisible = false"
    >
      <Form :model="editForm" layout="vertical">
        <FormItem label="Product Name">
          <Input v-model:value="editForm.productName" />
        </FormItem>
        <FormItem label="Price">
          <InputNumber v-model:value="editForm.price" style="width: 100%" />
        </FormItem>
        <FormItem label="Stock">
          <InputNumber v-model:value="editForm.stock" style="width: 100%" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  Space,
  Button,
  Input,
  Modal,
  Form,
  FormItem,
  InputNumber,
  Tag,
  Progress,
  Tooltip,
  message
} from 'ant-design-vue'
import {
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
} from '@ant-design/icons-vue'
import BaseTable from '../BaseTable.vue'
import TableToolbar from '../components/TableToolbar.vue'
import ExpandableRowContent from '../components/ExpandableRowContent.vue'
import type { BaseTableColumn, TableConfig } from '@/components/table/types/TableTypes.ts'

// Sample data
const generateSampleData = (count: number) => {
  const categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports']
  const statuses = ['active', 'inactive', 'pending']

  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    productName: `Product ${index + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    price: Math.floor(Math.random() * 1000) + 10,
    stock: Math.floor(Math.random() * 100),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    rating: Math.floor(Math.random() * 5) + 1,
    sales: Math.floor(Math.random() * 1000),
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    description: `This is a detailed description for Product ${index + 1}`,
    tags: ['tag1', 'tag2', 'tag3'].slice(0, Math.floor(Math.random() * 3) + 1)
  }))
}

// State
const tableRef = ref()
const dataSource = ref(generateSampleData(50))
const totalRecords = ref(500)
const searchText = ref('')
const density = ref<'small' | 'middle' | 'large'>('middle')
const editModalVisible = ref(false)
const editForm = ref({
  id: null,
  productName: '',
  price: 0,
  stock: 0
})

// Table configuration
const tableConfig = computed<TableConfig>(() => ({
  size: density.value,
  bordered: true,
  striped: true,
  hoverable: true,
  loading: false,
  stickyHeader: true,
  rowSelection: {
    type: 'checkbox',
    showSelectAll: true,
    fixed: true
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: totalRecords.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  },
  scroll: {
    x: 1200,
    y: 400
  },
  expandable: {
    expandRowByClick: true,
    expandedRowRender: (record) => h(ExpandableRowContent, {
      record,
      showChart: true,
      showDetails: true,
      showNestedTable: true,
      chartTitle: 'Sales Trend',
      detailsTitle: 'Product Details',
      nestedTableTitle: 'Related Products',
      detailFields: [
        { key: 'description', label: 'Description', span: 2 },
        { key: 'createdAt', label: 'Created Date', type: 'date' },
        { key: 'sales', label: 'Total Sales', type: 'number' },
        { key: 'rating', label: 'Rating' },
        { key: 'tags', label: 'Tags' }
      ],
      showEditButton: true,
      showViewButton: true,
      onEdit: (record) => handleEdit(record),
      onView: (record) => handleView(record)
    })
  }
}))

// Table columns
const columns = computed<BaseTableColumn[]>(() => [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 60,
    fixed: 'left',
    sortable: true
  },
  {
    title: 'Product Info',
    key: 'productInfo',
    children: [
      {
        title: 'Name',
        dataIndex: 'productName',
        key: 'productName',
        width: 200,
        ellipsis: true,
        sortable: true,
        filterable: true,
        customRender: ({ text, record }) => h('div', [
          h('div', { style: { fontWeight: 'bold' } }, text),
          h('div', { style: { fontSize: '12px', color: '#666' } }, record.category)
        ])
      },
      {
        title: 'Category',
        dataIndex: 'category',
        key: 'category',
        width: 120,
        filters: [
          { text: 'Electronics', value: 'Electronics' },
          { text: 'Clothing', value: 'Clothing' },
          { text: 'Books', value: 'Books' },
          { text: 'Home & Garden', value: 'Home & Garden' },
          { text: 'Sports', value: 'Sports' }
        ],
        onFilter: (value, record) => record.category === value
      }
    ]
  },
  {
    title: 'Price',
    dataIndex: 'price',
    key: 'price',
    width: 100,
    align: 'right',
    sortable: true,
    customRender: ({ text }) => `$${text.toLocaleString()}`
  },
  {
    title: 'Stock',
    dataIndex: 'stock',
    key: 'stock',
    width: 120,
    align: 'center',
    sortable: true,
    customRender: ({ text }) => h(Progress, {
      percent: Math.min(text, 100),
      size: 'small',
      status: text < 20 ? 'exception' : text < 50 ? 'active' : 'success',
      format: () => text
    })
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center',
    filters: [
      { text: 'Active', value: 'active' },
      { text: 'Inactive', value: 'inactive' },
      { text: 'Pending', value: 'pending' }
    ],
    onFilter: (value, record) => record.status === value,
    customRender: ({ text }) => {
      const colors = {
        active: 'green',
        inactive: 'red',
        pending: 'orange'
      }
      return h(Tag, { color: colors[text as keyof typeof colors] }, text.toUpperCase())
    }
  },
  {
    title: 'Rating',
    dataIndex: 'rating',
    key: 'rating',
    width: 120,
    align: 'center',
    sortable: true,
    customRender: ({ text }) => '★'.repeat(text) + '☆'.repeat(5 - text)
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => h(Space, [
      h(Tooltip, { title: 'Edit' }, () =>
        h(Button, {
          type: 'text',
          icon: h(EditOutlined),
          onClick: () => handleEdit(record)
        })
      ),
      h(Tooltip, { title: 'View' }, () =>
        h(Button, {
          type: 'text',
          icon: h(EyeOutlined),
          onClick: () => handleView(record)
        })
      ),
      h(Tooltip, { title: 'Delete' }, () =>
        h(Button, {
          type: 'text',
          danger: true,
          icon: h(DeleteOutlined),
          onClick: () => handleDelete(record)
        })
      )
    ])
  }
])

// Table events
const tableEvents = {
  onRowClick: (record: any, index: number, event: Event) => {
    console.log('Row clicked:', record, index)
  },
  onRowDoubleClick: (record: any, index: number, event: Event) => {
    handleEdit(record)
  }
}

// Event handlers
const handleSortChange = (sort: any) => {
  console.log('Sort changed:', sort)
}

const handleFilterChange = (filters: any) => {
  console.log('Filters changed:', filters)
}

const handleSelectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
  console.log('Selection changed:', selectedRowKeys, selectedRows)
}

const handleExpandChange = (expandedRowKeys: any[]) => {
  console.log('Expand changed:', expandedRowKeys)
}

const handleRowClick = (record: any, index: number, event: Event) => {
  console.log('Row clicked:', record, index)
}

const handleRefresh = () => {
  message.success('Table refreshed')
  dataSource.value = generateSampleData(50)
}

const handleDensityChange = (newDensity: 'small' | 'middle' | 'large') => {
  density.value = newDensity
  message.success(`Table density changed to ${newDensity}`)
}

const handleExport = (type: 'csv' | 'excel' | 'pdf') => {
  message.success(`Exporting as ${type.toUpperCase()}`)
}

const handleFullscreenChange = (fullscreen: boolean) => {
  message.info(fullscreen ? 'Entered fullscreen' : 'Exited fullscreen')
}

const handleSearch = () => {
  // Implement search logic
  console.log('Searching for:', searchText.value)
}

const handleBulkDelete = () => {
  message.success('Bulk delete action')
}

const handleEdit = (record: any) => {
  editForm.value = { ...record }
  editModalVisible.value = true
}

const handleView = (record: any) => {
  message.info(`Viewing product: ${record.productName}`)
}

const handleDelete = (record: any) => {
  message.success(`Deleted product: ${record.productName}`)
}

const handleEditSave = () => {
  message.success('Product updated successfully')
  editModalVisible.value = false
}

const loadMore = () => {
  const newData = generateSampleData(20)
  dataSource.value.push(...newData)
  message.success('Loaded 20 more items')
}
</script>

<style scoped>
.table-example {
  padding: 24px;
}

.demo-table {
  margin-top: 16px;
}

.selection-info {
  color: #1890ff;
  font-weight: 500;
}

.total-info {
  color: #666;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}
</style>
