<template>
  <div class="table-toolbar">
    <div class="table-toolbar__left">
      <slot name="left" :state="state" :columns="columns">
        <!-- Default left content -->
        <div class="table-toolbar__info">
          <span v-if="state.selectedRowKeys.value.length > 0" class="table-toolbar__selection">
            {{ state.selectedRowKeys.value.length }} selected
          </span>
          <span v-else class="table-toolbar__total">
            Total: {{ state.processedData.value.length }} items
          </span>
        </div>
      </slot>
    </div>

    <div class="table-toolbar__center">
      <slot name="center" :state="state" :columns="columns" />
    </div>

    <div class="table-toolbar__right">
      <slot name="right" :state="state" :columns="columns">
        <!-- Default right content -->
        <Space>
          <!-- Refresh button -->
          <Tooltip title="Refresh">
            <Button
              type="text"
              :icon="h(ReloadOutlined)"
              @click="$emit('refresh')"
            />
          </Tooltip>

          <!-- Column settings -->
          <Dropdown v-if="showColumnSettings" placement="bottomRight">
            <Tooltip title="Column Settings">
              <Button type="text" :icon="h(SettingOutlined)" />
            </Tooltip>
            <template #overlay>
              <Menu class="table-toolbar__column-menu">
                <MenuItem
                  v-for="column in allColumns"
                  :key="column.key"
                  @click="columns.toggleColumnVisibility(column.key)"
                >
                  <Checkbox
                    :checked="columns.isColumnVisible(column.key)"
                    @click.stop
                    @change="columns.toggleColumnVisibility(column.key)"
                  >
                    {{ column.title }}
                  </Checkbox>
                </MenuItem>
                <MenuDivider />
                <MenuItem @click="columns.showAllColumns()">
                  Show All
                </MenuItem>
                <MenuItem @click="columns.hideAllColumns()">
                  Hide All
                </MenuItem>
                <MenuItem @click="columns.resetAllColumnWidths()">
                  Reset Widths
                </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, computed, ref } from 'vue'
import {
  Space,
  Button,
  Tooltip,
  Dropdown,
  Menu,
  MenuItem,
  MenuDivider,
  Checkbox
} from 'ant-design-vue'
import {
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'
import type { BaseTableColumn } from '../types/TableTypes'

interface Props {
  state: any
  columns: any
  showColumnSettings?: boolean
  showDensitySettings?: boolean
  showExport?: boolean
  showFullscreen?: boolean
  currentDensity?: 'small' | 'middle' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  showColumnSettings: true,
  showDensitySettings: true,
  showExport: true,
  showFullscreen: true,
  currentDensity: 'middle'
})

const emit = defineEmits<{
  refresh: []
  densityChange: [density: 'small' | 'middle' | 'large']
  export: [type: 'csv' | 'excel' | 'pdf']
  fullscreenChange: [fullscreen: boolean]
}>()

// Fullscreen state
const isFullscreen = ref(false)

// All columns (including hidden ones)
const allColumns = computed(() => {
  const flattenColumns = (cols: BaseTableColumn[]): BaseTableColumn[] => {
    return cols.reduce((acc, col) => {
      if (col.children && col.children.length > 0) {
        acc.push(...flattenColumns(col.children))
      } else {
        acc.push(col)
      }
      return acc
    }, [] as BaseTableColumn[])
  }

  return flattenColumns(props.columns.processedColumns.value)
})
</script>

<style scoped>
.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.table-toolbar__left,
.table-toolbar__center,
.table-toolbar__right {
  display: flex;
  align-items: center;
}

.table-toolbar__left {
  flex: 1;
}

.table-toolbar__center {
  flex: 0 0 auto;
}

.table-toolbar__right {
  flex: 0 0 auto;
}

.table-toolbar__info {
  color: #666;
  font-size: 14px;
}

.table-toolbar__selection {
  color: #1890ff;
  font-weight: 500;
}

.table-toolbar__total {
  color: #999;
}

.table-toolbar__column-menu {
  max-height: 300px;
  overflow-y: auto;
}

.table-toolbar__column-menu .ant-menu-item {
  padding: 8px 12px;
}

.table-toolbar__column-menu .ant-checkbox-wrapper {
  width: 100%;
}
</style>
