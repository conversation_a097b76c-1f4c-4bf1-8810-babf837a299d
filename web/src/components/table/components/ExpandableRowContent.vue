<template>
  <div class="expandable-row-content">
    <!-- Chart/Graph Section -->
    <div v-if="showChart" class="expandable-row-content__chart">
      <div class="chart-header">
        <h4>{{ chartTitle || 'Data Visualization' }}</h4>
        <Space>
          <Select
            v-if="chartTypes.length > 1"
            v-model:value="selectedChartType"
            size="small"
            style="width: 120px"
          >
            <SelectOption
              v-for="type in chartTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </SelectOption>
          </Select>
          <Button
            type="text"
            size="small"
            :icon="h(FullscreenOutlined)"
            @click="$emit('chartFullscreen', record)"
          />
        </Space>
      </div>

      <div class="chart-container">
        <slot name="chart" :record="record" :chartType="selectedChartType">
          <!-- Default chart placeholder -->
          <div class="chart-placeholder">
            <BarChartOutlined style="font-size: 48px; color: #d9d9d9;" />
            <p>Chart will be displayed here</p>
          </div>
        </slot>
      </div>
    </div>

    <!-- Details Section -->
    <div v-if="showDetails" class="expandable-row-content__details">
      <div class="details-header">
        <h4>{{ detailsTitle || 'Details' }}</h4>
        <Space>
          <Button
            v-if="showEditButton"
            type="primary"
            size="small"
            :icon="h(EditOutlined)"
            @click="$emit('edit', record)"
          >
            Edit
          </Button>
          <Button
            v-if="showViewButton"
            size="small"
            :icon="h(EyeOutlined)"
            @click="$emit('view', record)"
          >
            View
          </Button>
        </Space>
      </div>

      <div class="details-content">
        <slot name="details" :record="record">
          <!-- Default details table -->
          <Descriptions
            :column="detailsColumns"
            size="small"
            bordered
          >
            <DescriptionsItem
              v-for="field in detailFields"
              :key="field.key"
              :label="field.label"
              :span="field.span || 1"
            >
              <template v-if="field.render">
                <component :is="field.render" :value="record[field.key]" :record="record" />
              </template>
              <template v-else>
                {{ formatValue(record[field.key], field.type) }}
              </template>
            </DescriptionsItem>
          </Descriptions>
        </slot>
      </div>
    </div>

    <!-- Nested Table Section -->
    <div v-if="showNestedTable" class="expandable-row-content__nested-table">
      <div class="nested-table-header">
        <h4>{{ nestedTableTitle || 'Related Data' }}</h4>
        <Space>
          <Button
            v-if="showAddButton"
            type="primary"
            size="small"
            :icon="h(PlusOutlined)"
            @click="$emit('add', record)"
          >
            Add
          </Button>
          <Button
            type="text"
            size="small"
            :icon="h(ReloadOutlined)"
            @click="$emit('refreshNested', record)"
          />
        </Space>
      </div>

      <div class="nested-table-content">
        <slot name="nestedTable" :record="record">
          <!-- Default nested table -->
          <Table
            :columns="nestedTableColumns"
            :data-source="nestedTableData"
            :pagination="false"
            size="small"
            :scroll="{ x: 'max-content' }"
          />
        </slot>
      </div>
    </div>

    <!-- Custom Content Section -->
    <div v-if="$slots.custom" class="expandable-row-content__custom">
      <slot name="custom" :record="record" />
    </div>

    <!-- Actions Section -->
    <div v-if="showActions" class="expandable-row-content__actions">
      <slot name="actions" :record="record">
        <Space>
          <Button
            v-for="action in actions"
            :key="action.key"
            :type="action.type || 'default'"
            :danger="action.danger"
            :loading="action.loading"
            :disabled="action.disabled"
            :size="action.size || 'small'"
            :icon="action.icon ? h(action.icon) : undefined"
            @click="$emit('action', action.key, record)"
          >
            {{ action.label }}
          </Button>
        </Space>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref } from 'vue'
import {
  Space,
  Button,
  Select,
  SelectOption,
  Descriptions,
  DescriptionsItem,
  Table
} from 'ant-design-vue'
import {
  FullscreenOutlined,
  BarChartOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

interface ChartType {
  value: string
  label: string
}

interface DetailField {
  key: string
  label: string
  type?: 'text' | 'number' | 'date' | 'currency' | 'percentage'
  span?: number
  render?: any
}

interface Action {
  key: string
  label: string
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  danger?: boolean
  loading?: boolean
  disabled?: boolean
  size?: 'small' | 'middle' | 'large'
  icon?: any
}

interface Props {
  record: any

  // Chart section
  showChart?: boolean
  chartTitle?: string
  chartTypes?: ChartType[]
  defaultChartType?: string

  // Details section
  showDetails?: boolean
  detailsTitle?: string
  detailFields?: DetailField[]
  detailsColumns?: number

  // Nested table section
  showNestedTable?: boolean
  nestedTableTitle?: string
  nestedTableColumns?: any[]
  nestedTableData?: any[]

  // Actions
  showActions?: boolean
  actions?: Action[]
  showEditButton?: boolean
  showViewButton?: boolean
  showAddButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showChart: false,
  showDetails: true,
  showNestedTable: false,
  showActions: false,
  showEditButton: false,
  showViewButton: false,
  showAddButton: false,
  chartTypes: () => [
    { value: 'line', label: 'Line Chart' },
    { value: 'bar', label: 'Bar Chart' },
    { value: 'area', label: 'Area Chart' }
  ],
  defaultChartType: 'line',
  detailsColumns: 2,
  detailFields: () => [],
  nestedTableColumns: () => [],
  nestedTableData: () => [],
  actions: () => []
})

const emit = defineEmits<{
  chartFullscreen: [record: any]
  edit: [record: any]
  view: [record: any]
  add: [record: any]
  refreshNested: [record: any]
  action: [actionKey: string, record: any]
}>()

// Chart type selection
const selectedChartType = ref(props.defaultChartType)

// Format value based on type
const formatValue = (value: any, type?: string): string => {
  if (value === null || value === undefined) {
    return '-'
  }

  switch (type) {
    case 'number':
      return typeof value === 'number' ? value.toLocaleString() : String(value)
    case 'currency':
      return typeof value === 'number' ? `$${value.toLocaleString()}` : String(value)
    case 'percentage':
      return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : String(value)
    case 'date':
      return value instanceof Date ? value.toLocaleDateString() : String(value)
    default:
      return String(value)
  }
}
</script>

<style scoped>
.expandable-row-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.expandable-row-content__chart,
.expandable-row-content__details,
.expandable-row-content__nested-table,
.expandable-row-content__custom {
  margin-bottom: 16px;
}

.expandable-row-content__chart:last-child,
.expandable-row-content__details:last-child,
.expandable-row-content__nested-table:last-child,
.expandable-row-content__custom:last-child,
.expandable-row-content__actions:last-child {
  margin-bottom: 0;
}

.chart-header,
.details-header,
.nested-table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.chart-header h4,
.details-header h4,
.nested-table-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.chart-container {
  min-height: 200px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.details-content {
  background: white;
  border-radius: 4px;
  padding: 16px;
}

.nested-table-content {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.expandable-row-content__actions {
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}
</style>
