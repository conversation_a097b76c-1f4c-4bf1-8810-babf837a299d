/* Base Table Styles */
.base-table-container {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* Table Sizes */
.base-table--small {
  font-size: 12px;
}

.base-table--small .ant-table-thead > tr > th {
  padding: 8px 12px;
  font-size: 12px;
}

.base-table--small .ant-table-tbody > tr > td {
  padding: 6px 12px;
  font-size: 12px;
}

.base-table--middle {
  font-size: 14px;
}

.base-table--middle .ant-table-thead > tr > th {
  padding: 12px 16px;
  font-size: 14px;
}

.base-table--middle .ant-table-tbody > tr > td {
  padding: 10px 16px;
  font-size: 14px;
}

.base-table--large {
  font-size: 16px;
}

.base-table--large .ant-table-thead > tr > th {
  padding: 16px 20px;
  font-size: 16px;
}

.base-table--large .ant-table-tbody > tr > td {
  padding: 14px 20px;
  font-size: 16px;
}

/* Bordered Table */
.base-table--bordered .ant-table-container {
  border: 1px solid #f0f0f0;
}

.base-table--bordered .ant-table-thead > tr > th,
.base-table--bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

.base-table--bordered .ant-table-thead > tr > th:last-child,
.base-table--bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}

/* Striped Rows */
.base-table--striped .ant-table-tbody > tr:nth-child(even) {
  background-color: #fafafa;
}

.base-table--striped .ant-table-tbody > tr:nth-child(even):hover {
  background-color: #f5f5f5;
}

/* Hoverable Rows */
.base-table--hoverable .ant-table-tbody > tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* Selected Rows */
.base-table__row--selected {
  background-color: #e6f7ff !important;
}

.base-table__row--selected:hover {
  background-color: #bae7ff !important;
}

/* Expanded Rows */
.base-table__row--expanded {
  background-color: #f0f9ff;
}

/* Sticky Header */
.base-table--sticky-header .ant-table-thead > tr > th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fafafa;
}

.base-table--sticky-header .ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: -1;
}

/* Loading State */
.base-table--loading {
  pointer-events: none;
  opacity: 0.7;
}

.base-table--loading .ant-table-tbody {
  position: relative;
}

.base-table--loading .ant-table-tbody::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

/* Column Hover Effects */
.column-hovered {
  background-color: #f0f9ff !important;
  position: relative;
}

.column-hovered::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
}

/* Fixed Columns */
.ant-table-fixed-left,
.ant-table-fixed-right {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

.ant-table-fixed-left {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.1);
}

.ant-table-fixed-right {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.1);
}

/* Resizable Columns */
.ant-table-thead > tr > th.resizable {
  position: relative;
}

.ant-table-thead > tr > th.resizable::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: #f0f0f0;
  cursor: col-resize;
  user-select: none;
}

.ant-table-thead > tr > th.resizable:hover::after {
  background: #1890ff;
}

/* Sort Indicators */
.ant-table-column-sorter {
  margin-left: 4px;
}

.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
  color: #1890ff;
}

/* Filter Indicators */
.ant-table-filter-trigger.active {
  color: #1890ff;
}

/* Empty State */
.base-table__empty {
  padding: 48px 24px;
  text-align: center;
  color: #999;
}

.base-table__empty .ant-empty-description {
  color: #999;
  font-size: 14px;
}

/* Expanded Content */
.base-table__expanded-content {
  background: #fafafa;
  border-radius: 6px;
  margin: 8px 0;
}

/* Toolbar Styles */
.base-table__toolbar {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.base-table__footer {
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .base-table-container {
    overflow-x: auto;
  }
  
  .base-table--small .ant-table-thead > tr > th,
  .base-table--small .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .base-table--middle .ant-table-thead > tr > th,
  .base-table--middle .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .base-table--large .ant-table-thead > tr > th,
  .base-table--large .ant-table-tbody > tr > td {
    padding: 12px 16px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .base-table__toolbar {
    flex-direction: column;
    gap: 8px;
  }
  
  .table-toolbar__left,
  .table-toolbar__center,
  .table-toolbar__right {
    width: 100%;
    justify-content: center;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .base-table-container {
    background: #141414;
    color: #fff;
  }
  
  .base-table--striped .ant-table-tbody > tr:nth-child(even) {
    background-color: #1f1f1f;
  }
  
  .base-table--hoverable .ant-table-tbody > tr:hover {
    background-color: #262626;
  }
  
  .base-table__row--selected {
    background-color: #111b26 !important;
  }
  
  .base-table__expanded-content {
    background: #1f1f1f;
  }
  
  .column-hovered {
    background-color: #111b26 !important;
  }
}

/* Print Styles */
@media print {
  .base-table__toolbar,
  .base-table__footer,
  .ant-table-pagination {
    display: none !important;
  }
  
  .base-table-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .ant-table-thead > tr > th {
    background: #f5f5f5 !important;
    color: #000 !important;
  }
  
  .ant-table-tbody > tr > td {
    color: #000 !important;
  }
}

/* Accessibility */
.base-table-container:focus-within {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.ant-table-tbody > tr:focus {
  outline: 2px solid #1890ff;
  outline-offset: -2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .base-table-container {
    border: 2px solid #000;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    border: 1px solid #000;
  }
  
  .base-table__row--selected {
    background-color: #000 !important;
    color: #fff !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .base-table-container *,
  .base-table-container *::before,
  .base-table-container *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
