<template>
  <div class="sparkline-examples">
    <h3>SaleVolumeSparkLine Examples</h3>
    
    <div class="example-section">
      <h4>Default (Blue-Orange scheme)</h4>
      <div class="example-row">
        <span>Sample data:</span>
        <SaleVolumeSparkLine :data="sampleData1" />
      </div>
    </div>

    <div class="example-section">
      <h4>Different data patterns</h4>
      <div class="example-row">
        <span>Increasing trend:</span>
        <SaleVolumeSparkLine :data="increasingData" />
      </div>
      <div class="example-row">
        <span>Decreasing trend:</span>
        <SaleVolumeSparkLine :data="decreasingData" />
      </div>
      <div class="example-row">
        <span>Random pattern:</span>
        <SaleVolumeSparkLine :data="randomData" />
      </div>
    </div>

    <div class="example-section">
      <h4>Custom sizes</h4>
      <div class="example-row">
        <span>Small (100x16):</span>
        <SaleVolumeSparkLine :data="sampleData1" :width="100" :height="16" />
      </div>
      <div class="example-row">
        <span>Large (200x32):</span>
        <SaleVolumeSparkLine :data="sampleData1" :width="200" :height="32" />
      </div>
    </div>

    <div class="example-section">
      <h4>In table context</h4>
      <table class="demo-table">
        <thead>
          <tr>
            <th>Product</th>
            <th>Store</th>
            <th>Sales Volume</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product A</td>
            <td>Store 1</td>
            <td><SaleVolumeSparkLine :data="sampleData1" /></td>
          </tr>
          <tr>
            <td>Product B</td>
            <td>Store 2</td>
            <td><SaleVolumeSparkLine :data="increasingData" /></td>
          </tr>
          <tr>
            <td>Product C</td>
            <td>Store 3</td>
            <td><SaleVolumeSparkLine :data="decreasingData" /></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SaleVolumeSparkLine from './SaleVolumeSparkLine.vue'

// Sample data
const sampleData1 = ref([10, 15, 12, 18, 14, 20, 16, 8, 6, 4, 3, 2])
const increasingData = ref([2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24])
const decreasingData = ref([24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2])
const randomData = ref([15, 8, 23, 12, 19, 6, 14, 21, 9, 17, 11, 5])
</script>

<style scoped>
.sparkline-examples {
  padding: 20px;
  max-width: 800px;
}

.example-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.example-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #262626;
}

.example-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;
}

.example-row span {
  min-width: 150px;
  font-weight: 500;
}

.demo-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.demo-table th,
.demo-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #e8e8e8;
}

.demo-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.demo-table td:last-child {
  text-align: center;
}
</style>
