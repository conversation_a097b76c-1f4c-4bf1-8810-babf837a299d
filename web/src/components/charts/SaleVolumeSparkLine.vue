<template>
  <div class="sale-volume-sparkline" :style="{ width: `${width}px`, height: `${height}px` }">
    <svg :width="width" :height="height" :viewBox="`0 0 ${width} ${height}`">
      <!-- Background bars (light gray) -->
      <rect
        v-for="(value, index) in normalizedData"
        :key="`bg-${index}`"
        :x="index * barWidth"
        :y="0"
        :width="barWidth - barSpacing"
        :height="height"
        :fill="getBackgroundColor(index)"
        :opacity="0.3"
      />

      <!-- Data bars -->
      <rect
        v-for="(value, index) in normalizedData"
        :key="`bar-${index}`"
        :x="index * barWidth"
        :y="height - value.height"
        :width="barWidth - barSpacing"
        :height="value.height"
        :fill="getBarColor(index, value.originalValue)"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /** Sales volume data array */
  data: number[]
  /** Width of the sparkline in pixels */
  width?: number
  /** Height of the sparkline in pixels */
  height?: number
  /** Color scheme for bars */
  colorScheme?: 'blue-orange' | 'custom'
  /** Custom colors array */
  customColors?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  width: 154,
  height: 24,
  colorScheme: 'blue-orange',
  customColors: () => []
})

// Calculate bar dimensions
const barSpacing = computed(() => 1)
const barWidth = computed(() => props.width / props.data.length)

// Normalize data to fit within height
const normalizedData = computed(() => {
  if (!props.data.length) return []

  const maxValue = Math.max(...props.data)
  const minValue = Math.min(...props.data)
  const range = maxValue - minValue || 1 // Avoid division by zero

  return props.data.map((value, index) => ({
    height: ((value - minValue) / range) * props.height,
    originalValue: value,
    index
  }))
})

// Get bar color based on position and value
const getBarColor = (index: number, value: number): string => {
  if (props.colorScheme === 'custom' && props.customColors.length > 0) {
    return props.customColors[index % props.customColors.length]
  }

  // Default blue-orange scheme based on the image
  const totalBars = props.data.length
  const blueCount = Math.ceil(totalBars * 0.4) // First 40% are blue
  const orangeCount = Math.ceil(totalBars * 0.3) // Next 30% are orange

  if (index < blueCount) {
    return '#1890ff' // Blue
  } else if (index < blueCount + orangeCount) {
    return '#fa8c16' // Orange
  } else {
    return '#d9d9d9' // Light gray for remaining bars
  }
}

// Get background color for bars
const getBackgroundColor = (index: number): string => {
  return '#ffffff'
}
</script>

<style scoped>
.sale-volume-sparkline {
  display: inline-block;
  vertical-align: middle;
}

.sale-volume-sparkline svg {
  display: block;
}
</style>
