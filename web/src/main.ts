import { createApp } from 'vue'
import { createPinia } from 'pinia'

import HighchartsVue from 'highcharts-vue'
import Highcharts from 'highcharts'
import Antd from 'ant-design-vue'
import '@/assets/base.css';
import 'ant-design-vue/dist/reset.css'
import App from './App.vue'
import router from './router'
import patternFillInit from 'highcharts/modules/pattern-fill';
import hcHeatmap from "highcharts/modules/heatmap";

const app = createApp(App)

app.use(HighchartsVue)
patternFillInit(Highcharts);
hcHeatmap(Highcharts);

app.use(Antd)
app.use(createPinia())
app.use(router)

app.mount('#app')
