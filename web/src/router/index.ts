import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/analytics',
    },
    {
      path: '/analytics',
      name: '<PERSON>aHangTuDong',
      redirect: '/analytics/inventory-detail',
      component: () => import('@/views/analytics/AnalyticsPage.vue'),
      children: [
        // {
        //   path: 'inventory-detail',
        //   name: 'InventoryDetail',
        //   component: () =>
        //     import('@/views/analytics/views/inventory-detail/InventoryDetailPage.vue'),
        // },
        {
          path: 'inventory-detail',
          name: 'InventoryDetail2',
          redirect: '/analytics/inventory-detail/view-by-sku',
          component: () =>
            import('@/views/analytics/views/inventory-detail-2/InventoryDetailPage.vue'),
          children: [
            {
              path: 'view-by-sku',
              name: 'ViewBySKU',
              component: () =>
                import('@/views/analytics/views/inventory-detail-2/views/sku/DetailBySKU.vue'),
            },
            {
              path: 'view-by-forecast',
              name: 'ViewByForecast',
              component: () =>
                import('@/views/analytics/views/inventory-detail-2/views/forecast/DetailByForecast.vue'),
            },
            {
              path: 'view-by-store',
              name: 'ViewByStore',
              component: () =>
                import('@/views/analytics/views/inventory-detail-2/views/store/DetailByStore.vue'),
            },
          ],
        },

        {
          path: 'replenishment',
          name: 'Replenishment',
          component: () => import('@/views/analytics/views/replenishment/ReplenishmentPage.vue'),
        },

        {
          path: 'inventory-overview',
          name: 'InventoryOverview',
          component: () =>
            import('@/views/analytics/views/inventory-overview/InventoryOverviewPage.vue'),
        },
        {
          path: 'pt-calendar',
          name: 'pt-calendar',
          component: () => import('@/views/analytics/views/pt-calendar/PTCalendarPage.vue'),
        },

        {
          path: 'po-calendar',
          name: 'po-calendar',
          component: () => import('@/views/analytics/views/po-calendar/POCalendarPage.vue'),
        },

        {
          path: 'performance-report',
          name: 'performance-report',
          component: () =>
            import('@/views/analytics/views/performance-report/PerformanceReportPage.vue'),
        },
      ],
    },
  ],
})

export default router
