import type {
  StoreInventoryData,
  SKUForecastData,
  SKUData,
  PivotTableResponse,
  ChartDataPoint,
  ForecastSaleVolumeData,
  OverviewMetrics,
  ApiResponse,
  PaginatedResponse,
  GetStoreInventoryRequest,
  GetSKUForecastRequest,
  GetSKUDataRequest,
  GetPivotDataRequest,
  GetChartDataRequest
} from '../types/index'

export abstract class InventoryDetail2Service {
  abstract getStoreInventoryData(request: GetStoreInventoryRequest): Promise<PaginatedResponse<StoreInventoryData>>
  abstract getSKUForecastData(request: GetSKUForecastRequest): Promise<ApiResponse<SKUForecastData[]>>
  abstract getSKUData(request: GetSKUDataRequest): Promise<PaginatedResponse<SKUData>>
  abstract getPivotData(request: GetPivotDataRequest): Promise<ApiResponse<PivotTableResponse>>
  abstract getChartData(request: GetChartDataRequest): Promise<ApiResponse<ChartDataPoint[] | ForecastSaleVolumeData>>
  abstract getOverviewMetrics(): Promise<ApiResponse<OverviewMetrics>>
}
