import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import type {
  StoreInventoryData,
  SKUForecastData,
  SKUData,
  PivotTableResponse,
  PivotColumn,
  PivotRow,
  ChartDataPoint,
  ForecastSaleVolumeData,
  OverviewMetrics,
  ApiResponse,
  PaginatedResponse,
  GetStoreInventoryRequest,
  GetSKUForecastRequest,
  GetSKUDataRequest,
  GetPivotDataRequest,
  GetChartDataRequest
} from '../types/index'
import { InventoryDetail2Service } from './InventoryDetail2Service'

export class InventoryDetail2MockService extends InventoryDetail2Service {
  async getStoreInventoryData(request: GetStoreInventoryRequest): Promise<PaginatedResponse<StoreInventoryData>> {
    // Simulate API delay
    await this.delay(500)

    const mockData: StoreInventoryData[] = [
      {
        key: '1',
        id: 1,
        storeName: 'KFM_HCM_Q07 - 571 <PERSON>ỳnh Tấn P...',
        storeCode: 'KFM_HCM_Q07_571',
        inventoryQuantity: 100,
        inventoryValue: 100,
        dayOnHand: 12,
        salesVolume: 100,
        grossProfit: 100,
        oos: 100,
        salesVelocity: 100,
        status: 'Active',
      },
      {
        key: '2',
        id: 2,
        storeName: 'KFM_HCM_Q07 - 102 LÂM VĂN BỀ...',
        storeCode: 'KFM_HCM_Q07_102',
        inventoryQuantity: 203,
        inventoryValue: 203,
        dayOnHand: 11,
        salesVolume: 203,
        grossProfit: 203,
        oos: 203,
        salesVelocity: 203,
        status: 'Last sale',
      },
      {
        key: '3',
        id: 3,
        storeName: 'KFM_HCM_BCH - 10 Phạm Hùng -...',
        storeCode: 'KFM_HCM_BCH_10',
        inventoryQuantity: 300,
        inventoryValue: 300,
        dayOnHand: 10,
        salesVolume: 300,
        grossProfit: 300,
        oos: 300,
        salesVelocity: 300,
        status: 'Active',
      },
      {
        key: '4',
        id: 4,
        storeName: 'KFM_HCM_Q07 - 102 LÂM VĂN BỀ...',
        storeCode: 'KFM_HCM_Q07_102',
        inventoryQuantity: 450,
        inventoryValue: 450,
        dayOnHand: 13,
        salesVolume: 450,
        grossProfit: 450,
        oos: 450,
        salesVelocity: 450,
        status: 'Inactive',
      },
      {
        key: '5',
        id: 5,
        storeName: 'KFM_HCM_Q07 - 102 LÂM VĂN BỀ...',
        storeCode: 'KFM_HCM_Q07_102',
        inventoryQuantity: 1200,
        inventoryValue: 1200,
        dayOnHand: 14,
        salesVolume: 1200,
        grossProfit: 1200,
        oos: 1200,
        salesVelocity: 1200,
        status: 'Active',
      },
      {
        key: '6',
        id: 6,
        storeName: 'KFM_HCM_Q07 - 31 Tân Mỹ - MART',
        storeCode: 'KFM_HCM_Q07_31',
        inventoryQuantity: 1550,
        inventoryValue: 1550,
        dayOnHand: 15,
        salesVolume: 1550,
        grossProfit: 1550,
        oos: 1550,
        salesVelocity: 1550,
        status: 'Active',
      }
    ]

    // Generate more mock data for pagination
    const expandedData: StoreInventoryData[] = []
    for (let i = 0; i < 50; i++) {
      const baseIndex = i % mockData.length
      const baseItem = mockData[baseIndex]
      expandedData.push({
        ...baseItem,
        key: `${i + 1}`,
        id: i + 1,
        storeName: `${baseItem.storeName} - ${i + 1}`,
        storeCode: `${baseItem.storeCode}_${i + 1}`,
        inventoryQuantity: this.getRandomInRange(50, 2000),
        inventoryValue: this.getRandomInRange(50, 2000),
        dayOnHand: this.getRandomInRange(5, 20),
        salesVolume: this.getRandomInRange(50, 2000),
        grossProfit: this.getRandomInRange(50, 2000),
        oos: this.getRandomInRange(50, 2000),
        salesVelocity: this.getRandomInRange(50, 2000),
      })
    }

    // Apply search filter
    let filteredData = expandedData
    if (request.search) {
      filteredData = expandedData.filter(item =>
        item.storeName.toLowerCase().includes(request.search!.toLowerCase()) ||
        item.storeCode.toLowerCase().includes(request.search!.toLowerCase())
      )
    }

    // Apply pagination
    const page = request.page || 1
    const pageSize = request.pageSize || 10
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      data: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    }
  }

  async getSKUForecastData(request: GetSKUForecastRequest): Promise<ApiResponse<SKUForecastData[]>> {
    await this.delay(300)

    const mockData: SKUForecastData[] = [
      {
        id: '1',
        key: 'FP-001',
        productName: 'Rau cải xanh',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        forecastNext3Day: 30,
        forecastNext7Day: 70,
        forecastNext1Month: 250,
        forecastNext3Month: 720,
        forecastNext1Year: 2800,
      },
      {
        id: '2',
        key: 'FP-002',
        productName: 'Thịt heo',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        forecastNext3Day: 50,
        forecastNext7Day: 120,
        forecastNext1Month: 400,
        forecastNext3Month: 1100,
        forecastNext1Year: 4500,
      },
      {
        id: '3',
        key: 'FP-003',
        productName: 'Cá hồi',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        forecastNext3Day: 20,
        forecastNext7Day: 55,
        forecastNext1Month: 190,
        forecastNext3Month: 570,
        forecastNext1Year: 2200,
      }
    ]

    // Generate more mock data
    const expandedData: SKUForecastData[] = []
    const productNames = [
      'Rau cải xanh', 'Thịt heo', 'Cá hồi', 'Sữa tươi hộp', 'Trứng gà',
      'Gạo trắng', 'Bánh mì tươi', 'Nước cam ép', 'Thịt bò Úc', 'Dưa hấu ruột đỏ'
    ]

    for (let i = 0; i < 20; i++) {
      const baseIndex = i % mockData.length
      const baseItem = mockData[baseIndex]
      const productIndex = i % productNames.length

      expandedData.push({
        ...baseItem,
        id: `${i + 1}`,
        key: `FP-${String(i + 1).padStart(3, '0')}`,
        productName: `${productNames[productIndex]} - ${i + 1}`,
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        forecastNext3Day: this.getRandomInRange(10, 100),
        forecastNext7Day: this.getRandomInRange(50, 200),
        forecastNext1Month: this.getRandomInRange(150, 500),
        forecastNext3Month: this.getRandomInRange(400, 1500),
        forecastNext1Year: this.getRandomInRange(1500, 6000),
      })
    }

    return {
      data: expandedData,
      success: true,
      message: 'SKU forecast data retrieved successfully'
    }
  }

  async getSKUData(request: GetSKUDataRequest): Promise<PaginatedResponse<SKUData>> {
    await this.delay(400)

    const mockData: SKUData[] = [
      {
        key: '1',
        id: 1,
        productName: 'Thịt gà hữu cơ',
        productCode: '8001234##',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/organic-chicken',
        revenue: '20.5M',
        soldQuantity: 120100,
        avgSellingPrice: 30,
        orders: '10/300',
        avgOrderValue: '4.004',
        dc: 10,
        store: '2.334',
        saleVolume: '20.5M',
        saleVolumeChart: [1, 3, 2, 4, 3, 5, 4],
        saleValue: '22.5M',
        saleValueChart: [2, 4, 3, 5, 4, 6, 5],
        grossProfit: '11.5M',
        grossProfitPercent: '+2%',
      },
      {
        key: '2',
        id: 2,
        productName: 'Cá hồi Na Uy tươi',
        productCode: '8001235##',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/fresh-norwegian-salmon',
        revenue: '18.3M',
        soldQuantity: 98500,
        avgSellingPrice: 35,
        orders: '8/250',
        avgOrderValue: '3.892',
        dc: 8,
        store: '2.156',
        saleVolume: '18.3M',
        saleVolumeChart: [2, 4, 3, 5, 4, 6, 5],
        saleValue: '19.8M',
        saleValueChart: [3, 5, 4, 6, 5, 7, 6],
        grossProfit: '9.2M',
        grossProfitPercent: '+1.5%',
      }
    ]

    // Generate more mock data
    const expandedData: SKUData[] = []
    const productNames = [
      'Thịt gà hữu cơ', 'Cá hồi Na Uy tươi', 'Rau cải xanh Đà Lạt', 'Gạo Jasmine hữu cơ',
      'Trái cây theo mùa', 'Sữa tươi không đường', 'Thịt bò Úc nhập khẩu', 'Bánh mì nguyên cám'
    ]

    for (let i = 0; i < 30; i++) {
      const baseIndex = i % mockData.length
      const baseItem = mockData[baseIndex]
      const productIndex = i % productNames.length

      expandedData.push({
        ...baseItem,
        key: `${i + 1}`,
        id: i + 1,
        productName: `${productNames[productIndex]} - ${i + 1}`,
        productCode: `800${String(i + 1).padStart(4, '0')}##`,
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: `/products/${productNames[productIndex].toLowerCase().replace(/\s+/g, '-')}-${i + 1}`,
        revenue: `${this.getRandomInRange(10, 50)}.${this.getRandomInRange(1, 9)}M`,
        soldQuantity: this.getRandomInRange(50000, 200000),
        avgSellingPrice: this.getRandomInRange(20, 50),
        orders: `${this.getRandomInRange(5, 15)}/${this.getRandomInRange(200, 400)}`,
        avgOrderValue: `${this.getRandomInRange(2, 6)}.${this.getRandomInRange(100, 999)}`,
        dc: this.getRandomInRange(5, 15),
        store: `${this.getRandomInRange(1, 5)}.${this.getRandomInRange(100, 999)}`,
        saleVolume: `${this.getRandomInRange(15, 35)}.${this.getRandomInRange(1, 9)}M`,
        saleVolumeChart: Array.from({ length: 7 }, () => this.getRandomInRange(1, 8)),
        saleValue: `${this.getRandomInRange(18, 40)}.${this.getRandomInRange(1, 9)}M`,
        saleValueChart: Array.from({ length: 7 }, () => this.getRandomInRange(2, 9)),
        grossProfit: `${this.getRandomInRange(8, 25)}.${this.getRandomInRange(1, 9)}M`,
        grossProfitPercent: `+${this.getRandomInRange(1, 5)}%`,
      })
    }

    // Apply search filter
    let filteredData = expandedData
    if (request.search) {
      filteredData = expandedData.filter(item =>
        item.productName.toLowerCase().includes(request.search!.toLowerCase()) ||
        item.productCode.toLowerCase().includes(request.search!.toLowerCase())
      )
    }

    // Apply pagination
    const page = request.page || 1
    const pageSize = request.pageSize || 10
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      data: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    }
  }

  async getPivotData(request: GetPivotDataRequest): Promise<ApiResponse<PivotTableResponse>> {
    await this.delay(600)

    const columns = this.generateColumns(request.dateHistogram as DateHistogram)
    const isStoreView = request.storeId !== undefined || request.dateHistogram === 'store_view'
    const rows = isStoreView ? this.generateStoreRows(columns):  this.generateSKURows(columns)

    return {
      data: {
        columns,
        rows
      },
      success: true,
      message: 'Pivot data retrieved successfully'
    }
  }

  async getChartData(request: GetChartDataRequest): Promise<ApiResponse<ChartDataPoint[] | ForecastSaleVolumeData>> {
    await this.delay(350)

    if (request.type === 'inventory-dc') {
      const histogram = request.histogram || 'day_of'
      const chartData: ChartDataPoint[] = []
      const now = new Date()

      let points = 15
      let dateIncrement = 1
      let dateFormat = 'day'

      switch (histogram) {
        case 'day_of':
          points = 15
          dateIncrement = 1
          dateFormat = 'day'
          break
        case 'week_of':
          points = 8
          dateIncrement = 7
          dateFormat = 'week'
          break
        case 'month_of':
          points = 6
          dateIncrement = 30
          dateFormat = 'month'
          break
        case 'quarter_of':
          points = 4
          dateIncrement = 90
          dateFormat = 'quarter'
          break
        case 'year_of':
          points = 3
          dateIncrement = 365
          dateFormat = 'year'
          break
      }

      for (let i = 0; i < points; i++) {
        const date = new Date(now)
        date.setDate(date.getDate() + i * dateIncrement)

        let dateLabel = ''
        let isWeekend = false

        if (dateFormat === 'day') {
          const day = date.getDate()
          const month = date.getMonth() + 1
          dateLabel = `${day}/${month}`
          isWeekend = date.getDay() === 0 || date.getDay() === 6
        } else if (dateFormat === 'week') {
          const weekStart = new Date(date)
          const weekEnd = new Date(date)
          weekEnd.setDate(weekEnd.getDate() + 6)
          dateLabel = `W${Math.ceil(date.getDate() / 7)}`
          isWeekend = false // Weeks don't have weekend concept
        } else if (dateFormat === 'month') {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
          dateLabel = `${monthNames[date.getMonth()]}`
          isWeekend = false
        } else if (dateFormat === 'quarter') {
          const quarter = Math.floor(date.getMonth() / 3) + 1
          dateLabel = `Q${quarter}`
          isWeekend = false
        } else if (dateFormat === 'year') {
          dateLabel = `${date.getFullYear()}`
          isWeekend = false
        }

        chartData.push({
          date: dateLabel,
          value: this.getRandomInRange(100, 200),
          isWeekend
        })
      }

      return {
        data: chartData,
        success: true,
        message: 'Chart data retrieved successfully'
      }
    }

    if (request.type === 'forecast-sale-volume') {
      const channel = request.channel || 'all'
      const histogram = request.histogram || 'day_of'

      const data = []
      const soLuongData = []
      const promotionData = []
      const dates = []
      const now = new Date()

      const points = histogram === 'day_of' ? 30 : histogram === 'week_of' ? 12 : 6

      for (let i = 0; i < points; i++) {
        const date = new Date(now)
        if (histogram === 'day_of') {
          date.setDate(date.getDate() + i)
          dates.push(date.toLocaleDateString('vi-VN'))
        } else if (histogram === 'week_of') {
          date.setDate(date.getDate() + i * 7)
          dates.push(`Tuần ${Math.floor(i / 7) + 1}`)
        } else {
          date.setMonth(date.getMonth() + i)
          dates.push(`Tháng ${date.getMonth() + 1}`)
        }

        data.push(this.getRandomInRange(100, 500))
        soLuongData.push(this.getRandomInRange(50, 200))
        promotionData.push(this.getRandomInRange(10, 100))
      }

      const forecastData: ForecastSaleVolumeData = {
        data,
        soLuongData,
        promotionData,
        dates
      }

      return {
        data: forecastData,
        success: true,
        message: 'Forecast sale volume data retrieved successfully'
      }
    }

    throw new Error('Unknown chart type')
  }

  async getOverviewMetrics(): Promise<ApiResponse<OverviewMetrics>> {
    await this.delay(200)

    const metrics: OverviewMetrics = {
      alerts: this.getRandomInRange(5, 50),
      lastSales: this.getRandomInRange(100, 500),
      deadStock: this.getRandomInRange(10, 100),
      nonMoving: this.getRandomInRange(20, 200),
      lowStock: this.getRandomInRange(15, 150)
    }

    return {
      data: metrics,
      success: true,
      message: 'Overview metrics retrieved successfully'
    }
  }

  private generateColumns(dateHistogram: DateHistogram): PivotColumn[] {
    const columns: PivotColumn[] = []
    const today = new Date()

    let numColumns = 7
    let dateIncrement = 0

    switch (dateHistogram) {
      case DateHistogram.DayOf:
        numColumns = 7
        dateIncrement = 1
        break
      case DateHistogram.WeekOf:
        numColumns = 4
        dateIncrement = 7
        break
      case DateHistogram.MonthOf:
        numColumns = 6
        dateIncrement = 30
        break
      case DateHistogram.QuarterOf:
        numColumns = 4
        dateIncrement = 90
        break
      case DateHistogram.YearOf:
        numColumns = 3
        dateIncrement = 365
        break
      default:
        numColumns = 7
        dateIncrement = 1
    }

    for (let i = 0; i < numColumns; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() + i * dateIncrement)

      let columnName = ''

      if (dateHistogram === DateHistogram.DayOf) {
        const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7']
        const dayOfWeek = date.getDay()
        const day = date.getDate().toString().padStart(2, '0')
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        columnName = `${dayNames[dayOfWeek]} ${day}/${month}`
      } else if (dateHistogram === DateHistogram.WeekOf) {
        const startDay = date.getDate().toString().padStart(2, '0')
        const startMonth = (date.getMonth() + 1).toString().padStart(2, '0')
        const endDate = new Date(date)
        endDate.setDate(date.getDate() + 6)
        const endDay = endDate.getDate().toString().padStart(2, '0')
        const endMonth = (endDate.getMonth() + 1).toString().padStart(2, '0')
        columnName = `Tuần ${startDay}/${startMonth} - ${endDay}/${endMonth}`
      } else if (dateHistogram === DateHistogram.MonthOf) {
        const monthNames = [
          'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
          'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]
        columnName = `${monthNames[date.getMonth()]} ${date.getFullYear()}`
      } else if (dateHistogram === DateHistogram.QuarterOf) {
        const quarter = Math.floor(date.getMonth() / 3) + 1
        columnName = `Q${quarter} ${date.getFullYear()}`
      } else if (dateHistogram === DateHistogram.YearOf) {
        columnName = `Năm ${date.getFullYear()}`
      }

      columns.push({
        id: `column_${i + 1}`,
        name: columnName,
      })
    }

    return columns
  }

  private generateStoreRows(columns: PivotColumn[]): PivotRow[] {
    const storeItems = [
      {
        id: '1',
        name: 'KFM_HCM_Q07 - 571 Huỳnh Tấn P...',
        thumbnail: 'https://via.placeholder.com/40?text=S1',
        link: '/stores/kfm-hcm-q07-571',
      },
      {
        id: '2',
        name: 'KFM_HCM_Q07 - 102 LÂM VĂN BỀ...',
        thumbnail: 'https://via.placeholder.com/40?text=S2',
        link: '/stores/kfm-hcm-q07-102',
      },
      {
        id: '3',
        name: 'KFM_HCM_BCH - 10 Phạm Hùng -...',
        thumbnail: 'https://via.placeholder.com/40?text=S3',
        link: '/stores/kfm-hcm-bch-10',
      },
      {
        id: '4',
        name: 'KFM_HCM_TBI - 22 Hoàng Hoa Th...',
        thumbnail: 'https://via.placeholder.com/40?text=S4',
        link: '/stores/kfm-hcm-tbi-22',
      },
      {
        id: '5',
        name: 'KFM_HCM_Q10 - BB17 Trường Sơn...',
        thumbnail: 'https://via.placeholder.com/40?text=S5',
        link: '/stores/kfm-hcm-q10-bb17',
      },
      {
        id: '6',
        name: 'KFM_HCM_Q07 - 31 Tân Mỹ - MART',
        thumbnail: 'https://via.placeholder.com/40?text=S6',
        link: '/stores/kfm-hcm-q07-31',
      },
      {
        id: '7',
        name: 'KFM_HCM_Q01 - 123 Nguyễn Huệ...',
        thumbnail: 'https://via.placeholder.com/40?text=S7',
        link: '/stores/kfm-hcm-q01-123',
      },
      {
        id: '8',
        name: 'KFM_HCM_Q03 - 456 Võ Văn Tần...',
        thumbnail: 'https://via.placeholder.com/40?text=S8',
        link: '/stores/kfm-hcm-q03-456',
      }
    ]

    return storeItems.map((item) => {
      const row: PivotRow = {
        id: item.id,
        name: item.name,
        thumbnail: item.thumbnail,
        link: item.link,
      }

      columns.forEach((column) => {
        row[column.id] = Math.floor(Math.random() * 1000) + 100
      })

      return row
    })
  }

  private generateSKURows(columns: PivotColumn[]): PivotRow[] {
    const skuItems = [
      {
        id: '1',
        name: 'Thịt gà hữu cơ',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/organic-chicken',
      },
      {
        id: '2',
        name: 'Rau cải xanh Đà Lạt',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/dalat-green-vegetables',
      },
      {
        id: '3',
        name: 'Cá hồi Na Uy tươi',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/fresh-norwegian-salmon',
      },
      {
        id: '4',
        name: 'Gạo Jasmine hữu cơ',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/organic-jasmine-rice',
      },
      {
        id: '5',
        name: 'Trái cây theo mùa',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/seasonal-fruits',
      },
      {
        id: '6',
        name: 'Sữa tươi không đường',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/unsweetened-fresh-milk',
      },
      {
        id: '7',
        name: 'Thịt bò Úc nhập khẩu',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/imported-australian-beef',
      },
      {
        id: '8',
        name: 'Bánh mì nguyên cám',
        thumbnail: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS94OweYHEpK5ooqRJfO2d1UW4ZQsIFoGRtDg&s',
        link: '/products/whole-grain-bread',
      }
    ]

    return skuItems.map((item) => {
      const row: PivotRow = {
        id: item.id,
        name: item.name,
        thumbnail: item.thumbnail,
        link: item.link,
      }

      columns.forEach((column) => {
        row[column.id] = Math.floor(Math.random() * 1000) + 100
      })

      return row
    })
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private getRandomInRange(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }
}

// Export singleton instance
export default new InventoryDetail2MockService()
