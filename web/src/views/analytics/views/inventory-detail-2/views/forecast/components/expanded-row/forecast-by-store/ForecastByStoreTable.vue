<script setup lang="ts">
import StatusCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/StatusCell.vue'
import { type ColumnType } from 'ant-design-vue/es/table'
import { computed } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import {
  convertToAntTableFormat,
  type PivotTableResponse,
} from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/forecast-by-store/ForecastByStoreData.ts'

type Props = {
  pivotData: PivotTableResponse
}

const props = defineProps<Props>()

const { columns, dataSource } = computed(() => {
  return convertToAntTableFormat(props.pivotData)
}).value

const getBackgroundColorStatus = (text: string) => {
  if (text.includes('Good')) {
    return '#EDFCF2'
  }

  if (text.includes('Not good')) {
    return '#FFF1F3'
  }
  if (text.includes('Đã nhận')) {
    return '#EDFCF2'
  }

  if (text.includes('Đang chuyển')) {
    return '#EFF4FF'
  }
  return 'white'
}

const getColorStaus = (text: string) => {
  if (text.includes('Good')) {
    return '#087443'
  }

  if (text.includes('Not good')) {
    return '#C01048'
  }
  if (text.includes('Đã nhận')) {
    return '#087443'
  }

  if (text.includes('Đang chuyển')) {
    return '#004EEB'
  }
  return 'black'
}

const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}

const paginationConfig: TablePaginationConfig = {
  current: 1,
  pageSize: 10,
  total: props.pivotData.rows.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'default',
  position: ['bottomRight'],
}
</script>

<template>
  <div class="inventory-dc__table table-hover-effects" ref="tableContainerRef">
    <ATable
      :scroll="{ x: 'max-content' }"
      size="small"
      :columns="columns"
      :data-source="dataSource"
      :pagination="paginationConfig"
      @resizeColumn="handleResizeColumn"
    >
      <template #status="{ text }">
        <StatusCell
          :status="text"
          :background-color="getBackgroundColorStatus(text)"
          :color="getColorStaus(text)"
        />
      </template>
    </ATable>
  </div>
</template>

<style scoped>
.inventory-dc__table :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.inventory-dc__table :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.inventory-dc__table :deep(.ant-table) {
  margin-inline: 0 !important;
}
</style>
