<script setup lang="ts">

import Overview from '@/views/analytics/views/inventory-detail-2/components/overview/Overview.vue'
import SKUDetailByForecast
  from '@/views/analytics/views/inventory-detail-2/views/forecast/components/SKUDetailByForecast.vue'
</script>

<template>
  <div id="view-by-forecast">
    <Overview/>
    <SKUDetailByForecast />
  </div>
</template>

<style scoped>
#view-by-forecast {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
