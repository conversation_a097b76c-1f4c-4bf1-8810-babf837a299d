<script setup lang="ts">
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import { ref, watch, onMounted } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import ForecastByStoreTable from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/forecast-by-store/ForecastByStoreTable.vue'
import { type PivotTableResponse } from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/forecast-by-store/ForecastByStoreData.ts'
import { usePivotData } from '@/views/analytics/views/inventory-detail-2/composables/useInventoryData.ts'

const dateHistogram = ref<DateHistogram>(DateHistogram.DayOf)

// Use the composable for data management
const pivotData = usePivotData({
  storeId: 'store_view',
  dateHistogram: dateHistogram.value.toString()
})

// Load data on mount
onMounted(() => {
  pivotData.execute()
})

// Watch for date histogram changes and reload data
watch(dateHistogram, (newHistogram) => {
  pivotData.execute()
})
</script>

<template>
  <div class="inventory-dc">
    <div class="inventory-dc__header">
      <h6>Lịch sử sức bán theo Store</h6>
      <div class="inventory-dc__actions">
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="pivotData.loading.value" class="loading-container">
      <ASpin size="large" />
      <p>Loading forecast data...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="pivotData.error.value" class="error-container">
      <AAlert
        type="error"
        :message="pivotData.error.value.message"
        show-icon
      >
        <template #action>
          <AButton size="small" @click="pivotData.refresh()">
            Retry
          </AButton>
        </template>
      </AAlert>
    </div>

    <!-- Data table -->
    <ForecastByStoreTable
      v-else-if="pivotData.data.value?.data"
      class="inventory-dc__chart"
      :date-histogram="dateHistogram"
      :pivot-data="pivotData.data.value.data"
    />
  </div>
</template>
<style scoped>
.inventory-dc {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.inventory-dc h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.inventory-dc__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.inventory-dc__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
