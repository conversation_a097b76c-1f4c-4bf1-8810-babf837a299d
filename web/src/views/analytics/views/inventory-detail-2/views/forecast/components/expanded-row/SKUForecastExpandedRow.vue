<script setup lang="ts">
import Tab from '@/views/analytics/views/inventory-detail-2/components/Tab.vue'

const tabs = [
  { id: 'forecast-sale-volume', label: '<PERSON>ứ<PERSON> b<PERSON>', component: () => import('./forecast-sale-volume/ForecastSaleVolume.vue') },
  { id: 'forecast-by-store', label: 'Sức bán theo Store', component: () => import('./forecast-by-store/ForecastByStore.vue') },
]

const handleTabChange = (tabId: string) => {
  console.log('Tab changed to:', tabId)
}
</script>

<template>
  <Tab :tabs="tabs" defaultTab="forecast-sale-volume" @tab-change="handleTabChange" />
</template>
