<script setup lang="ts">
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import { ref } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import ForecastSaleVolumeChart from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/forecast-sale-volume/ForecastSaleVolumeChart.vue'

const dateHistogram = ref<DateHistogram>(DateHistogram.DayOf)
</script>

<template>
  <div class="inventory-dc">
    <div class="inventory-dc__header">
      <h6>Sale history</h6>
      <div class="inventory-dc__actions">
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <ForecastSaleVolumeChart class="inventory-dc__chart" :date-histogram="dateHistogram" />
  </div>
</template>
<style scoped>
.inventory-dc {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.inventory-dc h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.inventory-dc__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.inventory-dc__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}
</style>
