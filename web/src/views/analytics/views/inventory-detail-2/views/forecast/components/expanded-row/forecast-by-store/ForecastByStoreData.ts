import { h } from 'vue'
import ImageCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ImageCell.vue'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'

// Re-export types from common types file
export type { PivotColumn, PivotRow, PivotTableResponse } from '../../../../../types/index'
import type { PivotTableResponse, PivotRow } from '../../../../../types/index'

// This file now uses the service instead of hardcoded mock data
// The mock data has been moved to InventoryDetail2Service.ts

// Keep the convertToAntTableFormat function as it's used by the table component
export function convertToAntTableFormat(pivotData: PivotTableResponse) {
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
    },
    {
      title: 'Product name',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      resizable: true,
      ellipsis: true,
      customRender: ({ record }: { record: PivotRow }) => {
        return h(ProductNameCell, {
          productName: record.name,
          expanded: {
            enabled: false,
            isExpanded: false,
          },
        })
      },
    },
    ...pivotData.columns.map((column) => ({
      title: column.name,
      dataIndex: column.id,
      key: column.id,
      minWidth: 120,
      align: 'right',
    })),
  ]

  const dataSource = pivotData.rows.map((row, index) => ({
    key: row.id,
    index: index + 1,
    ...row,
  }))

  return { columns, dataSource }
}
