<script setup lang="ts">
import SkuDetailActions from '@/views/analytics/views/inventory-detail-2/views/sku/components/SkuDetailActions.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import { h, ref, onMounted, computed, watch } from 'vue'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'
import type { ColumnType } from 'ant-design-vue/es/table'
import { type SKUForecastData } from '@/views/analytics/views/inventory-detail-2/views/forecast/components/SKUForecastData.ts'
import SKUForecastExpandedRow from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/SKUForecastExpandedRow.vue'
import { useSKUForecastData } from '../../../composables/useInventoryData'
import ImageCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ImageCell.vue'

const expandedRows = ref<string[]>([])
const tableContainerRef = ref<HTMLDivElement>()
useTableHoverEffects(tableContainerRef)

// Use the composable for data management
const skuForecast = useSKUForecastData({})

// Load data on mount
onMounted(() => {
  skuForecast.execute()
})

const dataSource = computed(() => {
  return skuForecast.data.value?.data || []
})
const columns = ref<ColumnType<SKUForecastData>[]>([
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    title: '',
    dataIndex: 'thumbnail',
    key: 'thumbnail',
    width: 52,
    fixed: 'left',
    align: 'center',
    customRender: ({ record }: { record: { thumbnail: string } }) => {
      return h(ImageCell, {
        imageUrl: record.thumbnail,
      })
    },
  },
  {
    title: 'Product name',
    dataIndex: 'productName',
    key: 'productName',
    minWidth: 200,
    width: 200,
    fixed: 'left',
    resizable: true,
    ellipsis: true,
    customRender: ({ text, record }: { text: string; record: SKUForecastData }) => {
      const isExpanded = expandedRows.value.includes(record.id)
      return h(ProductNameCell, {
        productName: text,
        link: `/products/${text.toLowerCase().replace(/\s+/g, '-')}-${record.id}`,
        expanded: {
          enabled: true,
          isExpanded: isExpanded,
        },
      })
    },
  },
  {
    title: 'Sức bán 3 ngày tới',
    dataIndex: 'forecastNext3Day',
    key: 'forecastNext3Day',
    width: 183,
    align: 'right',
  },
  {
    title: 'Sức bán 7 ngày tới',
    dataIndex: 'forecastNext7Day',
    key: 'forecastNext7Day',
    width: 183,
    align: 'right',
  },
  {
    title: 'Sức bán 1 tháng tới',
    dataIndex: 'forecastNext1Month',
    key: 'forecastNext1Month',
    width: 183,
    align: 'right',
  },

  {
    title: 'Sức bán 3 tháng tới',
    dataIndex: 'forecastNext3Month',
    key: 'forecastNext3Month',
    width: 183,
    align: 'right',
  },
  {
    title: 'Sức bán 1 năm tới',
    dataIndex: 'forecastNext1Year',
    key: 'forecastNext1Year',
    width: 183,
    align: 'right',
  },
])

const handleExpandRow = (expanded: boolean, record: SKUForecastData) => {
  if (expanded) {
    expandedRows.value.push(record.id)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.id)
  }
}

const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}
const dateRange = ref(DateRange.LAST_7_DAYS)

watch(dateRange, () => {
  skuForecast.execute()
})
</script>

<template>
  <div class="sku-detail-table">
    <div class="sku-detail_header">
      <ATypographyTitle :level="5">SKU detail</ATypographyTitle>
      <SkuDetailActions v-model:date-range="dateRange" />
    </div>
    <div class="sku-detail-table__body table-hover-effects" ref="tableContainerRef">
      <!-- Loading state -->
      <div v-if="skuForecast.loading.value" class="loading-container">
        <ASpin size="large" />
        <p>Loading SKU forecast data...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="skuForecast.error.value" class="error-container">
        <AAlert type="error" :message="skuForecast.error.value.message" show-icon>
          <template #action>
            <AButton size="small" @click="skuForecast.refresh()"> Retry </AButton>
          </template>
        </AAlert>
      </div>

      <!-- Data table -->
      <ATable
        v-else
        :scroll="{ x: 'max-content' }"
        size="small"
        :columns="columns"
        :data-source="dataSource"
        :expandIcon="() => null"
        expandRowByClick
        @expand="handleExpandRow"
        :show-expand-column="false"
        @resizeColumn="handleResizeColumn"
      >
        <template #expandedRowRender="{ record }">
          <SKUForecastExpandedRow :sku="record" />
        </template>
      </ATable>
    </div>
  </div>
</template>

<style scoped>
.sku-detail-table {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: calc(100vw - 64px);
}

.sku-detail_header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sku-detail-table__body :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.sku-detail-table__body :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
