<script setup lang="ts">
import type { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { Chart } from 'highcharts-vue'
import { type Options } from 'highcharts'
import { computed, watch, ref, onMounted } from 'vue'
import Highcharts from 'highcharts'
import { useChartData } from '../../../../../composables/useInventoryData'
import type { ForecastSaleVolumeData } from '../../../../../types/index'

type Props = {
  dateHistogram: DateHistogram
}

const props = defineProps<Props>()
const highcharts = ref<Highcharts.Chart>('highcharts' as unknown as Highcharts.Chart)

// Use the composable for data management
const chartData = useChartData({
  type: 'forecast-sale-volume',
  channel: 'online',
  histogram: props.dateHistogram.toString()
})

// Load data on mount
onMounted(() => {
  chartData.execute()
})

// Watch for prop changes and reload data
watch(() => props.dateHistogram, () => {
  chartData.execute()
})


const chartOptions = computed<Options>(() => {
  const data = chartData.data.value?.data as ForecastSaleVolumeData || {
    data: [],
    soLuongData: [],
    promotionData: [],
    dates: []
  }

  return {
    chart: {
      type: 'column',
      zoomType: 'xy',
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: data.dates,
      crosshair: true,
    },
    yAxis: [
      {
        title: {
          text: '',
        },
      },
    ],
    series: [
      {
        name: `Sales Volume`,
        type: 'column',
        data: data.data,
        color: '#2F54EB',
        yAxis: 0,
      },
    ],
    tooltip: {
      shared: true,
    },
    credits: {
      enabled: false,
    },
    accessibility: {
      enabled: false,
    },
    legend: {
      enabled: false,
    },
  }
})
</script>

<template>
  <!-- Loading state -->
  <div v-if="chartData.loading.value" class="loading-container">
    <ASpin size="large" />
    <p>Loading forecast data...</p>
  </div>

  <!-- Error state -->
  <div v-else-if="chartData.error.value" class="error-container">
    <AAlert
      type="error"
      :message="chartData.error.value.message"
      show-icon
    >
      <template #action>
        <AButton size="small" @click="chartData.refresh()">
          Retry
        </AButton>
      </template>
    </AAlert>
  </div>

  <!-- Chart -->
  <Chart v-else ref="highcharts" :options="chartOptions"></Chart>
</template>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  height: 350px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
