import ImageCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ImageCell.vue'
import { h } from 'vue'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import { type PivotTableResponse, type PivotRow, type PivotColumn } from '@/views/analytics/views/inventory-detail-2/types/index.ts'


// These functions have been moved to the service
// They are kept here for backward compatibility but should be removed eventually

function convertToAntTableFormat(pivotData: PivotTableResponse) {
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
    },
    {
      title: '',
      dataIndex: 'thumbnail',
      key: 'thumbnail',
      width: 52,
      align: 'center',
      customRender: ({ record }: { record: PivotRow }) => {
        return h(ImageCell, {
          imageUrl: record.thumbnail as string,
        })
      },
    },
    {
      title: 'Product name',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      resizable: true,
      ellipsis: true,
      customRender: ({ record }: { record: PivotRow }) => {
        return h(ProductNameCell, {
          productName: record.name,
          link: record.link,
          expanded: {
            enabled: false,
            isExpanded: false,
          },
        })
      },
    },
    ...pivotData.columns.map((column) => ({
      title: column.name,
      dataIndex: column.id,
      key: column.id,
      minWidth: 120,
      align: 'right',
    })),
  ]

  const dataSource = pivotData.rows.map((row, index) => ({
    key: row.id,
    index: index + 1,
    ...row,
  }))

  return { columns, dataSource }
}

export {
  convertToAntTableFormat,
  type PivotTableResponse,
  type PivotColumn,
  type PivotRow,
}
