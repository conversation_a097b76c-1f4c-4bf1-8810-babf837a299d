<script setup lang="ts">
import { computed, h, ref, onMounted, watch } from 'vue'
import SkuDetailActions from '@/views/analytics/views/inventory-detail-2/views/sku/components/SkuDetailActions.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects.ts'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import SkuExpandedRowByStore from '@/views/analytics/views/inventory-detail-2/views/store/components/expanded-row/SkuExpandedRowByStore.vue'
import type { ColumnType } from 'ant-design-vue/es/table'
import { usePivotData } from '../../../composables/useInventoryData'

// Use the composable for data management
const pivotData = usePivotData({
  dateHistogram: 'skuId', // Special identifier for store view
  storeId: 'current_store' // Indicate this is for store view
})

// Load data on mount
onMounted(() => {
  pivotData.execute()
})

const dataSource = computed(() => {
  if (!pivotData.data.value?.data) return []
  return pivotData.data.value.data.rows.map((row, index) => ({
    key: row.id,
    index: index + 1,
    ...row,
  }))
})

const columns = ref([
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    title: 'Store name',
    dataIndex: 'name',
    key: 'name',
    minWidth: 200,
    width: 200,
    fixed: 'left',
    resizable: true,
    ellipsis: true,
    customRender: ({ text, record }: { text: string; record: {id: string} }) => {
      const isExpanded = expandedRows.value.includes(record.id)
      return h(ProductNameCell, {
        productName: text,
        expanded: {
          enabled: true,
          isExpanded: isExpanded,
        },
      })
    },
  },
  {
    title: 'Forecast tổng doanh thu bán',
    align: 'left',
    children: [],
  },
])

// Cập nhật sub-columns khi pivotData thay đổi
const updateForecastColumns = () => {
  if (!pivotData.data.value?.data?.columns || pivotData.data.value.data.columns.length === 0) {
    columns.value[2].children = []
    return
  }

  (columns.value[2] as any).children = pivotData.data.value.data.columns.map((column) => ({
    title: column.name,
    dataIndex: column.id,
    key: column.id,
    minWidth: 120,
    align: 'right',
  }))
}

// Cập nhật columns ban đầu khi component được tạo
onMounted(() => {
  updateForecastColumns()
})

// Watch sự thay đổi của pivotData.data
watch(
  () => pivotData.data.value,
  () => {
    updateForecastColumns()
  },
  { deep: true }
)

const expandedRows = ref<string[]>([])
const tableContainerRef = ref<HTMLDivElement>()
useTableHoverEffects(tableContainerRef)

const handleExpandRow = (expanded: boolean, record: { id: string }) => {
  if (expanded) {
    expandedRows.value.push(record.id)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.id)
  }
}

const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}
const dateRange = ref<DateRange>(DateRange.LAST_7_DAYS)

watch(dateRange, () => {
  pivotData.execute()
})
</script>

<template>
  <div class="sku-detail-table">
    <div class="sku-detail_header">
      <ATypographyTitle :level="5">SKU detail</ATypographyTitle>
      <SkuDetailActions v-model:date-range="dateRange" />
    </div>
    <div class="sku-detail-table__body table-hover-effects" ref="tableContainerRef">
      <!-- Loading state -->
      <div v-if="pivotData.loading.value" class="loading-container">
        <ASpin size="large" />
        <p>Loading pivot data...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="pivotData.error.value" class="error-container">
        <AAlert type="error" :message="pivotData.error.value.message" show-icon>
          <template #action>
            <AButton size="small" @click="pivotData.refresh()"> Retry </AButton>
          </template>
        </AAlert>
      </div>

      <!-- Data table -->
      <ATable
        v-else
        :scroll="{ x: 'max-content' }"
        size="small"
        :columns="columns"
        :data-source="dataSource"
        :expandIcon="() => null"
        expandRowByClick
        @expand="handleExpandRow"
        :show-expand-column="false"
        @resizeColumn="handleResizeColumn"
      >
        <template #expandedRowRender="{ record }">
          <SkuExpandedRowByStore :sku="record" />
        </template>
      </ATable>
    </div>
  </div>
</template>

<style scoped>
.sku-detail-table {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: calc(100vw - 64px);
}

.sku-detail_header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sku-detail-table__body :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.sku-detail-table__body :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
