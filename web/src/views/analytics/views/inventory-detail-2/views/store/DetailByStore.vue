<script setup lang="ts">
import Overview from '@/views/analytics/views/inventory-detail-2/components/overview/Overview.vue'
import SkuDetailTableByStore
  from '@/views/analytics/views/inventory-detail-2/views/store/components/SkuDetailTableByStore.vue'
</script>

<template>
  <div id="view-by-store">
    <Overview />
    <SkuDetailTableByStore />
  </div>
</template>

<style scoped>
#view-by-store {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
