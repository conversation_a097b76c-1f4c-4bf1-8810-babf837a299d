<script setup lang="ts">
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { Chart } from 'highcharts-vue'
import Highcharts, { type Options } from 'highcharts'
import { computed, onMounted, ref, watch } from 'vue'

type Props = {
  dateHistogram: DateHistogram
}

const props = defineProps<Props>()
const mockData = ref()
const highcharts = ref<Highcharts.Chart>('highcharts' as unknown as Highcharts.Chart)
onMounted(() => {
  mockData.value = generateMockData(props.dateHistogram) //TODO: Change to service
})

watch(
  () => props.dateHistogram,
  () => {
    mockData.value = generateMockData(props.dateHistogram)
  },
)

function generateMockData(histogram: DateHistogram) {
  const currentDate = new Date()
  const categories: string[] = []
  const inventoryData: { y: number; color: string }[] = []
  const saleData: number[] = []
  const forecastData: number[] = []

  let pointCount = 0

  if (histogram === DateHistogram.DayOf) {
    pointCount = 30

    for (let i = 0; i < pointCount; i++) {
      const date = new Date()
      date.setDate(currentDate.getDate() - (pointCount - 1) + i)

      categories.push(
        `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`,
      )

      const dayOfWeek = date.getDay() // 0 = chủ nhật, 1 = thứ 2
      const color = dayOfWeek === 1 ? '#155EEF' : dayOfWeek === 0 ? '#EAAA08' : '#155EEF'

      inventoryData.push({
        y: Math.floor(Math.random() * 1000) + 500,
        color: color,
      })

      saleData.push(Math.floor(Math.random() * 500) + 100)
      forecastData.push(Math.floor(Math.random() * 600) + 200)
    }
  } else if (histogram === DateHistogram.MonthOf) {
    pointCount = 12

    for (let i = 0; i < pointCount; i++) {
      const date = new Date()
      date.setMonth(currentDate.getMonth() - (pointCount - 1) + i)

      categories.push(`${(date.getMonth() + 1).toString().padStart(2, '0')}`)

      inventoryData.push({
        y: Math.floor(Math.random() * 5000) + 2000,
        color: '#155EEF',
      })

      saleData.push(Math.floor(Math.random() * 3000) + 500)
      forecastData.push(Math.floor(Math.random() * 3500) + 800)
    }
  } else {
    pointCount = 5

    for (let i = 0; i < pointCount; i++) {
      const date = new Date()
      date.setFullYear(currentDate.getFullYear() - (pointCount - 1) + i)

      categories.push(`${date.getFullYear()}`)

      inventoryData.push({
        y: Math.floor(Math.random() * 20000) + 10000,
        color: '#155EEF',
      })

      saleData.push(Math.floor(Math.random() * 15000) + 5000)
      forecastData.push(Math.floor(Math.random() * 18000) + 8000)
    }
  }

  const series = [
    {
      name: 'Inventory Quantity',
      type: 'column',
      data: inventoryData,
    },
    {
      name: 'Sale Volume',
      type: 'spline',
      data: saleData,
      color: '#099250',
      yAxis: 1,
    },
    {
      name: 'Forecast Sale Volume',
      type: 'spline',
      data: forecastData,
      color: '#DC6803',
      yAxis: 1,
    },
  ]

  return {
    categories,
    series,
  }
}

const chartOptions = computed<Options>(() => {
  return {
    chart: {
      type: 'column',
      zoomType: 'xy',
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: mockData.value?.categories ?? [],
      crosshair: true,
    },
    yAxis: [
      {
        title: {
          text: 'Inventory Quantity',
        },
      },
      {
        title: {
          text: 'Sale Volumes',
        },
        opposite: true,
      },
    ],
    plotOptions: {
      spline: {
        marker: {
          enabled: false,
        },
      },
    },
    series: mockData.value?.series ?? [],
    tooltip: {
      shared: true,
    },
    credits: {
      enabled: false,
    },
    accessibility: {
      enabled: false,
    },
  }
})
</script>

<template>
  <Chart ref="highcharts" :options="chartOptions"></Chart>
</template>

<style scoped></style>
