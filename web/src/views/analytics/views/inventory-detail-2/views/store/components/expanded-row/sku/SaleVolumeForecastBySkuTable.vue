<script setup lang="ts">
import { type ColumnType } from 'ant-design-vue/es/table'
import { onMounted, ref, watch } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import { type PivotTableResponse } from '@/views/analytics/views/inventory-detail-2/views/forecast/components/expanded-row/forecast-by-store/ForecastByStoreData.ts'
import { convertToAntTableFormat } from '@/views/analytics/views/inventory-detail-2/views/store/components/expanded-row/sku/skuMockData.ts'

type Props = {
  pivotData: PivotTableResponse
}

const props = defineProps<Props>()

const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}

const columns = ref<any[]>([])
const dataSource = ref<any[]>([])

onMounted(() => {
  const { columns: newColumns, dataSource: newDataSource } = convertToAntTableFormat(
    props.pivotData,
  )

  columns.value = newColumns
  dataSource.value = newDataSource
})

watch(
  () => props.pivotData,
  () => {
    const { columns: updatedColumns, dataSource: updatedDataSource } = convertToAntTableFormat(
      props.pivotData,
    )

    columns.value = updatedColumns
    dataSource.value = updatedDataSource
  },
)

const paginationConfig: TablePaginationConfig = {
  current: 1,
  pageSize: 10,
  total: props.pivotData.rows.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'default',
  position: ['bottomRight'],
}
</script>

<template>
  <div class="inventory-dc__table table-hover-effects" ref="tableContainerRef">
    <ATable
      :scroll="{ x: 'max-content' }"
      size="small"
      :columns="columns"
      :data-source="dataSource"
      :pagination="paginationConfig"
      @resizeColumn="handleResizeColumn"
    >
    </ATable>
  </div>
</template>

<style scoped>
.inventory-dc__table :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.inventory-dc__table :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.inventory-dc__table :deep(.ant-table) {
  margin-inline: 0 !important;
}
</style>
