<script setup lang="ts">

import Tab from '@/views/analytics/views/inventory-detail-2/components/Tab.vue'

const tabs = [
  { id: 'by-category', label: 'Forecast theo category', component: () => import('./category/SaleVolumeForecastByCategory.vue') },
  { id: 'by-sku', label: 'Forecast theo SKU', component: () => import('./sku/SaleVolumeForecastBySku.vue') },
]

const handleTabChange = (tabId: string) => {
  console.log('Tab changed to:', tabId)
}
</script>

<template>
  <Tab :tabs="tabs" defaultTab="by-category" @tab-change="handleTabChange" />
</template>
