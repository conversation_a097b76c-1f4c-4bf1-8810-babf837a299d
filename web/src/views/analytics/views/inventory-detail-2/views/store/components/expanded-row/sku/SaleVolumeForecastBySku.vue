<script setup lang="ts">
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import { ref, watch, onMounted } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import SaleVolumeForecastBySkuTable from '@/views/analytics/views/inventory-detail-2/views/store/components/expanded-row/sku/SaleVolumeForecastBySkuTable.vue'
import { usePivotData } from '../../../../../composables/useInventoryData'

const dateHistogram = ref(DateHistogram.DayOf)

// Use the composable for data management
const pivotData = usePivotData({
  dateHistogram: dateHistogram.value.toString()
})

// Load data on mount
onMounted(() => {
  pivotData.execute()
})

// Watch for date histogram changes and reload data
watch(dateHistogram, (newHistogram) => {
  pivotData.execute()
})
</script>

<template>
  <div class="sales-performance-by-sku">
    <div class="sales-performance-by-sku__header">
      <h6>Lịch sử sức bán dự đoán</h6>
      <div class="sales-performance-by-sku__actions">
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="pivotData.loading.value" class="loading-container">
      <ASpin size="large" />
      <p>Loading forecast data...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="pivotData.error.value" class="error-container">
      <AAlert
        type="error"
        :message="pivotData.error.value.message"
        show-icon
      >
        <template #action>
          <AButton size="small" @click="pivotData.refresh()">
            Retry
          </AButton>
        </template>
      </AAlert>
    </div>

    <!-- Data table -->
    <SaleVolumeForecastBySkuTable
      v-else-if="pivotData.data.value?.data"
      class="sales-performance-by-sku__table"
      :pivot-data="pivotData.data.value.data"
    />
  </div>
</template>

<style scoped>
.sales-performance-by-sku {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.sales-performance-by-sku h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.sales-performance-by-sku__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sales-performance-by-sku__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
