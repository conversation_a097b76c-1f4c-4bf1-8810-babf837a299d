<script setup lang="ts">
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import { ref } from 'vue'
import SaleVolumeForecastChart
  from '@/views/analytics/views/inventory-detail-2/views/store/components/expanded-row/category/SaleVolumeForecastChart.vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'

const dateHistogram = ref(DateHistogram.DayOf)
</script>

<template>
  <div class="sales-performance">
    <div class="sales-performance__header">
      <h6><PERSON><PERSON><PERSON> sử sức bán dự đoán</h6>
      <div class="sales-performance__actions">
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <SaleVolumeForecastChart
      class="sales-performance__chart"
      :date-histogram="dateHistogram"
    />
  </div>
</template>

<style scoped>
.sales-performance {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.sales-performance h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.sales-performance__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sales-performance__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}
</style>
