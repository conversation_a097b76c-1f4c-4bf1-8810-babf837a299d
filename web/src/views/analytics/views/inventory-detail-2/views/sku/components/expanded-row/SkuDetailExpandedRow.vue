<script setup lang="ts">

import Tab from '@/views/analytics/views/inventory-detail-2/components/Tab.vue'

const tabs = [
  { id: 'sales', label: 'Sales performance', component: () => import('./sale-performance/SalesPerformance.vue') },
  { id: 'dc', label: 'Inventory DC', component: () => import('./inventory-dc/InventoryDC.vue') },
  { id: 'store', label: 'Inventory Store', component: () => import('./inventory-store/InventoryStore.vue') },
]

const handleTabChange = (tabId: string) => {
  console.log('Tab changed to:', tabId)
}
</script>

<template>
  <Tab :tabs="tabs" defaultTab="sales" @tab-change="handleTabChange" />
</template>
