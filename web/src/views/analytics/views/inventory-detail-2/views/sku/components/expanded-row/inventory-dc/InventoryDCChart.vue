<script setup lang="ts">
import type { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { Chart } from 'highcharts-vue'
import { computed, ref, watch, onMounted } from 'vue'
import Highcharts from 'highcharts'
import { useChartData } from '../../../../../composables/useInventoryData'
import type { ChartDataPoint } from '../../../../../types/index'

type Props = {
  channel: 'online' | 'offline'
  dateHistogram: DateHistogram
}

const props = defineProps<Props>()

// Use the composable for data management
const chartData = useChartData({
  type: 'inventory-dc',
  channel: props.channel,
  histogram: props.dateHistogram.toString()
})

// Load data on mount
onMounted(() => {
  chartData.execute()
})

// Watch for prop changes and reload data
watch([() => props.channel, () => props.dateHistogram], ([newChannel, newHistogram]) => {
  // Create new request with updated parameters
  const newRequest = {
    type: 'inventory-dc' as const,
    channel: newChannel,
    histogram: newHistogram.toString()
  }
  // Note: We would need to update the composable to accept new parameters
  // For now, we'll just re-execute with the existing parameters
  chartData.execute()
}, { deep: true })

const highcharts = ref<Highcharts.Chart>('highcharts' as unknown as Highcharts.Chart)

const chartOptions = computed<Highcharts.Options>(() => {
  const data = chartData.data.value?.data as ChartDataPoint[] || []

  return {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 350,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: data.map(item => item.date),
      crosshair: true,
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Inventory Value',
      },
    },
    tooltip: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>{point.y}</b></td></tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true,
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
      },
    },
    series: [
      {
        name: 'Inventory DC',
        type: 'column',
        data: data.map(item => ({
          y: item.value || 0,
          color: item.isWeekend ? '#EAAA08' : '#155EEF' // Yellow for weekend, Green for weekday
        })),
      },
    ],
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
  }
})
</script>

<template>
  <!-- Loading state -->
  <div v-if="chartData.loading.value" class="loading-container">
    <ASpin size="large" />
    <p>Loading chart data...</p>
  </div>

  <!-- Error state -->
  <div v-else-if="chartData.error.value" class="error-container">
    <AAlert
      type="error"
      :message="chartData.error.value.message"
      show-icon
    >
      <template #action>
        <AButton size="small" @click="chartData.refresh()">
          Retry
        </AButton>
      </template>
    </AAlert>
  </div>

  <!-- Chart -->
  <Chart v-else ref="highcharts" :options="chartOptions"></Chart>
</template>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  height: 350px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
