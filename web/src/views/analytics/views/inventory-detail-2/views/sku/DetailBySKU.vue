<script setup lang="ts">
import SkuDetailTable from '@/views/analytics/views/inventory-detail-2/views/sku/components/SkuDetailTable.vue'
import Overview from '@/views/analytics/views/inventory-detail-2/components/overview/Overview.vue'
</script>

<template>
  <div id="view-by-sku">
    <Overview />
    <SkuDetailTable />
  </div>
</template>

<style scoped>
#view-by-sku {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
