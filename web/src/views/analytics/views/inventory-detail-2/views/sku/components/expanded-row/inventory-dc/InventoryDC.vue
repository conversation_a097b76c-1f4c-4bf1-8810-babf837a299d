<script setup lang="ts">

import SaleChannelSwitch from '@/views/analytics/views/inventory-detail-2/components/SaleChannelSwitch.vue'
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import InventoryDCChart
  from '@/views/analytics/views/inventory-detail-2/views/sku/components/expanded-row/inventory-dc/InventoryDCChart.vue'
import { ref } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'

const saleChannel = ref<'online' | 'offline'>('online')
const dateHistogram = ref(DateHistogram.DayOf)

</script>

<template>
  <div class="inventory-dc">
    <div class="inventory-dc__header">
      <h6>Inventory DC</h6>
      <div class="inventory-dc__actions">
        <SaleChannelSwitch v-model:channel="saleChannel" />
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <InventoryDCChart
      class="inventory-dc__chart"
      :channel="saleChannel"
      :date-histogram="dateHistogram"
    />
  </div>
</template>
<style scoped>
.inventory-dc {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.inventory-dc h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.inventory-dc__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.inventory-dc__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}
</style>
