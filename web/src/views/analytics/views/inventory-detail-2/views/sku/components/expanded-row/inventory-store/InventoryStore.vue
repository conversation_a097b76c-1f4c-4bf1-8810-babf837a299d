<script setup lang="ts">
import StatusCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/StatusCell.vue'
import { type ColumnType } from 'ant-design-vue/es/table'
import { computed, ref, onMounted } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import { useStoreInventoryPaginated } from '../../../../../composables/useInventoryData'
import type { StoreInventoryData } from '@/views/analytics/views/inventory-detail-2/types'

const columns = ref<ColumnType<StoreInventoryData>[]>([
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 60,
    align: 'center',
    fixed: 'left',
  },
  {
    title: 'Store name',
    dataIndex: 'storeName',
    key: 'storeName',
    width: 280,
    resizable: true,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: 'Inventory quantity',
    dataIndex: 'inventoryQuantity',
    key: 'inventoryQuantity',
    width: 150,
    align: 'right',
  },
  {
    title: 'Inventory value',
    dataIndex: 'inventoryValue',
    key: 'inventoryValue',
    width: 140,
    align: 'right',
  },
  {
    title: 'Day on Hand (DOH)',
    dataIndex: 'dayOnHand',
    key: 'dayOnHand',
    width: 160,
    align: 'center',
  },
  {
    title: 'Sales volume',
    dataIndex: 'salesVolume',
    key: 'salesVolume',
    width: 130,
    align: 'right',
  },
  {
    title: 'Gross profit',
    dataIndex: 'grossProfit',
    key: 'grossProfit',
    width: 130,
    align: 'right',
  },
  {
    title: 'OOS',
    dataIndex: 'oos',
    key: 'oos',
    width: 80,
    align: 'center',
  },
  {
    title: 'Sales velocity',
    dataIndex: 'salesVelocity',
    key: 'salesVelocity',
    width: 140,
    align: 'right',
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 120,
    align: 'center',
  },
])

const getBackgroundColorStatus = (text: string) => {
  if (text.includes('Good')) {
    return '#EDFCF2'
  }

  if (text.includes('Not good')) {
    return '#FFF1F3'
  }
  if (text.includes('Đã nhận')) {
    return '#EDFCF2'
  }

  if (text.includes('Đang chuyển')) {
    return '#EFF4FF'
  }
  return 'white'
}

const getColorStaus = (text: string) => {
  if (text.includes('Good')) {
    return '#087443'
  }

  if (text.includes('Not good')) {
    return '#C01048'
  }
  if (text.includes('Đã nhận')) {
    return '#087443'
  }

  if (text.includes('Đang chuyển')) {
    return '#004EEB'
  }
  return 'black'
}

// Use the composable for data management
const storeInventory = useStoreInventoryPaginated({
  pageSize: 10
})

// Load data on mount
onMounted(() => {
  storeInventory.execute()
})

const dataSource = computed(() => {
  return storeInventory.items.value
})


const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}

const paginationConfig = computed<TablePaginationConfig>(() => ({
  current: storeInventory.currentPage.value,
  pageSize: storeInventory.pageSize.value,
  total: storeInventory.total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'default',
  position: ['bottomRight'],
  onChange: (page: number, pageSize: number) => {
    storeInventory.setPage(page)
  },
  onShowSizeChange: (current: number, size: number) => {
    storeInventory.setPageSize(size)
  }
}));
</script>

<template>
  <div class="inventory-dc__table table-hover-effects" ref="tableContainerRef">
    <!-- Loading state -->
    <div v-if="storeInventory.loading.value" class="loading-container">
      <ASpin size="large" />
      <p>Loading store inventory...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="storeInventory.error.value" class="error-container">
      <AAlert
        type="error"
        :message="storeInventory.error.value.message"
        show-icon
      >
        <template #action>
          <AButton size="small" @click="storeInventory.refresh()">
            Retry
          </AButton>
        </template>
      </AAlert>
    </div>

    <!-- Data table -->
    <ATable
      v-else
      :scroll="{ x: 'max-content' }"
      size="small"
      :columns="columns"
      :data-source="dataSource"
      :pagination="paginationConfig"
      @resizeColumn="handleResizeColumn"
    >
      <template #status="{ text }">
        <StatusCell
          :status="text"
          :background-color="getBackgroundColorStatus(text)"
          :color="getColorStaus(text)"
        />
      </template>
    </ATable>
  </div>
</template>

<style scoped>
.inventory-dc__table :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}
.inventory-dc__table :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.inventory-dc__table :deep(.ant-table) {
  margin-inline: 0 !important ;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
