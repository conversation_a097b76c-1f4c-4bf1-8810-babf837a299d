<script setup lang="ts">
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'


const dateRange = defineModel('dateRange', { required: true, default: DateRange.LAST_7_DAYS })

const DateRangeLabels: [DateRange, string][] = [
  [DateRange.LAST_7_DAYS, '7 ngày gần nhất'],
  [DateRange.LAST_30_DAYS, '30 ngày gần nhất'],
  [DateRange.MONTH_TO_DATE, 'Từ đầu tháng đến nay'],
]
</script>

<template>
  <ARadioGroup v-model:value="dateRange">
    <template v-for="label in DateRangeLabels" :key="label[0]">
      <ARadioButton :value="label[0]">
        {{ label[1] }}
      </ARadioButton>
    </template>
  </ARadioGroup>
</template>

<style scoped></style>
