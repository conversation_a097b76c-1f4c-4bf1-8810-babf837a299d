<script setup lang="ts">
import type { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { Chart } from 'highcharts-vue'
import { computed, watch, ref, onMounted } from 'vue'
import Highcharts from 'highcharts'

type Props = {
  channel: 'online' | 'offline'
  dateHistogram: DateHistogram
}

const props = defineProps<Props>()
const mockData = ref()//TODO: Change to service
const highcharts = ref<Highcharts.Chart>('highcharts' as unknown as Highcharts.Chart)
onMounted(() => {
  mockData.value = generateMockData(props.channel, props.dateHistogram)
})
watch(
  () => props.channel,
  () => {
    mockData.value = generateMockData(props.channel, props.dateHistogram)
  },
)
watch(
  () => props.dateHistogram,
  () => {
    mockData.value = generateMockData(props.channel, props.dateHistogram)
  },
)

const generateMockData = (channel: string, histogram: string) => {
  const data = []
  const soLuongData = []
  const promotionData = []
  const dates = []
  const now = new Date()

  const points = histogram === 'day_of' ? 30 : histogram === 'week_of' ? 12 : 6

  for (let i = 0; i < points; i++) {
    const value = Math.floor(Math.random() * 1000) + 500
    data.push(value)

    // Thêm dữ liệu ngẫu nhiên cho Số lượng và Promotion
    soLuongData.push(Math.floor(Math.random() * 200) + 50)
    promotionData.push(Math.floor(Math.random() * 100) + 20)

    const date = new Date(now)
    if (histogram === 'day_of') date.setDate(date.getDate() - i)
    else if (histogram === 'week_of') date.setDate(date.getDate() - i * 7)
    else date.setMonth(date.getMonth() - i)

    dates.push(date.toISOString().split('T')[0])
  }

  return {
    data: data.reverse(),
    soLuongData: soLuongData.reverse(),
    promotionData: promotionData.reverse(),
    categories: dates.reverse(),
  }
}

const chartOptions = computed<Highcharts.Options>(() => {
  return {
    chart: {
      type: 'column',
      zoomType: 'xy',
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: mockData.value?.categories ?? [],
      crosshair: true,
    },
    yAxis: [
      {
        // Trục y chính cho cột Sales
        title: {
          text: 'Sales Volume',
          style: {
            color: '#2F54EB',
          },
        },
        labels: {
          style: {
            color: '#2F54EB',
          },
        },
      },
      {
        // Trục y phụ cho Số lượng và Promotion
        title: {
          text: 'Số lượng / Promotion',
          style: {
            color: '#FF0000',
          },
        },
        labels: {
          style: {
            color: '#FF0000',
          },
        },
        opposite: true,
      },
    ],
    series: [
      {
        name: `${props.channel} Sales`,
        type: 'column',
        data: mockData.value?.data,
        color: '#2F54EB',
        yAxis: 0,
      },
      {
        name: 'Số lượng',
        type: 'line',
        data: mockData.value?.soLuongData,
        color: 'rgba(60, 203, 127, 1)',
        yAxis: 1,
        marker: {
          enabled: false,
        },
      },
      {
        name: 'Promotion',
        type: 'line',
        data: mockData.value?.promotionData,
        color: 'rgba(220, 104, 3, 1)',
        yAxis: 1,
        marker: {
          enabled: false,
        },
      },
    ],
    tooltip: {
      shared: true,
    },
    credits: {
      enabled: false,
    },
    accessibility: {
      enabled: false,
    },
  }
})
</script>

<template>
  <Chart ref="highcharts" :options="chartOptions"></Chart>
</template>

<style scoped></style>
