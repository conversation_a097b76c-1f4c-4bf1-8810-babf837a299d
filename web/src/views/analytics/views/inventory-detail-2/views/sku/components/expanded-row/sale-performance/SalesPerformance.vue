<script setup lang="ts">
import SaleChannelSwitch from '@/views/analytics/views/inventory-detail-2/components/SaleChannelSwitch.vue'
import DateHistogramSelect from '@/views/analytics/views/inventory-detail-2/components/DateHistogramSelect.vue'
import SalesPerformanceChart from '@/views/analytics/views/inventory-detail-2/views/sku/components/expanded-row/sale-performance/SalesPerformanceChart.vue'
import { ref } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'

const saleChannel = ref<'online' | 'offline'>('online')
const dateHistogram = ref(DateHistogram.DayOf)

</script>

<template>
  <div class="sales-performance">
    <div class="sales-performance__header">
      <h6>Sales performance</h6>
      <div class="sales-performance__actions">
        <SaleChannelSwitch v-model:channel="saleChannel" />
        <DateHistogramSelect v-model:date-histogram="dateHistogram" />
      </div>
    </div>

    <SalesPerformanceChart
      class="sales-performance__chart"
      :channel="saleChannel"
      :date-histogram="dateHistogram"
    />
  </div>
</template>

<style scoped>
.sales-performance {
  min-height: 434px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
}

.sales-performance h6 {
  font-weight: 700;
  font-family: Inter, sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 0;
}

.sales-performance__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sales-performance__actions {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}
</style>
