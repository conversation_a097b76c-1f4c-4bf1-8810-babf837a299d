<script setup lang="ts">
import SkuDetailActions from '@/views/analytics/views/inventory-detail-2/views/sku/components/SkuDetailActions.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import { type SKUData } from '@/views/analytics/views/inventory-detail-2/views/sku/components/SKUData.ts'
import { h, ref, onMounted, computed, watch } from 'vue'
import { useSKUDataPaginated } from '../../../composables/useInventoryData'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import { Button } from 'ant-design-vue'
import { ExportOutlined } from '@ant-design/icons-vue'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'
import SkuDetailExpandedRow from '@/views/analytics/views/inventory-detail-2/views/sku/components/expanded-row/SkuDetailExpandedRow.vue'
import type { ColumnType } from 'ant-design-vue/es/table'
import ImageCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ImageCell.vue'

const expandedRows = ref<string[]>([])
const tableContainerRef = ref<HTMLDivElement>()
useTableHoverEffects(tableContainerRef)

// Use the composable for data management
const skuData = useSKUDataPaginated({
  pageSize: 10
})

// Load data on mount
onMounted(() => {
  skuData.execute()
})

const dataSource = computed(() => {
  return skuData.items.value
})

const columns = ref([
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    title: '',
    dataIndex: 'thumbnail',
    key: 'thumbnail',
    width: 52,
    fixed: 'left',
    align: 'center',
    customRender: ({ record }: { record: { thumbnail: string } }) => {
      return h(ImageCell, {
        imageUrl: record.thumbnail,
      })
    },
  },
  {
    title: 'Product name',
    dataIndex: 'productName',
    key: 'productName',
    minWidth: 200,
    width: 200,
    fixed: 'left',
    resizable: true,
    ellipsis: true,
    customRender: ({ text, record }: { text: string; record: SKUData }) => {
      const isExpanded = expandedRows.value.includes(record.key)
      return h(ProductNameCell, {
        productName: text,
        link: record.link,
        expanded: {
          enabled: true,
          isExpanded: isExpanded,
        },
      })
    },
  },
  {
    title: 'Sales performance',
    align: 'left',
    children: [
      {
        title: 'Doanh thu',
        dataIndex: 'revenue',
        key: 'revenue',
        minWidth: 120,
        align: 'right',
      },
      {
        title: 'Số bán',
        dataIndex: 'soldQuantity',
        key: 'soldQuantity',
        minWidth: 120,
        align: 'right',
      },

      {
        title: 'Số đơn hàng bán',
        dataIndex: 'avgSellingPrice',
        key: 'avgSellingPrice',
        width: 172,
        align: 'right',
      },
      {
        title: 'Số đơn',
        dataIndex: 'orders',
        key: 'orders',
        minWidth: 120,
        align: 'right',
      },
      {
        title: 'Số bán trung bình',
        dataIndex: 'avgOrderValue',
        key: 'avgOrderValue',
        width: 190,
        align: 'right',
      },
    ],
  },
  {
    title: 'Inventory performance',
    align: 'left',
    children: [
      {
        title: 'DC',
        dataIndex: 'dc',
        key: 'dc',
        minWidth: 120,
        align: 'center',
      },
      {
        title: 'Store',
        dataIndex: 'store',
        key: 'store',
        minWidth: 120,
        align: 'right',
      },
    ],
  },
  {
    title: 'Sales performance (Day)',
    align: 'left',
    children: [
      {
        title: 'Sale volume',
        dataIndex: 'saleVolume',
        key: 'saleVolume',
        width: 120,
        align: 'right',
      },
      {
        title: 'Sale value',
        dataIndex: 'saleValue',
        key: 'saleValue',
        width: 120,
        align: 'right',
      },
      {
        title: 'Gross profit',
        dataIndex: 'grossProfit',
        key: 'grossProfit',
        width: 150,
        align: 'right',
      },
    ],
  },
  {
    title: '',
    key: 'actions',
    fixed: 'right',
    width: 55,
    align: 'center',
    customRender: ({ record }: { record: SKUData }) => {
      return h(Button, {
        type: 'text',
        size: 'small',
        icon: h(ExportOutlined),
        onClick: (event: Event) => {
          event.stopPropagation()
          console.log('Action 2:', record.productName)
        },
      })
    },
  },
])

const handleExpandRow = (expanded: boolean, record: SKUData) => {
  if (expanded) {
    expandedRows.value.push(record.key)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.key)
  }
}

const handleResizeColumn = (w: number, col: ColumnType) => {
  col.width = w
}

const dateRange = ref(DateRange.LAST_7_DAYS)

watch(dateRange, () => {
  skuData.execute()
})
</script>

<template>
  <div class="sku-detail-table">
    <div class="sku-detail_header">
      <ATypographyTitle :level="5">SKU detail</ATypographyTitle>
      <SkuDetailActions v-model:date-range="dateRange" />
    </div>
    <div class="sku-detail-table__body table-hover-effects" ref="tableContainerRef">
      <!-- Loading state -->
      <div v-if="skuData.loading.value" class="loading-container">
        <ASpin size="large" />
        <p>Loading SKU data...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="skuData.error.value" class="error-container">
        <AAlert
          type="error"
          :message="skuData.error.value.message"
          show-icon
        >
          <template #action>
            <AButton size="small" @click="skuData.refresh()">
              Retry
            </AButton>
          </template>
        </AAlert>
      </div>

      <!-- Data table -->
      <ATable
        v-else
        :scroll="{ x: 'max-content' }"
        size="small"
        :columns="columns"
        :data-source="dataSource"
        :expandIcon="() => null"
        expandRowByClick
        @expand="handleExpandRow"
        :show-expand-column="false"
        @resizeColumn="handleResizeColumn"
      >
        <template #expandedRowRender="{ record }">
          <SkuDetailExpandedRow :sku="record" />
        </template>
      </ATable>
    </div>
  </div>
</template>

<style scoped>
.sku-detail-table {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: calc(100vw - 64px);
}

.sku-detail_header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.sku-detail-table__body :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.sku-detail-table__body :deep(.ant-table-expanded-row-fixed) {
  background: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  padding: 20px;
}
</style>
