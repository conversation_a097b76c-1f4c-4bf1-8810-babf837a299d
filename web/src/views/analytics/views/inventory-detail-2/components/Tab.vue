<script setup lang="ts">
import { ref, defineAsyncComponent, shallowRef, watch } from 'vue'

interface TabItem {
  id: string
  label: string
  component: () => Promise<any>
}

interface Props {
  tabs: TabItem[]
  defaultTab?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['tabChange'])

const activeTabId = ref(props.defaultTab || (props.tabs.length > 0 ? props.tabs[0].id : ''))
const currentComponent = shallowRef(
  props.tabs.length > 0
    ? defineAsyncComponent(props.tabs.find(t => t.id === activeTabId.value)?.component || props.tabs[0].component)
    : null
)

watch(activeTabId, (newTab) => {
  const tab = props.tabs.find(t => t.id === newTab)
  if (tab) {
    currentComponent.value = defineAsyncComponent(tab.component)
    emit('tabChange', newTab)
  }
})
</script>

<template>
  <div class="tab-wrapper">
    <div class="tab-nav">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="['tab-button', { active: tab.id === activeTabId }]"
        @click="activeTabId = tab.id"
      >
        {{ tab.label }}
      </button>
    </div>

    <div class="tab-content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<style scoped>
.tab-wrapper {
  width: 100%;
  font-family: Inter, sans-serif;
  background: transparent;
}

.tab-nav {
  display: flex;
  gap: 20px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.tab-button {
  padding-bottom: 12px;
  border: none;
  border-bottom: 2px solid transparent;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #717680;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #4b5563;
  border-color: #d1d5db;
}

.tab-button.active {
  color: #2f54eb;
  border-color: #2f54eb;
}

.tab-content {
  margin-top: 16px;
}
</style>
