.card {
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: sans-serif;
  background-color: #fff;
  box-shadow: 0 0 3px 0 rgba(16, 24, 40, 0.15);
  transition: border 0.2s ease;
  border: 2px solid transparent;
}

.card:hover {
  border: 2px solid var(--primary-color);
}

.header {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 14px;
}

.icon {
  font-size: 14px;
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  font-size: 18px;
}

.value {
  color: var(--primary-color);
  font-size: 28px;
  font-weight: 600;
}

.change {
  color: #dc3545;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 2px;
}
