<script setup lang="ts">

</script>

<template>
  <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="14" height="14" transform="translate(0.599609)" fill="white" style="mix-blend-mode:multiply"/>
    <path d="M6.72461 7.875H4.09961V8.75H6.72461V7.875Z" fill="#D0D5DD"/>
    <path d="M8.47461 9.625H4.09961V10.5H8.47461V9.625Z" fill="#D0D5DD"/>
    <path d="M11.9746 1.75H3.22461C2.99263 1.75029 2.77024 1.84257 2.60621 2.0066C2.44218 2.17063 2.3499 2.39302 2.34961 2.625V11.375C2.3499 11.607 2.44218 11.8294 2.60621 11.9934C2.77024 12.1574 2.99263 12.2497 3.22461 12.25H11.9746C12.2066 12.2497 12.429 12.1574 12.593 11.9934C12.757 11.8294 12.8493 11.607 12.8496 11.375V2.625C12.8493 2.39302 12.757 2.17063 12.593 2.0066C12.429 1.84257 12.2066 1.75029 11.9746 1.75ZM8.47461 2.625V4.375H6.72461V2.625H8.47461ZM3.22461 11.375V2.625H5.84961V5.25H9.34961V2.625H11.9746L11.9751 11.375H3.22461Z" fill="#D0D5DD"/>
  </svg>

</template>

<style scoped>

</style>
