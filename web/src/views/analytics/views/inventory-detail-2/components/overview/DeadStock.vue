<template>
  <div class="card">
    <div class="header">
      <NonMovingIcon/>
      <span class="label">Dead Stock</span>
    </div>
    <div class="content">
      <span v-if="overviewMetrics.loading.value" class="value">-</span>
      <span v-else-if="overviewMetrics.error.value" class="value error">Error</span>
      <span v-else class="value">{{ overviewMetrics.data.value?.data.deadStock || 0 }}</span>
      <span class="change">-2.1% <span class="arrow">↓</span></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import NonMovingIcon from '@/views/analytics/views/inventory-detail-2/components/overview/icons/NonMovingIcon.vue'
import { useOverviewMetrics } from '../../composables/useInventoryData'
import { onMounted } from 'vue'

// Use the composable for data management
const overviewMetrics = useOverviewMetrics()

// Load data on mount
onMounted(() => {
  overviewMetrics.execute()
})
</script>
<style scoped>
.card {
  --primary-color: rgba(41, 112, 255, 1);
}

.value.error {
  color: #ff4d4f;
  font-size: 12px;
}
</style>
<style scoped src="./card.css"></style>
