<script setup lang="ts">
import Alerts from '@/views/analytics/views/inventory-detail-2/components/overview/Alerts.vue'
import LastSales from '@/views/analytics/views/inventory-detail-2/components/overview/LastSales.vue'
import DeadStock from '@/views/analytics/views/inventory-detail-2/components/overview/DeadStock.vue'
import NonMoving from '@/views/analytics/views/inventory-detail-2/components/overview/NonMoving.vue'
import LowStock from '@/views/analytics/views/inventory-detail-2/components/overview/LowStock.vue'
</script>

<template>
  <div class="overview">
    <Alerts />
    <LastSales />
    <DeadStock />
    <NonMoving />
    <LowStock />
  </div>
</template>

<style scoped>
.overview {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

@media (max-width: 768px) {
  .overview {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1366px) {
  .overview {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
