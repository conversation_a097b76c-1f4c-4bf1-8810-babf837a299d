<template>
  <div class="card">
    <div class="header">
      <AlertIcon />
      <span class="label">Alerts</span>
    </div>
    <div class="content">
      <span v-if="overviewMetrics.loading.value" class="value">-</span>
      <span v-else-if="overviewMetrics.error.value" class="value error">Error</span>
      <span v-else class="value">{{ overviewMetrics.data.value?.data.alerts || 0 }}</span>
      <InfoCircleOutlined />
    </div>
  </div>
</template>

<script setup lang="ts">
import AlertIcon from './icons/AlertIcon.vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { useOverviewMetrics } from '../../composables/useInventoryData'
import { onMounted } from 'vue'

// Use the composable for data management
const overviewMetrics = useOverviewMetrics()

// Load data on mount
onMounted(() => {
  overviewMetrics.execute()
})
</script>
<style scoped>
.card {
  --primary-color: rgba(247, 144, 9, 1);
}

.value.error {
  color: #ff4d4f;
  font-size: 12px;
}
</style>
<style scoped src="./card.css"></style>
