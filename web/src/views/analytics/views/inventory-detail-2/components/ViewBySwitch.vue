<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const viewByValue = computed(() => route.name)

const onSwitchChange = (key: string) => {
  console.log('onSwitchChange', key)
  router.push({ name: key }) // tab1 -> Tab1
}

const ViewByLabels: [string, string][] = [
  ['ViewBySKU', 'Chiều SKU'],
  ['ViewByForecast', 'Chiều Forecast'],
  ['ViewByStore', 'Chiều Store'],
]
</script>

<template>
  <ARadioGroup :value="viewByValue">
    <template v-for="label in ViewByLabels" :key="label[0]">
      <ARadioButton :value="label[0]" @change="() => onSwitchChange(label[0])">
        {{ label[1] }}
      </ARadioButton>
    </template>
  </ARadioGroup>
</template>

<style scoped></style>
