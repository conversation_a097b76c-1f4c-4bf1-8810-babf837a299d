<script setup lang="ts">
type SaleChannel = 'online' | 'offline'

const channel = defineModel('channel', { required: true, default: 'online' })

const SaleChannelLabels: [SaleChannel, string][] = [
  ['online', 'Online'],
  ['offline', 'Offline'],
]
</script>

<template>
  <ARadioGroup v-model:value="channel">
    <template v-for="label in SaleChannelLabels" :key="label[0]">
      <ARadioButton :value="label[0]">
        {{ label[1] }}
      </ARadioButton>
    </template>
  </ARadioGroup>
</template>

<style scoped></style>
