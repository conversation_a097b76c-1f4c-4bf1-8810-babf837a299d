<script setup lang="ts">
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'

const dateHistogram = defineModel('dateHistogram', { required: true, default: DateHistogram.DayOf })

const options = [
  {
    value: DateHistogram.DayOf,
    label: 'Day',
  },
  {
    value: DateHistogram.WeekOf,
    label: 'Week',
  },
  {
    value: DateHistogram.MonthOf,
    label: 'Month',
  },
]
</script>

<template>
  <ASelect
    ref="select"
    placeholder="Select a date histogram"
    v-model:value="dateHistogram"
    style="width: 300px"
    :options="options"
    allow-clear
  ></ASelect>
</template>

<style scoped></style>
