// Common types for inventory detail 2 module

export interface StoreInventoryData {
  key: string;
  id: number;
  storeName: string;
  storeCode: string;
  inventoryQuantity: number;
  inventoryValue: number;
  dayOnHand: number;
  salesVolume: number;
  grossProfit: number;
  oos: number;
  salesVelocity: number;
  status: 'Last sale' | 'Active' | 'Inactive';
  pinned?: boolean;
}

export interface SKUForecastData {
  id: string;
  key: string;
  productName: string;
  thumbnail: string;
  forecastNext3Day: number;
  forecastNext7Day: number;
  forecastNext1Month: number;
  forecastNext3Month: number;
  forecastNext1Year: number;
}

export interface SKUData {
  key: string;
  id: number;
  productName: string;
  productCode: string;
  thumbnail?: string;
  link?: string;
  revenue: string;
  soldQuantity: number;
  avgSellingPrice: number;
  orders: string;
  avgOrderValue: string;
  dc: number;
  store: string;
  saleVolume: string;
  saleVolumeChart: number[];
  saleValue: string;
  saleValueChart: number[];
  grossProfit: string;
  grossProfitPercent: string;
}

export interface PivotColumn {
  id: string;
  name: string;
}

export interface PivotRow {
  id: string;
  name: string;
  thumbnail?: string;
  link?: string;
  [id: string]: any;
}

export interface PivotTableResponse {
  columns: PivotColumn[];
  rows: PivotRow[];
}

export interface ChartDataPoint {
  date: string;
  value?: number;
  value1?: number;
  value2?: number;
  isWeekend: boolean;
  isStacked?: boolean;
}

export interface ForecastSaleVolumeData {
  data: number[];
  soLuongData: number[];
  promotionData: number[];
  dates: string[];
}

export interface OverviewMetrics {
  alerts: number;
  lastSales: number;
  deadStock: number;
  nonMoving: number;
  lowStock: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// Request types
export interface GetStoreInventoryRequest {
  storeId?: string;
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface GetSKUForecastRequest {
  skuId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface GetSKUDataRequest {
  search?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface GetPivotDataRequest {
  dateHistogram: string;
  storeId?: string;
  skuId?: string;
}

export interface GetChartDataRequest {
  type: 'inventory-dc' | 'forecast-sale-volume';
  dateRange?: {
    start: string;
    end: string;
  };
  channel?: string;
  histogram?: string;
}
