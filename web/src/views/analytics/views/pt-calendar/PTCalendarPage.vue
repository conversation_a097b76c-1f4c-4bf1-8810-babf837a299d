<script setup lang="ts">
import { onMounted } from 'vue';
import MonitorMetricSegment from './segments/monitor-metric-segment/MonitorMetricSegment.vue';
import { useStoreStore } from '../replenishment/segments/monitor-sku/stores/StoreStore';
import CalendarSegment from './segments/calendar-segment/CalendarSegment.vue';

const { fetchStores } = useStoreStore();

onMounted(() => {
  fetchStores();
});

</script>

<template>
  <div class="pt-calendar-page">
    <MonitorMetricSegment />

    <CalendarSegment />
  </div>
</template>

<style scoped>
.pt-calendar-page {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
