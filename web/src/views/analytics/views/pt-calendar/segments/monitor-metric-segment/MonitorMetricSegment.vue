<script setup lang="ts">
import MetricCard from '@/views/analytics/components/MetricCard.vue';
import { InfoCircleOutlined, ShopOutlined } from '@ant-design/icons-vue';
import { computed, h, ref, watch } from 'vue';
import MonitorMetricHeader from './components/MonitorMetricHeader.vue';
import { storeToRefs } from 'pinia';
import { useSelectedStore } from '../../../inventory-overview/stores/SelectedStoreStore';
import type { PTMonitorMetricsResponse } from '../../domains/responses/PTMonitorMetricsResponse';
import ptCalendarService from '../../services/PTCalendarService';
import { convertDateToYYYYMMDD } from '@/utils/DateUtils';
import CompareMetric from './components/CompareMetric.vue';

const { selectedStoreIds } = storeToRefs(useSelectedStore());

const todayMetrics = ref<PTMonitorMetricsResponse>({
  totalCreated: 0,
  totalWaitingForApproval: 0,
  totalTransferToday: 0,
  totalTransferNextDay: 0,
});

const yesterdayMetrics = ref<PTMonitorMetricsResponse>({
  totalCreated: 0,
  totalWaitingForApproval: 0,
  totalTransferToday: 0,
  totalTransferNextDay: 0,
});

const compareMetrics = computed(() => {
  return {
    totalWaitingForApprovalPercentage: ((todayMetrics.value.totalWaitingForApproval - yesterdayMetrics.value.totalWaitingForApproval) / yesterdayMetrics.value.totalWaitingForApproval) * 100,
    totalTransferTodayPercentage: ((todayMetrics.value.totalTransferToday - yesterdayMetrics.value.totalTransferToday) / yesterdayMetrics.value.totalTransferToday) * 100,
    totalTransferNextDayPercentage: ((todayMetrics.value.totalTransferNextDay - yesterdayMetrics.value.totalTransferNextDay) / yesterdayMetrics.value.totalTransferNextDay) * 100,
  };
});

watch(selectedStoreIds, async (newStoreIds) => {
  if (newStoreIds.length > 0) {
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    [todayMetrics.value, yesterdayMetrics.value] = await Promise.all([
      ptCalendarService.getMonitorMetrics({
        storeIds: newStoreIds,
        date: convertDateToYYYYMMDD(today),
      }),
      ptCalendarService.getMonitorMetrics({
        storeIds: newStoreIds,
        date: convertDateToYYYYMMDD(yesterday),
      })
    ]);
  } else {
    todayMetrics.value = {
      totalCreated: 0,
      totalWaitingForApproval: 0,
      totalTransferToday: 0,
      totalTransferNextDay: 0,
    };
    yesterdayMetrics.value = {
      totalCreated: 0,
      totalWaitingForApproval: 0,
      totalTransferToday: 0,
      totalTransferNextDay: 0,
    };
  }
}, { immediate: true });

</script>

<template>
  <AFlex class="metric-container" gap="small">
    <MonitorMetricHeader />

    <AFlex class="metric-body" gap="small">
      <MetricCard cardColor='#F79009'>
        <div class="card-content">
          <ATypography class="card-title">
            <ShopOutlined />
            <span style="margin-left: 6px;">Số lượng PT đã tạo</span>
          </ATypography>
          <AFlex flexDirection="row" align="center" justify="space-between">
            <div style="margin-bottom: 12px;">
              <span class="card-metric-text" :style="{ color: `#F79009`}">{{ todayMetrics.totalCreated }}</span> <span class="card-content-text">PT</span>
            </div>
            <AButton type="text" :icon="h(InfoCircleOutlined)" />
          </AFlex>
        </div>
      </MetricCard>

      <MetricCard cardColor='#0BA5EC'>
        <div class="card-content">
          <ATypography class="card-title">
            <ShopOutlined />
            <span style="margin-left: 6px;">Số lượng PT chờ duyệt</span>
          </ATypography>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <div><span class="card-metric-text" :style="{ color: `#0BA5EC`}">{{ todayMetrics.totalWaitingForApproval }}</span> <span class="card-content-text">PT</span></div>
            <CompareMetric :percentage="compareMetrics.totalWaitingForApprovalPercentage" />
          </div>
        </div>
      </MetricCard>

      <MetricCard cardColor='#2970FF'>
        <div class="card-content">
          <ATypography class="card-title">
            <ShopOutlined />
            <span style="margin-left: 6px;">Số lượng PT giao hôm nay</span>
          </ATypography>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <div><span class="card-metric-text" :style="{ color: `#2970FF`}">{{ todayMetrics.totalTransferToday }}</span> <span class="card-content-text">PT</span></div>
            <CompareMetric :percentage="compareMetrics.totalTransferTodayPercentage" />
          </div>
        </div>
      </MetricCard>

      <MetricCard cardColor='#15B79E'>
        <div class="card-content">
          <ATypography class="card-title">
            <ShopOutlined />
            <span style="margin-left: 6px;">Số lượng PT giao ngày mai</span>
          </ATypography>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <div><span class="card-metric-text" :style="{ color: `#15B79E`}">{{ todayMetrics.totalTransferNextDay }}</span> <span class="card-content-text">PT</span></div>
            <CompareMetric :percentage="compareMetrics.totalTransferNextDayPercentage" />
          </div>
        </div>
      </MetricCard>
    </AFlex>
  </AFlex>
</template>

<style scoped>
.metric-container {
  display: flex;
  flex-direction: column;
}

.metric-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  width: 100%;
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #667085;
}

.card-metric-text {
  font-size: 20px;
  font-weight: 700;
}

.card-content-text {
  font-size: 20px;
  font-weight: 700;
  color: #667085;
}

</style>
