<script setup lang="ts">
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons-vue';

const props = defineProps<{
  percentage: number;
}>();

</script>

<template>
  <ATypography v-if="!Number.isNaN(props.percentage)" :style="{ fontSize: `14px`, color: props.percentage < 0 ? 'red' : 'green', fontWeight: 500 }">
    {{ props.percentage.toFixed(1) }}% <ArrowDownOutlined v-if="props.percentage < 0" /><ArrowUpOutlined v-else />
  </ATypography>
</template>
