<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { usePTCalendarStore } from '../../../../stores/PTCalendarStore';
import { computed, ref, watch } from 'vue';
import { PTCalendarDisplay } from '../../../../enums/PTCalendarDisplay';
import { useSelectedStore } from '@/views/analytics/views/inventory-overview/stores/SelectedStoreStore';
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore';
import DeliveryTooltip from './DeliveryTooltip.vue';
import { Flex as AFlex } from 'ant-design-vue';
import { type CalendarDelivery } from '../../types/PTCalendarType';
import PTCalendarTable from './PTCalendarTable.vue';
import type { PTCalendarRow } from '../../../../domains/responses/PTCalendarRow';
import ptCalendarService from '../../../../services/PTCalendarService';
import { convertDateToYYYYMMDD } from '@/utils/DateUtils';

const { firstDayOfWeek, selectedFromStoreId } = storeToRefs(usePTCalendarStore());

// Date utils
const getWeekDays = () => {
  const days = [];
  const startDate = new Date(firstDayOfWeek.value);
  const weekdayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    days.push({
      date: convertDateToYYYYMMDD(date),
      weekday: weekdayNames[date.getDay()],
      day: date.getDate(),
      isToday: date.toDateString() === new Date().toDateString(),
    });
  }
  return days;
};

// State
const { displayType } = storeToRefs(usePTCalendarStore());
const { selectedStoreIds } = storeToRefs(useSelectedStore());
const { getStoreById } = useStoreStore();
const purchaseOrders = ref<PTCalendarRow[]>([]);

watch([firstDayOfWeek, selectedStoreIds, selectedFromStoreId], async () => {
  const fromDate = new Date(firstDayOfWeek.value);
  const toDate = new Date(fromDate);
  toDate.setDate(fromDate.getDate() + 6);

  purchaseOrders.value = await ptCalendarService.getListPTCalendar({
    storeIds: selectedStoreIds.value,
    fromStoreId: selectedFromStoreId.value,
    fromDate: convertDateToYYYYMMDD(fromDate),
    toDate: convertDateToYYYYMMDD(toDate),
  });
}, { immediate: true });

// Calendar view data
const calendarData = computed<CalendarDelivery[]>(() => {
  const weekDays = getWeekDays();
  return selectedStoreIds.value.map((id, idx) => ({
    idx: idx + 1,
    storeId: id,
    storeName: getStoreById(id)?.name || "Unknown Store",
    days: weekDays.map(day => ({
      date: day.date,
      isToday: day.isToday,
      ptOrders: purchaseOrders.value.filter(pt => {
        return pt.toStoreId === id && pt.expectedDeliveryDate === day.date;
      }),
    })),
  }));
});

</script>

<template>
  <AFlex class="body-container">
    <!-- Calendar View -->
    <div v-if="displayType === PTCalendarDisplay.Calendar" class="calendar-view">
      <div class="calendar-grid">
        <!-- Header Row -->
        <div class="calendar-header"></div>
        <div v-for="day in getWeekDays()" :key="day.date" class="calendar-header-day" :class="{ 'is-today': day.isToday }">
          <div class="day-header">
            <div class="day-weekday">{{ day.weekday }}</div>
            <div class="day-number" :class="{ 'today-number': day.isToday }">{{ day.day }}</div>
          </div>
        </div>

        <!-- Store Rows -->
        <template v-for="store in calendarData" :key="store.storeId">
          <div class="calendar-store">{{ store.storeName }}</div>
          <div
            v-for="day in store.days"
            :key="`${store.storeId}-${day.date}`"
            class="calendar-day"
            :class="{ 'is-today': day.isToday }"
          >
            <div
              v-if="day.ptOrders.length"
              class="delivery-card"
            >
              <ATypography class="delivery-title">Danh sách phiếu PT về store</ATypography>
              <div
                class="delivery-items"
                v-for="ptOrder in day.ptOrders"
                :key="ptOrder.code"
              >
                <span class="item-dot" :class="[`status-${ptOrder.status}`]"></span>
                <DeliveryTooltip :data="ptOrder" :date="day.date">
                  <span class="item-name hover-text">{{ ptOrder.code }} ({{ ptOrder.totalSku }} SKU)</span>
                </DeliveryTooltip>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- List View -->
    <div v-else class="list-view">
      <PTCalendarTable :records="calendarData"/>
    </div>
  </AFlex>
</template>

<style scoped>
.body-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Calendar View */
.calendar-view {
  background-color: white;
  width: 100%;
}

.calendar-grid {
  display: grid;
  grid-template-columns: 150px repeat(7, 1fr);
}

.calendar-header {
  grid-column: 1;
}

.calendar-header-day {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
  text-align: center;
}

.calendar-header-day.is-today {
  background-color: #e6f7ff;
  /* border-color: #91d5ff; */
}

.calendar-store {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  font-weight: 400;
  color: #667085;
}

.calendar-day {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
  min-height: 120px;
}

.calendar-day.is-today {
  background-color: #e6f7ff;
  /* border-color: #91d5ff; */
}

.day-header {
  text-align: left;
  margin-bottom: 8px;
  padding-bottom: 4px;
}

.day-weekday {
  font-weight: 400;
  font-size: 12px;
  margin-bottom: 4px;
  color: #667085;
}

.day-number {
  font-weight: 700;
  font-size: 24px;
}

.day-name {
  font-weight: 500;
  font-size: 12px;
}

.delivery-card {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  border-left: 3px solid #1677ff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-left-color: #0BA5EC;
}

.delivery-title {
  font-weight: 400;
  font-size: 10px;
}

.delivery-time {
  font-weight: 500;
  font-size: 14px;
}

.delivery-items {
  font-size: 12px;
  font-weight: 500;
}

.item-name {
  text-decoration: underline;
}

.item-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
  display: inline-block;
}

.item-dot.status-1 {
  background-color: #0BA5EC;
}

.item-dot.status-3 {
  background-color: #faad14;
}

.item-dot.status-5 {
  background-color: #52c41a;
}

.item-dot.status-7 {
  background-color: #ff4d4f;
}

.hover-text {
  cursor: pointer;
  color: #1f2937;
}

/* List View */
.list-view {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 100%;
}

</style>
