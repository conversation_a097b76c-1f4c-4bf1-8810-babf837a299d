<script setup lang="ts">
import { computed, ref } from 'vue';
import { DataDisplayLabels } from '../../../constants/DataDisplayLabels';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { usePTCalendarStore } from '../../../stores/PTCalendarStore';
import { storeToRefs } from 'pinia';
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore';

const selectedValue = ref<string>();
const { displayType, firstDayOfWeek, selectedFromStoreId } = storeToRefs(usePTCalendarStore());
const { stores } = storeToRefs(useStoreStore());

const options = computed(() =>
  stores.value.map(store => ({
    label: store.name,
    value: store.id
  }))
);

const handleChange = (value: string) => {
  selectedFromStoreId.value = value;
  console.log(`Selected value: ${value}`);
};

// Lấy ngày đầu tuần (chủ nhật) từ ngày bất kỳ
const getFirstDayOfWeek = (date: Date): Date => {
  const day = date.getDay();
  const diff = date.getDate() - day;
  return new Date(date.setDate(diff));
};

// Lấy ngày cuối tuần (thứ 7) từ ngày đầu tuần
const getLastDayOfWeek = (firstDay: Date): Date => {
  const lastDay = new Date(firstDay);
  lastDay.setDate(firstDay.getDate() + 6);
  return lastDay;
};

// Định dạng hiển thị ngày: dd/MM
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
};

// Thiết lập ngày hiện tại
const currentDate = new Date();
// Ngày đầu tuần hiện tại
const currentFirstDayOfWeek = getFirstDayOfWeek(new Date(currentDate));
// Sử dụng ref để theo dõi ngày đầu tuần đã chọn
firstDayOfWeek.value = currentFirstDayOfWeek;

// Text hiển thị khoảng thời gian của tuần
const weekRangeDisplay = computed(() => {
  const firstDay = firstDayOfWeek.value;
  const lastDay = getLastDayOfWeek(new Date(firstDay));
  return `${formatDate(firstDay)} - ${formatDate(lastDay)}`;
});

// Điều hướng đến tuần trước
const goToPreviousWeek = () => {
  const newDate = new Date(firstDayOfWeek.value);
  newDate.setDate(newDate.getDate() - 7);
  firstDayOfWeek.value = newDate;
};

// Điều hướng đến tuần sau
const goToNextWeek = () => {
  const newDate = new Date(firstDayOfWeek.value);
  newDate.setDate(newDate.getDate() + 7);
  firstDayOfWeek.value = newDate;
};

// Trở về tuần hiện tại
const goToCurrentWeek = () => {
  firstDayOfWeek.value = new Date(currentFirstDayOfWeek);
};

</script>

<template>
  <AFlex class="header-container">
    <AFlex gap="small" align="center">
      <ATypographyTitle  :level="5">Lịch hàng về</ATypographyTitle>
      <div class="center-controls">
        <AButton type="text" @click="goToPreviousWeek">
          <template #icon><LeftOutlined /></template>
        </AButton>
        <span class="week-display">{{ weekRangeDisplay }}</span>
        <AButton type="text" @click="goToNextWeek">
          <template #icon><RightOutlined /></template>
        </AButton>
        <AButton type="link" @click="goToCurrentWeek">Tuần này</AButton>
      </div>
    </AFlex>
    <AFlex gap="small" align="center">
      <ASelect
        ref="select"
        placeholder="Nơi chuyển"
        v-model:value="selectedValue"
        style="width: 300px"
        @change="handleChange"
        :options="options"
        allow-clear
      ></ASelect>
      <ARadioGroup v-model:value="displayType">
        <ARadioButton v-for="label in DataDisplayLabels" :key="label[0]" :value="label[0]">
          <div
            class="radio-button-icon-container"
            :[`data`]="displayType === label[0] ? 'active' : ''"
          >
            <component :is="label[1]" color="var(--fill-color)" />
            <a style="color: var(--fill-color)">{{ label[2] }}</a>
          </div>
        </ARadioButton>
      </ARadioGroup>
    </AFlex>
  </AFlex>
</template>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.center-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.week-display {
  font-size: 16px;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

.radio-button-icon-container {
  --fill-color: #667085;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.radio-button-icon-container[data='active'] {
  --fill-color: #155eef;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
