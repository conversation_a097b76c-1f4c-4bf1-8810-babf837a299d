import type { PTCalendarRequest } from "../domains/requests/PTCalendarRequest";
import type { PTMonitorMetricsRequest } from "../domains/requests/PTMonitorMetricsRequest";
import type { PTCalendarRow } from "../domains/responses/PTCalendarRow";
import type { PTMonitorMetricsResponse } from "../domains/responses/PTMonitorMetricsResponse";
import repository, { PTCalendarRepository } from "../repositories/PTCalendarRepository";

export abstract class PTCalendarService{
  abstract getMonitorMetrics(request: PTMonitorMetricsRequest): Promise<PTMonitorMetricsResponse>;
  abstract getListPTCalendar(request: PTCalendarRequest): Promise<PTCalendarRow[]>;
}

export class PTCalendarServiceImpl extends PTCalendarService {
  private readonly repository: PTCalendarRepository

  constructor(repository: PTCalendarRepository) {
    super()
    this.repository = repository
  }

  async getMonitorMetrics(request: PTMonitorMetricsRequest): Promise<PTMonitorMetricsResponse> {
    return this.repository.getMonitorMetrics(request);
  }

  async getListPTCalendar(request: PTCalendarRequest): Promise<PTCalendarRow[]> {
    return this.repository.getListPTCalendar(request);
  }
}

export default new PTCalendarServiceImpl(repository);
