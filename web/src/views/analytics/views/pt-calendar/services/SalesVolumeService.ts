import type { StoreSalesVolumeRequest } from "../domains/requests/StoreSalesVolumeRequest";
import type { StoreSalesVolumeRow } from "../domains/responses/StoreSalesVolumeRow";
import repository, { SalesVolumeRepository } from "../repositories/SalesVolumeRepository";

export abstract class SalesVolumeService{
  abstract getStoreSalesVolume(request: StoreSalesVolumeRequest): Promise<StoreSalesVolumeRow[]>;
}

export class SalesVolumeServiceImpl extends SalesVolumeService {
  private readonly repository: SalesVolumeRepository

  constructor(repository: SalesVolumeRepository) {
    super()
    this.repository = repository
  }

  async getStoreSalesVolume(request: StoreSalesVolumeRequest): Promise<StoreSalesVolumeRow[]> {
    return this.repository.getStoreSalesVolume(request);
  }
}

export default new SalesVolumeServiceImpl(repository);
