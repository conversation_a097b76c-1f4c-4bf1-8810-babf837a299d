import client from '@/client/HttpClientBuilder.ts'
import type { HttpClient } from '@/client/HttpClient'
import type { StoreSalesVolumeRequest } from '../domains/requests/StoreSalesVolumeRequest'
import type { StoreSalesVolumeRow } from '../domains/responses/StoreSalesVolumeRow'

export abstract class SalesVolumeRepository {
  abstract getStoreSalesVolume(request: StoreSalesVolumeRequest): Promise<StoreSalesVolumeRow[]>;
}

export class SalesVolumeRepositoryImpl extends SalesVolumeRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getStoreSalesVolume(request: StoreSalesVolumeRequest): Promise<StoreSalesVolumeRow[]> {
    const response = await this.client.post<StoreSalesVolumeRow[]>('/api/sales-volume/get-by-store', request)
    return response.data
  }
}

export default new SalesVolumeRepositoryImpl(client);
