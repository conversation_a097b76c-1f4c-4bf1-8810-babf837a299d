import client from '@/client/HttpClientBuilder.ts'
import type { HttpClient } from '@/client/HttpClient'
import type { PTMonitorMetricsRequest } from '../domains/requests/PTMonitorMetricsRequest'
import type { PTMonitorMetricsResponse } from '../domains/responses/PTMonitorMetricsResponse'
import type { PTCalendarRequest } from '../domains/requests/PTCalendarRequest'
import type { PTCalendarRow } from '../domains/responses/PTCalendarRow'

export abstract class PTCalendarRepository {
  abstract getMonitorMetrics(request: PTMonitorMetricsRequest): Promise<PTMonitorMetricsResponse>;
  abstract getListPTCalendar(request: PTCalendarRequest): Promise<PTCalendarRow[]>;
}

export class PTCalendarRepositoryImpl extends PTCalendarRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getMonitorMetrics(request: PTMonitorMetricsRequest): Promise<PTMonitorMetricsResponse> {
    return this.client
      .post<PTMonitorMetricsResponse>('/api/pt-calendar/get-monitor-metrics', request)
      .then((response) => response.data);
  }

  async getListPTCalendar(request: PTCalendarRequest): Promise<PTCalendarRow[]> {
    return this.client
      .post<PTCalendarRow[]>('/api/pt-calendar/get-list', request)
      .then((response) => response.data);
  }
}

export default new PTCalendarRepositoryImpl(client);
