import { defineStore } from 'pinia';
import { ref } from 'vue';
import { PTCalendarDisplay } from '../enums/PTCalendarDisplay';

export const usePTCalendarStore = defineStore('PTCalendar', () => {
  // State
  const displayType = ref<PTCalendarDisplay>(PTCalendarDisplay.Calendar);
  const firstDayOfWeek = ref<Date>(new Date());
  const selectedFromStoreId = ref<string | null>(null);

  function setDisplayType(type: PTCalendarDisplay) {
    displayType.value = type;
  }

  return {
    // State
    selectedFromStoreId,
    firstDayOfWeek,
    displayType,

    // Actions
    setDisplayType,
  };
});
