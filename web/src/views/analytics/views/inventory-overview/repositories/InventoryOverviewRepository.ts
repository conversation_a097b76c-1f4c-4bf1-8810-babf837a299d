import client from '@/client/HttpClientBuilder.ts'
import type { GetAvgDaysOnHandRequest, GetChartDataRequest, GetHeatmapDataRequest, GetInventoryTurnoverRequest, GetSkuAlmostOutOfStockRequest, GetStoreAlmostOutOfStockRequest, GetTotalInventoryQuantityRequest } from '../domains/requests/InventoryOverviewRequest'
import type { GetAvgDaysOnHandResponse, GetChartDataResponse, GetHeatmapDataResponse, GetInventoryTurnoverResponse, GetSkuAlmostOutOfStockResponse, GetStoreAlmostOutOfStockResponse, GetTotalInventoryQuantityResponse } from '../domains/responses/InventoryOverviewResponse'
import type { HttpClient } from '@/client/HttpClient'

export abstract class InventoryOverviewRepository {
  abstract getAvgDaysOnHand(request: GetAvgDaysOnHandRequest): Promise<GetAvgDaysOnHandResponse>
  abstract getTotalInventoryQuantity(request: GetTotalInventoryQuantityRequest): Promise<GetTotalInventoryQuantityResponse>
  abstract getInventoryTurnover(request: GetInventoryTurnoverRequest): Promise<GetInventoryTurnoverResponse>
  abstract getSkuAlmostOutOfStock(request: GetSkuAlmostOutOfStockRequest): Promise<GetSkuAlmostOutOfStockResponse>
  abstract getStoreAlmostOutOfStock(request: GetStoreAlmostOutOfStockRequest): Promise<GetStoreAlmostOutOfStockResponse>
  abstract getHeatmapData(request: GetHeatmapDataRequest): Promise<GetHeatmapDataResponse>
  abstract getChartData(request: GetChartDataRequest): Promise<GetChartDataResponse>
}

export class InventoryOverviewRepositoryImpl extends InventoryOverviewRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getAvgDaysOnHand(request: GetAvgDaysOnHandRequest): Promise<GetAvgDaysOnHandResponse> {
    return this.client
      .post<GetAvgDaysOnHandResponse>('/api/query', {
        ...request,
        name: 'get_avg_days_on_hand',
      })
      .then((response) => response.data)
  }

  async getTotalInventoryQuantity(request: GetTotalInventoryQuantityRequest): Promise<GetTotalInventoryQuantityResponse> {
    return this.client
      .post<GetTotalInventoryQuantityResponse>('/api/query', {
        ...request,
        name: 'get_total_inventory_quantity',
      })
      .then((response) => response.data)
  }

  async getInventoryTurnover(request: GetInventoryTurnoverRequest): Promise<GetInventoryTurnoverResponse> {
    return this.client
      .post<GetInventoryTurnoverResponse>('/api/query', {
        ...request,
        name: 'get_inventory_turnover',
      })
      .then((response) => response.data)
  }

  async getSkuAlmostOutOfStock(request: GetSkuAlmostOutOfStockRequest): Promise<GetSkuAlmostOutOfStockResponse> {
    return this.client
      .post<GetSkuAlmostOutOfStockResponse>('/api/query/page', {
        ...request,
        name: 'get_products_almost_out_of_stock',
      })
      .then((response) => response.data)
  }

  async getStoreAlmostOutOfStock(request: GetStoreAlmostOutOfStockRequest): Promise<GetStoreAlmostOutOfStockResponse> {
    return this.client
      .post<GetStoreAlmostOutOfStockResponse>('/api/query', {
        ...request,
        name: 'get_stores_almost_out_of_stock',
      })
      .then((response) => response.data)
  }

  async getHeatmapData(request: GetHeatmapDataRequest): Promise<GetHeatmapDataResponse> {
    return this.client
      .post<GetHeatmapDataResponse>('/api/query', {
        ...request,
        name: 'get_sku_oos_heatmap_data',
      })
      .then((response) => response.data)
  }

  async getChartData(request: GetChartDataRequest): Promise<GetChartDataResponse> {
    return this.client
      .post<GetChartDataResponse>('/api/query', {
        ...request,
        name: 'get_total_hours_oos_chart_data',
      })
      .then((response) => response.data)
  }

}

export default new InventoryOverviewRepositoryImpl(client);
