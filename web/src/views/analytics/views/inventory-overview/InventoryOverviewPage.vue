<script setup lang="ts">

import { onMounted } from 'vue';
import HistoricalDataSegment from './segments/historical-data-segment/HistoricalDataSegment.vue';
import MonitorMetricSegment from './segments/monitor-metric-segment/MonitorMetricSegment.vue';
import ProductOutOfStockSegment from './segments/product-out-of-stock-segment/ProductOutOfStockSegment.vue';
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore';

const { fetchStores } = useStoreStore();

onMounted(() => {
  fetchStores();
});

</script>

<template>
  <div style="display: flex; flex-direction: column; gap: 1rem;">
    <MonitorMetricSegment />
    <HistoricalDataSegment />
    <ProductOutOfStockSegment />
  </div>
</template>

<style scoped>

</style>
