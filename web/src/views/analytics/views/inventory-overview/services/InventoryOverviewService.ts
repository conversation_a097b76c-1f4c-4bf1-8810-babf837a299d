import type { GetAvgDaysOnHandRequest, GetChartDataRequest, GetHeatmapDataRequest, GetInventoryTurnoverRequest, GetSkuAlmostOutOfStockRequest, GetStoreAlmostOutOfStockRequest, GetTotalInventoryQuantityRequest } from "../domains/requests/InventoryOverviewRequest";
import type { GetAvgDaysOnHandResponse, GetInventoryTurnoverResponse, GetStoreAlmostOutOfStockResponse, GetTotalInventoryQuantityResponse } from "../domains/responses/InventoryOverviewResponse";
import type { QueryPageResponse } from "../domains/responses/QueryPageResponse";
import repository, { type InventoryOverviewRepository } from "../repositories/InventoryOverviewRepository";
import type { ProductAlmostOutOfStockModel } from "../segments/product-out-of-stock-segment/models/ProductAlmostOutOfStock.model";
import { SkuOutOfStockResponseAdapter } from "./adapters/SkuOutOfStockResponseAdapter";
import { HeatmapDataResponseAdapter } from "./adapters/HeatmapDataResponseAdapter";
import { ChartDataResponseAdapter } from "./adapters/ChartDataResponseAdapter";
import type { HeatmapDataModel } from "../segments/historical-data-segment/models/HeatmapDataModel";
import type { ChartDataModel } from "../segments/historical-data-segment/models/ChartDataModel";

export abstract class InventoryOverviewService{
  abstract getAvgDaysOnHand(request: GetAvgDaysOnHandRequest): Promise<GetAvgDaysOnHandResponse>
  abstract getTotalInventoryQuantity(request: GetTotalInventoryQuantityRequest): Promise<GetTotalInventoryQuantityResponse>
  abstract getInventoryTurnover(request: GetInventoryTurnoverRequest): Promise<GetInventoryTurnoverResponse>
  abstract getSkuAlmostOutOfStock(request: GetSkuAlmostOutOfStockRequest): Promise<QueryPageResponse<ProductAlmostOutOfStockModel>>
  abstract getStoreAlmostOutOfStock(request: GetStoreAlmostOutOfStockRequest): Promise<GetStoreAlmostOutOfStockResponse>
}

export class InventoryOverviewServiceImpl extends InventoryOverviewService {
  private readonly repository: InventoryOverviewRepository

  constructor(repository: InventoryOverviewRepository) {
    super()
    this.repository = repository
  }

  async getAvgDaysOnHand(request: GetAvgDaysOnHandRequest): Promise<GetAvgDaysOnHandResponse> {
    return this.repository.getAvgDaysOnHand(request);
  }

  async getTotalInventoryQuantity(request: GetTotalInventoryQuantityRequest): Promise<GetTotalInventoryQuantityResponse> {
    return this.repository.getTotalInventoryQuantity(request);
  }

  async getInventoryTurnover(request: GetInventoryTurnoverRequest): Promise<GetInventoryTurnoverResponse> {
    return this.repository.getInventoryTurnover(request);
  }

  async getSkuAlmostOutOfStock(request: GetSkuAlmostOutOfStockRequest): Promise<QueryPageResponse<ProductAlmostOutOfStockModel>> {
    const response = await this.repository.getSkuAlmostOutOfStock(request);
    return SkuOutOfStockResponseAdapter.adapt(response);
  }

  async getStoreAlmostOutOfStock(request: GetStoreAlmostOutOfStockRequest): Promise<GetStoreAlmostOutOfStockResponse> {
    return this.repository.getStoreAlmostOutOfStock(request);
  }

  async getHeatmapData(request: GetHeatmapDataRequest): Promise<HeatmapDataModel[]> {
    return this.repository.getHeatmapData(request).then(response => {
      return HeatmapDataResponseAdapter.adapt(response.rows);
    });
  }

  async getChartData(request: GetChartDataRequest): Promise<ChartDataModel[]> {
    return this.repository.getChartData(request).then(response => {
      return ChartDataResponseAdapter.adapt(response.rows);
    });
  }
}

export default new InventoryOverviewServiceImpl(repository);
