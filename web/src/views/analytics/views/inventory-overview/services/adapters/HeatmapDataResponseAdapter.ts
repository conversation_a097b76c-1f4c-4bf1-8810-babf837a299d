import type { HeatmapDataRow } from "../../domains/responses/InventoryOverviewResponse"
import type { HeatmapDataModel } from "../../segments/historical-data-segment/models/HeatmapDataModel";

export class HeatmapDataResponseAdapter {
  static adapt(rows: HeatmapDataRow[]): HeatmapDataModel[] {
    return rows.flatMap(row => {
      const { date, ...obj } = row;
      return Object.entries(obj).map(([key, value]) => ({
        date: this.formatDate(date), // Convert 'yyyy-mm-dd' to 'dd-mm-yyyy' format
        hour: key.replace('total_sku_OOS_', '').concat(':00'),
        value: value
      }));
    });
  }

  private static formatDate(dateString: string): string {
    const parts = dateString.split('-');
    return `${parts[2]}-${parts[1]}-${parts[0]}`;
  }
}
