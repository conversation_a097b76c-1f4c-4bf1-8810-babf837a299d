import type { ChartDataRow } from "../../domains/responses/InventoryOverviewResponse"
import type { ChartDataModel } from "../../segments/historical-data-segment/models/ChartDataModel";

export class ChartDataResponseAdapter {
  static adapt(rows: ChartDataRow[]): ChartDataModel[] {
    return rows.map(row => ({
      date: this.formatDate(row.date), // Convert 'yyyy-mm-dd' to 'dd-mm-yyyy' format
      value: row.total_hours
    }));
  }

  private static formatDate(dateString: string): string {
    const parts = dateString.split('-');
    return `${parts[2]}-${parts[1]}-${parts[0]}`;
  }
}
