import type { GetSkuAlmostOutOfStockResponse } from "../../domains/responses/InventoryOverviewResponse"
import type { QueryPageResponse } from "../../domains/responses/QueryPageResponse";
import type { ProductAlmostOutOfStockModel } from "../../segments/product-out-of-stock-segment/models/ProductAlmostOutOfStock.model";

export class SkuOutOfStockResponseAdapter {
  static adapt(response: GetSkuAlmostOutOfStockResponse): QueryPageResponse<ProductAlmostOutOfStockModel> {
    return {
      totalItems: response.totalItems,
      totalPages: response.totalPages,
      page: response.page,
      size: response.size,
      rows: response.rows.map((row, index) => {
        const predictions: { date: string; prediction: number }[] = [];
        let currentStock = row.latestInventory;
        row.saleDates.forEach((date, i) => {
          predictions.push({
            date: date,
            prediction: row.predictions[i],
          });
        });

        return {
          id: response.page * response.size + index + 1,
          barcode: row.barcode,
          productName: row.productName,
          storeId: row.storeId,
          availableStock: row.latestInventory,
          predictions: predictions.sort((a, b) => {
            return a.date.localeCompare(b.date);
          }).map(p => {
            currentStock -= p.prediction;
            return {
              date: p.date.split('-').reverse().join('-'),
              stock: Number(currentStock.toFixed(2))
            }
          })
        }
      })
    }
  }
}
