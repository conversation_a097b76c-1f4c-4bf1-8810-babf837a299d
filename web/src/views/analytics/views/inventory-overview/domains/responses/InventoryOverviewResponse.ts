import type { QueryResponse } from "../../../replenishment/domains/responses/QueryResponse";
import type { QueryPageResponse } from "./QueryPageResponse";

export type AvgDaysOnHandRow = {
  storeId: string;
  storeName: string;
  daysOnHand: number;
};

export type TotalInventoryQuantityRow = {
  storeId: string;
  storeName: string;
  totalInventory: number;
};

export type InventoryTurnoverRow = {
  storeId: string;
  storeName: string;
  inventoryTurnover: number;
};

export type SkuAlmostOutOfStockRow = {
  barcode: string;
  productId: string;
  productName: string;
  storeId: string;
  storeName: string;
  latestInventory: number;
  saleDates: string[];
  predictions: number[];
};

export type StoreAlmostOutOfStockRow = {
  id: string;
  name: string;
};

export type HeatmapDataRow = {
  date: string;
  total_sku_OOS_06: number;
  total_sku_OOS_07: number;
  total_sku_OOS_08: number;
  total_sku_OOS_09: number;
  total_sku_OOS_10: number;
  total_sku_OOS_11: number;
  total_sku_OOS_12: number;
  total_sku_OOS_13: number;
  total_sku_OOS_14: number;
  total_sku_OOS_15: number;
  total_sku_OOS_16: number;
  total_sku_OOS_17: number;
  total_sku_OOS_18: number;
  total_sku_OOS_19: number;
  total_sku_OOS_20: number;
  total_sku_OOS_21: number;
  total_sku_OOS_22: number;
}

export type ChartDataRow = {
  date: string;
  total_hours: number;
}

export type GetAvgDaysOnHandResponse = QueryResponse<AvgDaysOnHandRow>;
export type GetTotalInventoryQuantityResponse = QueryResponse<TotalInventoryQuantityRow>;
export type GetInventoryTurnoverResponse = QueryResponse<InventoryTurnoverRow>;
export type GetSkuAlmostOutOfStockResponse = QueryPageResponse<SkuAlmostOutOfStockRow>;
export type GetStoreAlmostOutOfStockResponse = QueryResponse<StoreAlmostOutOfStockRow>;
export type GetHeatmapDataResponse = QueryResponse<HeatmapDataRow>;
export type GetChartDataResponse = QueryResponse<ChartDataRow>;

