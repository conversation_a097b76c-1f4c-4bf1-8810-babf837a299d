<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

const props = defineProps<{
  data: {
    date: string;
    value: number;
  } [];
}>();

const chartOptions = computed(() => ({
  chart: {
    type: 'spline',
    height: 400,
  },
  title: {
    text: "Total out of stock hours per day",
    align: 'left',
    style: {
      fontSize: '14px',
      fontWeight: '500',
    },
    margin: 20,
  },
  xAxis: {
    categories: props.data.map((d) => {
      const [day, month, ] = d.date.split('-');
      return `${day}/${month}`;
    }),
    tickInterval: 1, // <PERSON><PERSON>n thị tất cả các ngày
  },
  yAxis: {
    title: {
      text: '',
    },
    min: 0, // Giới hạn tối thiểu là 0 giờ
  },
  tooltip: {
    shared: true,
    valueSuffix: ' giờ',
  },
  plotOptions: {
    spline: {
      marker: {
        enabled: true, // <PERSON><PERSON>n thị điểm trên đường spline
      },
    },
  },
  series: [
    {
      name: 'Out of stock hours per day',
      type: 'spline',
      color: '#28a745', // Màu xanh lá cây
      data: props.data.map(d => d.value), // Dữ liệu cho biểu đồ
    },
  ],
  credits: {
    enabled: false,
  },
}));

const chartRef = ref<any>(null);

const handleResize = () => {
  if (chartRef.value && chartRef.value.chart) {
    setTimeout(() => {
      chartRef.value.chart.reflow();
    }, 100);
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="chart-container">
    <highcharts ref="chartRef" :options="chartOptions"></highcharts>
  </div>
</template>

<style scoped>
.chart-container {
  background: #ffffff;
  border: 1px solid #f0ebeb;
  border-radius: 8px;
  padding: 16px;
}
</style>
