<script setup lang="ts">
import Highcharts from "highcharts";
import { Chart } from "highcharts-vue";
import { computed } from "vue";

const props = defineProps<{
  data: Array<{
    date: string; // Date in dd-mm-yyyy format
    hour: string; // hh:mm format
    value: number;
  }>;
}>();

const chartOptions = computed(() => {
  const xCategories = Array.from(new Set(props.data.map(d => {
    const [day, month] = d.date.split('-');
    return `${day}/${month}`;
  })));
  const yCategories = Array.from({ length: 17 }, (_, i) => `${(i + 6).toString().padStart(2, '0')}:00`);
  const data = props.data.map(d => {
    const [day, month] = d.date.split('-');
    return [xCategories.indexOf(`${day}/${month}`), yCategories.indexOf(d.hour), d.value];
  });
  return {
    chart: {
      type: "heatmap",
      height: 400,
    },
    title: {
      text: "Sku out of stock by Hour",
      align: 'left',
      style: {
        fontSize: '14px',
        fontWeight: '500',
      },
      margin: 20,
    },
    xAxis: {
      categories: xCategories,
    },
    yAxis: {
      categories: yCategories,
      title: null,
      labels: {
        enabled: false,
      },
    },
    colorAxis: {
      min: 0,
      minColor: "#84ADFF",
      maxColor: "#D92D00",
    },
    legend: {
      align: "right",
      layout: "vertical",
      margin: 0,
      verticalAlign: "top",
      y: 25,
      symbolHeight: 280,
    },
    tooltip: {
      formatter: function (this: Highcharts.TooltipFormatterContextObject): string {
        const date = this.series.xAxis.categories[this.point.x];
        const hour = this.series.yAxis.categories[this.point.y || 0];
        const nextHour = hour ? `${(parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0')}:00` : '';
        const value = this.point.value;
        return `
          <div style="font-family: Arial, sans-serif; font-size: 12px; line-height: 1.5; color: #333;">
            <span style="color: #555;">Total out of stock: <b style="color: #000;">${value}</b></span><br>
            <span style="color: #777;">${hour} - ${nextHour}: ${date}</span>
          </div>
        `;
      },
      useHTML: true, // Cho phép sử dụng HTML trong tooltip
    },
    series: [
      {
        name: "Sales per employee",
        borderWidth: 1,
        borderColor: "#FFFFFF",
        data: data,
        dataLabels: {
          enabled: false,
        },
      },
    ],
    credits: {
      enabled: false,
    },
  }
})
</script>

<template>
  <div class="chart-container">
    <Chart :options="chartOptions as any"></Chart>
  </div>
</template>

<style scoped>
.chart-container {
  background: #ffffff;
  border: 1px solid #f0ebeb;
  border-radius: 8px;
  padding: 16px;
}
</style>
