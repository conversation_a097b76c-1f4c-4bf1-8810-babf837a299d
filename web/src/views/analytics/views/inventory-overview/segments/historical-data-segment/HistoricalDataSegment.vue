<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useStoreStore } from '../../../replenishment/segments/monitor-sku/stores/StoreStore';
import HistoricalChart from './components/charts/HistoricalChart.vue';
import HistoricalHeatMap from './components/charts/HistoricalHeatMap.vue';
import HistoricalDataHeader from './components/HistoricalDataHeader.vue';
import type { StoreRow } from '../../../replenishment/domains/responses/GetSalesResponse';
import { useSelectedStore } from '../../stores/SelectedStoreStore';
import service from '@/views/analytics/views/inventory-overview/services/InventoryOverviewService';
import type { HeatmapDataModel } from './models/HeatmapDataModel';
import type { ChartDataModel } from './models/ChartDataModel';

const selectedStoreStore = useSelectedStore();
const storeStore = useStoreStore();

const stores = computed(() => {
  return storeStore.stores.filter(store => selectedStoreStore.selectedStoreIds.includes(store.id)) as StoreRow[];
});

const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const getDateRange = () => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const fromDate = new Date(today);
  fromDate.setDate(today.getDate() - 14);

  return {
    fromDate: formatDateToYYYYMMDD(fromDate),
    toDate: formatDateToYYYYMMDD(yesterday)
  };
};

const heatmapData = ref<HeatmapDataModel[]>([]);
const chartData = ref<ChartDataModel[]>([]);

watch(stores, async (newStores) => {
  if (newStores.length === 0) return;

  const { fromDate, toDate } = getDateRange();

  [heatmapData.value, chartData.value] = await Promise.all([
    service.getHeatmapData({
      storeIds: newStores.map(store => store.id),
      fromDate: fromDate,
      toDate: toDate,
    }),
    service.getChartData({
    storeIds: newStores.map(store => store.id),
    fromDate: fromDate,
    toDate: toDate,
  })
  ]);
}, { deep: true });

</script>

<template>
  <AFlex class="historical-container">
    <HistoricalDataHeader />

    <div v-if="stores.length" class="chart-list">
      <div class="chart-item"><HistoricalHeatMap :data="heatmapData" /></div>
      <div class="chart-item"><HistoricalChart :data="chartData" /></div>
    </div>

    <div class="store-chart-list">

    </div>
  </AFlex>
</template>

<style scoped>
.historical-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.chart-list {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.chart-item {
  flex: 1;
  min-width: 300px;
  max-width: calc(100% - 16px);
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .store-chart-item {
    flex-direction: column;
  }

  .chart-item {
    width: 100%;
    margin-bottom: 16px;
  }
}
</style>
