<script setup lang="ts">
import { computed, ref } from 'vue';

const selectedValue = ref<string>();

const options = computed(() => [
  {
    value: 1,
    label: 'Total out of stock hours',
  },
  {
    value: 2,
    label: 'Days on hand (DoH)',
  },
  {
    value: 3,
    label: 'Sales velocity'
  },
  {
    value: 4,
    label: 'Inventory quantity',
  },
  {
    value: 5,
    label: 'Inventory value',
  },
  {
    value: 6,
    label: 'Sales volume',
  },
  {
    value: 7,
    label: 'Revenue',
  },
  {
    value: 8,
    label: 'Lost sales value etc',
  },
  {
    value: 9,
    label: 'Số lượng SKU về siêu thị',
  },
  {
    value: 10,
    label: '<PERSON><PERSON><PERSON><PERSON> lượ<PERSON> nhập (tính từ PT)',
  }
]);


const handleChange = (value: string) => {
  console.log(`Selected value: ${value}`);
};

</script>

<template>
  <AFlex class="header-container">
    <ATypographyTitle  :level="5">Historical data</ATypographyTitle>
    <ASelect
      ref="select"
      placeholder="Select a metric"
      v-model:value="selectedValue"
      style="width: 300px"
      @change="handleChange"
      :options="options"
      allow-clear
    ></ASelect>
  </AFlex>
</template>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
