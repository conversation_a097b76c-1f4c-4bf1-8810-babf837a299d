<script setup lang="ts">
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore';
import { computed, ref, watch } from 'vue';
import { useSelectedStore } from '../../../../stores/SelectedStoreStore';

const { stores } = useStoreStore();
const selectedStoreStore = useSelectedStore();

type Store = {
  id: string;
  name: string;
  type: StoreType;
};

enum StoreType {
  Mini = 'Mini',
  Mart = 'Mart',
}

enum SortType {
  Selected = 'Selected',
  Alphabet = 'Alphabet',
}

const props = defineProps<{
  isOpen: boolean;
}>()

// Local state to manage visibility of the modal
const localVisible = ref(props.isOpen);

// Watch for changes in the isOpen prop to update localVisible
watch(() => props.isOpen, (newVal) => {
  localVisible.value = newVal;
});

// Emit events for close and save actions
const emit = defineEmits<{
  (e: 'save', data: string[]): void;
  (e: 'close'): void;
}>();

const handleOk = () => {
  emit('save', selectedStoreIds.value);
};

const handleCancel = () => {
  emit('close');
};

// Local state for input search, sort option, and selected store IDs
const inputSearch = ref('');
const sortOption = ref<SortType>(SortType.Selected);
const selectedStoreTypes = ref<StoreType[]>([]);
const selectedStoreIds = ref<string[]>([...selectedStoreStore.selectedStoreIds]);

const storeOptions = computed(() => {
  // Store types: 1 for Mart, 3 for Mini
  return stores.filter(store => [1,3].includes(store.storeType)).map((store) => ({
    id: store.id,
    name: store.name,
    type: store.storeType == 1 ? StoreType.Mart : StoreType.Mini,
  })) as Store[];
});

const filteredStoreOptions = ref<Store[]>([...storeOptions.value]);

const isSelectAll = computed(() => {
  return selectedStoreIds.value.length === storeOptions.value.length;
});

const handleCheckboxChange = (id: string, checked: boolean) => {
  if (checked) {
    selectedStoreIds.value.push(id);
  } else {
    selectedStoreIds.value = selectedStoreIds.value.filter((storeId) => storeId !== id);
  }
};

const handleCheckAll = () => {
  if (isSelectAll.value) {
    selectedStoreIds.value = [];
    return;
  }
  selectedStoreIds.value = storeOptions.value.map((store) => store.id);
};

const handleStoreTypeChange = (type: StoreType, checked: boolean) => {
  if (checked) {
    selectedStoreTypes.value.push(type);
  } else {
    selectedStoreTypes.value = selectedStoreTypes.value.filter((storeType) => storeType !== type);
  }
};

const filterStores = (input: string, sort: SortType, storeTypes: StoreType[]) => {
  filteredStoreOptions.value = storeOptions.value
    .filter((store) => {
      const matchesSearch = store.name.toLowerCase().includes(input.toLowerCase());
      const matchesType = storeTypes.length === 0 || storeTypes.includes(store.type);
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      if (sort === SortType.Selected) {
        return selectedStoreIds.value.includes(a.id) ? -1 : 1;
      } else if (sort === SortType.Alphabet) {
        return a.name.localeCompare(b.name);
      }
      return 0;
    });
};

// Watch for changes in inputSearch, sortOption, and selectedStoreTypes to filter stores
watch([inputSearch, sortOption, selectedStoreTypes], ([input, sort, storeTypes]) => {
  filterStores(input, sort, storeTypes);
}, { deep: true, immediate: true });

</script>

<template>
  <!-- Dialog -->
  <AModal
    v-model:visible="localVisible"
    title="Chọn siêu thị"
    @ok="handleOk"
    @cancel="handleCancel"
    width="600px"
  >
    <div>
      <!-- Input và Dropdown -->
      <AFlex gap="small" style="margin-bottom: 16px">
        <AInput v-model:value="inputSearch" placeholder="Tìm siêu thị" style="flex: 1" />
        <ASelect
          style="width: 200px"
          v-model:value="sortOption"
          :options="[
            { value: SortType.Selected, label: 'Sắp xếp theo đã chọn' },
            { value: SortType.Alphabet, label: 'Sắp xếp theo tên' }
          ]"
        />
      </AFlex>
      <div style="margin-bottom: 16px">
        <ATypography>{{ selectedStoreIds.length }} siêu thị đang được chọn. <a class="total-selected-text" @click="handleCheckAll">{{ isSelectAll ? 'Bỏ chọn tất cả' : 'Chọn tất cả' }}</a></ATypography>
      </div>
      <AFlex gap="small" style="margin-bottom: 16px">
        <ACheckbox @change="handleStoreTypeChange(StoreType.Mini, $event.target.checked)" :checked="selectedStoreTypes.includes(StoreType.Mini)">Mini</ACheckbox>
        <ACheckbox @change="handleStoreTypeChange(StoreType.Mart, $event.target.checked)" :checked="selectedStoreTypes.includes(StoreType.Mart)">Mart</ACheckbox>
      </AFlex>

      <div style="max-height: 300px; overflow-y: auto; border: 1px solid #f0f0f0; border-radius: 4px;">
        <AList
          :data-source="filteredStoreOptions"
        >
          <template #renderItem="{ item }: { item: Store }">
            <AListItem>
              <ACheckbox
                :checked="selectedStoreIds.includes(item.id)"
                @change="handleCheckboxChange(item.id, $event.target.checked)"
              >
                {{ item.name }}
              </ACheckbox>
            </AListItem>
          </template>
        </AList>
      </div>
    </div>

    <!-- Custom Footer -->
    <template #footer>
      <AFlex justify="end" gap="small">
        <AButton @click="handleCancel">Hủy</AButton>
        <AButton type="primary" @click="handleOk">Lưu</AButton>
      </AFlex>
    </template>
  </AModal>
</template>

<style scoped>

.total-selected-text {
  font-weight: 700;
  color: #007bff;
}

</style>
