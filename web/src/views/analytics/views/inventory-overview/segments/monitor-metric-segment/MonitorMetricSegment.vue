<script setup lang="ts">
import MetricCard from '@/views/analytics/components/MetricCard.vue';
import MonitorMetricHeader from './components/MonitorMetricHeader.vue';
import { InfoCircleOutlined, CalendarOutlined, ShopOutlined, ReloadOutlined, Area<PERSON>hartOutlined, ArrowDownOutlined } from '@ant-design/icons-vue';
import { h, ref, watch } from 'vue';
import service from '@/views/analytics/views/inventory-overview/services/InventoryOverviewService';
import BreakdownSelect from './components/BreakdownSelect.vue';
import { useSelectedStore } from '../../stores/SelectedStoreStore';

const selectedStoreStore = useSelectedStore();
const totalStoresOutOfStock = ref<number>(0);
const avgDaysOnHand = ref<number>(0);
const avgInventoryQuantity = ref<number>(0);
const avgInventoryTurnover = ref<number>(0);

const fetchTotalStoresOutOfStock = async () => {
  try {
    const data = await service.getStoreAlmostOutOfStock({
      storeIds: selectedStoreStore.selectedStoreIds,
    })
    totalStoresOutOfStock.value = data.rows.length;
  } catch (err) {
    console.error('Failed to fetch total stores out of stock:', err);
  }
};

const fetchAvgDaysOnHand = async () => {
  try {
    const data = await service.getAvgDaysOnHand({
      storeIds: selectedStoreStore.selectedStoreIds,
    });
    avgDaysOnHand.value = Math.ceil(data.rows.reduce((sum, item) => sum + item.daysOnHand, 0) / data.rows.length || 0);
  } catch (err) {
    console.error('Failed to fetch average days on hand:', err);
  }
};

const fetchTotalInventoryQuantity = async () => {
  try {
    const data = await service.getTotalInventoryQuantity({
      storeIds: selectedStoreStore.selectedStoreIds,
    });
    avgInventoryQuantity.value = Math.ceil(data.rows.reduce((sum, item) => sum + item.totalInventory, 0) / data.rows.length || 0);
  } catch (err) {
    console.error('Failed to fetch total inventory quantity:', err);
  }
}

const fetchInventoryTurnover = async () => {
  try {
    const data = await service.getInventoryTurnover({
      storeIds: selectedStoreStore.selectedStoreIds,
      month: new Date().toISOString().slice(0, 7), // Current month in YYYY-MM format
    });
    avgInventoryTurnover.value = Math.ceil(data.rows.reduce((sum, item) => sum + item.inventoryTurnover, 0) / data.rows.length || 0);
  } catch (err) {
    console.error('Failed to fetch inventory turnover:', err);
  }
}

watch(() => selectedStoreStore.selectedStoreIds, () => {
  fetchTotalStoresOutOfStock();
  fetchAvgDaysOnHand();
  fetchTotalInventoryQuantity();
  fetchInventoryTurnover();
}, { deep: true, immediate: true });

const formatNumber = (value: number): string => {
  if (value >= 1_000_000) {
    return `${(value / 1_000_000).toFixed(1)}M`;
  } else if (value >= 1_000) {
    return `${(value / 1_000).toFixed(1)}K`;
  }
  return value.toString();
};

</script>

<template>
  <AFlex class="metric-container" gap="small">
    <MonitorMetricHeader />

    <AFlex class="metric-body" gap="small">
      <MetricCard cardColor='#F79009'>
        <div class="card-content">
          <div style="display: flex; justify-content: space-between; align-items: center; min-height: 30px; max-height: 44px;">
            <ATypography class="card-title">
              <ShopOutlined />
              <span style="margin-left: 6px;">Số siêu thị sắp hết hàng</span>
            </ATypography>
          </div>

          <AFlex flexDirection="row" align="center" justify="space-between">
            <div style="margin-bottom: 12px;">
              <span class="card-metric-text" :style="{ color: `#F79009`}">{{ totalStoresOutOfStock }}</span> <span class="card-content-text">/{{ selectedStoreStore.selectedStoreIds.length }}</span>
            </div>
            <AButton type="text" :icon="h(InfoCircleOutlined)" />
          </AFlex>
          <ATypography :style="{ fontSize: `12px`, fontWeight: `400`, color: `#667085` }">
            {{ totalStoresOutOfStock > 0 ? 'Several stores are low on inventory and may soon run out of stock.' : 'No stores are currently low on inventory.' }}
          </ATypography>
        </div>
      </MetricCard>

      <MetricCard cardColor='#0BA5EC'>
        <div class="card-content">
          <div style="display: flex; justify-content: space-between; align-items: center; max-height: 30px;">
            <ATypography class="card-title">
              <CalendarOutlined />
              <span style="margin-left: 6px;">Days on hand (DoH)</span>
            </ATypography>
            <BreakdownSelect />
          </div>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <div><span class="card-metric-text" :style="{ color: `#0BA5EC`}">{{ avgDaysOnHand }}</span> <span class="card-content-text">/day</span></div>
            <ATypography :style="{ fontSize: `14px`, color: 'red', fontWeight: 500 }">-2.1% <ArrowDownOutlined /></ATypography>
          </div>
          <div class="metric-details" style="background-color: #ECF9FF;">
            <div class="metric-detail">Short <span>5 day</span></div>
            <div class="metric-detail metric-detail-mid">Medium <span>20 day</span></div>
            <div class="metric-detail">Long <span>40 day</span></div>
          </div>
        </div>
      </MetricCard>

      <MetricCard cardColor='#2970FF'>
        <div class="card-content">
          <div style="display: flex; justify-content: space-between; align-items: center;  max-height: 30px;">
            <ATypography class="card-title">
              <AreaChartOutlined />
              <span style="margin-left: 6px;">Inventory quantity</span>
            </ATypography>
            <BreakdownSelect />
          </div>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span class="card-metric-text" :style="{ color: `#2970FF`}">{{ formatNumber(avgInventoryQuantity) }}</span>
            <ATypography :style="{ fontSize: `14px`, color: 'red', fontWeight: 500 }">-2.1% <ArrowDownOutlined /></ATypography>
          </div>
          <div class="metric-details" style="background-color: #F5F8FF;">
            <div class="metric-detail">Short <span>10.4M</span></div>
            <div class="metric-detail metric-detail-mid">Medium <span>20.4M</span></div>
            <div class="metric-detail">Long <span>100.0M</span></div>
          </div>
        </div>
      </MetricCard>

      <MetricCard cardColor='#15B79E'>
        <div class="card-content">
          <div style="display: flex; justify-content: space-between; align-items: center; min-height: 30px;  max-height: 44px;">
            <ATypography class="card-title">
              <ReloadOutlined />
              <span style="margin-left: 6px;">Inventory Turnover</span>
            </ATypography>
          </div>
          <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center;">
            <div><span class="card-metric-text" :style="{ color: `#15B79E`}">{{ avgInventoryTurnover }}</span> <span class="card-content-text">lần/ tháng</span></div>
            <ATypography :style="{ fontSize: `14px`, color: 'red', fontWeight: 500 }">-2.1% <ArrowDownOutlined /></ATypography>
          </div>
          <div class="metric-details" style="background-color: #F6FEF9;">
            <div class="metric-detail">Short <span>10 lần</span></div>
            <div class="metric-detail metric-detail-mid">Medium <span>20 lần</span></div>
            <div class="metric-detail">Long <span>30 lần</span></div>
          </div>
        </div>
      </MetricCard>
    </AFlex>
  </AFlex>
</template>

<style scoped>
.metric-container {
  display: flex;
  flex-direction: column;
}

.metric-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  width: 100%;
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #667085;
}

.card-metric-text {
  font-size: 26px;
  font-weight: 700;
}

.card-content-text {
  font-size: 20px;
  font-weight: 700;
  color: #667085;
}

.metric-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #667085;
  padding: 0.5rem 0;
  border-radius: 8px;
}

.metric-detail {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.metric-detail-mid {
  border-left: 1px solid #EAECF0;
  border-right: 1px solid #EAECF0;
}

.metric-detail span {
  font-size: 14px;
  font-weight: 700;
  color: #000;
}

</style>
