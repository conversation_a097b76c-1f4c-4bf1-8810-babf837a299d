<script setup lang="ts">
import { ref, h }  from 'vue'
import {
  PlusOutlined
} from '@ant-design/icons-vue'
import SelectStoreModal from './modals/SelectStoreModal.vue';
import { useSelectedStore } from '../../../stores/SelectedStoreStore';

const store = useSelectedStore();

const isModalVisible = ref(false);

const showModal = () => {
  isModalVisible.value = true;
};

const handleSave = (data: string[]) => {
  store.setSelectedStoreIds(data);
  isModalVisible.value = false;
};

const handleClose = () => {
  isModalVisible.value = false;
};

</script>

<template>
  <AFlex class="header-container">
    <ATypographyTitle  :level="5">Monitor Metric</ATypographyTitle>
    <AFlex gap="small" align="center">
      <ATypography>Đ<PERSON> chọn <span class="total-selected-text">{{ store.selectedStoreIds.length }} siêu thị</span></ATypography>
      <AButton type="primary" :icon ="h(PlusOutlined)" @click="showModal"><PERSON><PERSON><PERSON> siêu thị</AButton>
    </AFlex>
  </AFlex>

  <!-- Dialog -->
  <SelectStoreModal v-if="isModalVisible" :is-open="isModalVisible" @close="handleClose" @save="handleSave"/>
</template>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-selected-text {
  font-weight: 700;
  color: #007bff;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
