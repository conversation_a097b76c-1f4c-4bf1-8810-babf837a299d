<script setup lang="ts">
import { computed, ref } from 'vue'
import type { SelectProps } from 'ant-design-vue'
import { SkuOvertimeBreakdown } from '@/views/analytics/views/inventory-detail/components/historical/types.ts'

const breakdownOptions = computed<SelectProps['options']>(() => [
  {
    label: 'Shelf life',
    value: SkuOvertimeBreakdown.ShelfLife,
  },
  {
    label: 'Category',
    value: SkuOvertimeBreakdown.Category,
  },
  {
    label: 'Fast Slow Non',
    value: SkuOvertimeBreakdown.FastSlowNon,
  },
])

const breakdownValue = ref<SkuOvertimeBreakdown>(SkuOvertimeBreakdown.ShelfLife)

</script>

<template>
  <ASelect
    v-model:value="breakdownValue"
    :show-search="false"
    :options="breakdownOptions"
    :bordered="false"
    style="min-width: 8rem;"
  />
</template>

<style scoped>

</style>
