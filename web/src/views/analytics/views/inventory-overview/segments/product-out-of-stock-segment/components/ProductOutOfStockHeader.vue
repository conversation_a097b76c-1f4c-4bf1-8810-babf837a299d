<script setup lang="ts">
import { ref } from 'vue';
import { DataDisplay } from '@/views/analytics/views/inventory-detail/enums/DataDisplay';
import { DataDisplayLabels } from '@/views/analytics/views/inventory-detail/const/DataDisplayLabels';

const dataDisplay = ref(DataDisplay.Chart)

</script>

<template>
  <AFlex class="header-container">
    <ATypographyTitle  :level="5"><PERSON>h sách các sản phẩm gần hết hàng</ATypographyTitle>

    <ARadioGroup v-model:value="dataDisplay">
      <ARadioButton v-for="label in DataDisplayLabels" :key="label[0]" :value="label[0]">
        <div
          class="radio-button-icon-container"
          :[`data`]="dataDisplay === label[0] ? 'active' : ''"
        >
          <component :is="label[1]" color="var(--fill-color)" />
          <a style="color: var(--fill-color)">{{ label[2] }}</a>
        </div>
      </ARadioButton>
    </ARadioGroup>
  </AFlex>
</template>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

.view-toggle .ant-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 14px;
  color: #667085;
}

.view-toggle .ant-btn.active {
  border: 1px solid #007bff;
  color: #007bff;
  font-weight: 600;
}

.radio-button-icon-container {
  --fill-color: #667085;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.radio-button-icon-container[data='active'] {
  --fill-color: #155eef;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
