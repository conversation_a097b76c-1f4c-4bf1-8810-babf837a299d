<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { useAvailableWidthProvider } from '@/hooks/userContainerWidth.ts'
import type { ProductAlmostOutOfStockModel } from '../../models/ProductAlmostOutOfStock.model';
import { ContainerTwoTone, PlusSquareTwoTone } from '@ant-design/icons-vue';
import { h } from 'vue'
import service from '@/views/analytics/views/inventory-overview/services/InventoryOverviewService'
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore';
import { useSelectedStore } from '../../../../stores/SelectedStoreStore';
import BaseTable from '@/components/table/BaseTable.vue';
import type { BaseTableColumn, TableConfig } from '@/components/table/types/TableTypes';
import { Button as AButton } from 'ant-design-vue';

const selectedStoreStore = useSelectedStore();
const { getStoreById } = useStoreStore();

type PaginationInfo = {
  current: number; // Trang hiện tại
  pageSize: number; // Số lượng bản ghi trên mỗi trang
  total: number; // Tổng số bản ghi
};

// Pagination state
const pagination = ref<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
});

// Record state
const records = ref<ProductAlmostOutOfStockModel[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Tạo các cột con động dựa trên predictions
const predictionColumns = computed<BaseTableColumn[]>(() => {
  if (records.value.length > 0) {
    return records.value[0].predictions.map((prediction) => ({
      title: prediction.date.split('-').join('/'),
      dataIndex: `prediction_${prediction.date}`,
      key: `prediction_${prediction.date}`,
      width: 120,
      ellipsis: true,
      customRender: ({ record }: { record: ProductAlmostOutOfStockModel }) => {
        return record.predictions.find(p => p.date === prediction.date)?.stock ?? 'N/A';
      },
    }))
  }
  return [];
});

const tableRef = ref<HTMLDivElement | null>(null)
const containerRef = ref<HTMLDivElement>()

// Sử dụng hook để provide available width cho child components
const { updateAvailableWidth } = useAvailableWidthProvider(containerRef, {
  left: 32, // margin-left của .main-table-detail
  padding: 32
});

const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const getDateRange = () => {
  const today = new Date();

  const fromDate = new Date(today);
  fromDate.setDate(today.getDate() + 1);

  const toDate = new Date(today);
  toDate.setDate(today.getDate() + 14);

  return {
    fromDate: formatDateToYYYYMMDD(fromDate),
    toDate: formatDateToYYYYMMDD(toDate)
  };
};

const fetchData = async (pageNumber: number, pageSize: number) => {
  try {
    loading.value = true;
    const { fromDate, toDate } = getDateRange();
    const data = await service.getSkuAlmostOutOfStock({
      storeIds: selectedStoreStore.selectedStoreIds,
      fromDate: fromDate,
      toDate: toDate,
      pageNumber: pageNumber,
      pageSize: pageSize,
    })
    records.value = data.rows.map(item => ({
      ...item,
      store: getStoreById(item.storeId)?.name || 'Unknown Store',
    }));
    pagination.value.total = data.totalItems;
  } catch (err) {
    error.value = (err as Error).message || 'Failed to load data';
  } finally {
    loading.value = false;
  }
};

watch(() => selectedStoreStore.selectedStoreIds, () => {
  pagination.value.current = 1;
  fetchData(pagination.value.current - 1, pagination.value.pageSize);
}, { deep: true, immediate: true });

// Hàm xử lý khi người dùng chuyển trang
const handleTableChange = (paginationInfo: PaginationInfo) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
  fetchData(pagination.value.current - 1, pagination.value.pageSize);
};

// Tính toán height động
const ROW_HEIGHT = 54 // Height của mỗi row (bao gồm border)
const HEADER_HEIGHT = 55 // Height của table header
const MIN_HEIGHT = 400 // Minimum height
const MAX_HEIGHT = 600 // Maximum height để tránh table quá cao

const calculateTableHeight = computed(() => {
  const baseHeight = HEADER_HEIGHT
  const dataRowsHeight = records.value.length * ROW_HEIGHT

  const totalHeight = baseHeight + dataRowsHeight

  // Giới hạn trong khoảng min-max
  return Math.min(Math.max(totalHeight, MIN_HEIGHT), MAX_HEIGHT)
})

const columns = computed<BaseTableColumn[]>(() => ([
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 60,
    align: 'center',
    fixed: 'left',
  },
  {
    title: 'Product name',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
    align: 'left',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: 'Store',
    dataIndex: 'store',
    key: 'store',
    width: 200,
    align: 'left',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: 'Tồn kho hiện tại',
    dataIndex: 'availableStock',
    key: 'availableStock',
    width: 150,
    align: 'left',
    fixed: 'left',
  },
  {
    key: 'predictions',
    title: 'Dự báo cuối ngày',
    fixed: 'left',
    align: 'left',
    children: predictionColumns.value
  },
  {
    key: 'actions',
    title: '',
    fixed: 'right',
    align: 'center',
    width: 80,
    customRender: ({ record }: { record: ProductAlmostOutOfStockModel }) => {
      const iconStyle = { fontSize: '16px' }
      const actionContainerStyle = { display: 'flex', justifyContent: 'space-evenly' }
      return h('div', { style: actionContainerStyle }, [
        h(AButton, {
          type: 'text',
          size: 'small',
          onClick: (event: Event) => {
            event.stopPropagation();
            console.log(record.productName);
          }
        }, () => [h(ContainerTwoTone, { style: iconStyle })]),
        h(AButton, {
          type: 'text',
          size: 'small',
          onClick: (event: Event) => {
            event.stopPropagation();
            console.log(record.productName);
          }
        }, () => [h(PlusSquareTwoTone, { style: iconStyle })])
      ]);
    }
  },
]))

// Table configuration using default config
// Expand functionality is now handled by Ant Design Vue's built-in expandRowByClick
const tableConfig = computed<TableConfig>(() =>{
  return {
    size: 'small',
    loading: loading.value,
    scroll: {
      y: 600
    },
    pagination: {
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
      total: pagination.value.total,
      onChange: (page, pageSize) => handleTableChange({ ...pagination.value, current: page, pageSize }),
    }
  }
})

// Watch để update height khi data thay đổi
watch(() => records.value.length, () => {
  nextTick(() => {
    updateAvailableWidth()
  })
})
</script>

<template>
  <div
    ref="containerRef"
    class="main-table-container"
    :style="{ height: `${calculateTableHeight}px` }"
  >
    <!-- Error Display -->
    <AAlert
      v-if="error"
      :message="error"
      type="error"
      show-icon
      closable
      style="margin-bottom: 16px"
    />

    <!-- Table -->
    <BaseTable
      ref="tableRef"
      :columns="columns"
      :data-source="records"
      :config="tableConfig"
      row-key="id"
      class="main-table"
    >
    </BaseTable>
  </div>
</template>

<style scoped>
.main-table-container {
  position: relative;
  width: 100%;
  transition: height 0.3s ease;
}

.main-table {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  scrollbar-width: none;
}

/* Đảm bảo expanded row detail không làm tràn */
.main-table-detail {
  margin-left: 32px;
  max-width: calc(100% - 32px);
  overflow-x: auto;
}

/* Empty state styling */
.main-table :deep(.ant-empty) {
  margin: 40px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-table-detail {
    margin-left: 16px;
    max-width: calc(100% - 16px);
  }
}
</style>

<style scoped src="/src/assets/pivot-tables.css"></style>
