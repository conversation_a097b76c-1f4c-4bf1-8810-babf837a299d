<script setup lang="ts">
import ProductOutOfStockTable from './components/body/ProductOutOfStockTable.vue';
import ProductOutOfStockHeader from './components/ProductOutOfStockHeader.vue';

</script>

<template>
  <AFlex class="segment-container">
    <ProductOutOfStockHeader />
    <ProductOutOfStockTable />
  </AFlex>
</template>

<style scoped>
.segment-container {
  display: flex;
  flex-direction: column;
}
</style>
