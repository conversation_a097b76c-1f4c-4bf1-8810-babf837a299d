import type {
  HeatmapResponse, SkuOutOfStockRequest,
  SkuOvertimeRequest, AllSkuRequest, AllSkuResponse, SkuItem, SkuStatus
} from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import {
  type PivotTableData,
  PivotTableData as PivotTableDataClass,
  TableDataItem,
  StoreInfo
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import type { ForecastData } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import { SkuOvertimeBreakdown } from '@/views/analytics/views/inventory-detail/components/historical/types.ts'
import repository, { InventoryRepository } from "../repositories/InventoryRepository";
import { formatDateStringToDDMMYYYY } from '@/utils/DateUtils';

export abstract class InventoryService{
  abstract getSkuOvertime(request: SkuOvertimeRequest): Promise<PivotTableData>;

  abstract getSkuOutOfStock(request: SkuOutOfStockRequest): Promise<HeatmapResponse>;

  abstract getAllSkus(request: AllSkuRequest): Promise<AllSkuResponse>;
}

export class InventoryServiceImpl extends InventoryService{
  private readonly repository: InventoryRepository

  constructor(repository: InventoryRepository) {
    super()
    this.repository = repository
  }

  getSkuOvertime(request: SkuOvertimeRequest): Promise<PivotTableData> {
    const mockData = this.createMockSkuOvertimeData(request.breakdown);
    return Promise.resolve(mockData);
  }

  private createMockSkuOvertimeData(breakdown: SkuOvertimeBreakdown): PivotTableData {
    // Tạo danh sách các ngày từ 01/01/2025 đến 21/01/2025
    const dayAxis = [
      "01/01", "02/01", "03/01", "04/01", "05/01",
      "06/01", "07/01", "08/01", "09/01", "10/01",
      "11/01", "12/01", "13/01", "14/01", "15/01",
      "16/01", "17/01", "18/01", "19/01", "20/01",
      "21/01"
    ];

    let tableData: TableDataItem[] = [];

    switch (breakdown) {
      case SkuOvertimeBreakdown.ShelfLife:
        tableData = [
          new TableDataItem(
            "1",
            "Shelf life (Short)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(25, 35),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "2",
            "Shelf life (Mid)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(20, 30),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "3",
            "Shelf life (Long)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(140, 160),
              this.getRandomStatus()
            ))
          )
        ];
        break;

      case SkuOvertimeBreakdown.Category:
        tableData = [
          new TableDataItem(
            "1",
            "Beverages",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(50, 80),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "2",
            "Food & Snacks",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(100, 150),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "3",
            "Personal Care",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(30, 60),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "4",
            "Household Items",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(40, 70),
              this.getRandomStatus()
            ))
          )
        ];
        break;

      case SkuOvertimeBreakdown.FastSlowNon:
        tableData = [
          new TableDataItem(
            "1",
            "Fast Moving",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(80, 120),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "2",
            "Slow Moving",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(20, 40),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "3",
            "Non Moving",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(5, 15),
              this.getRandomStatus()
            ))
          )
        ];
        break;

      default:
        // Default to ShelfLife
        tableData = [
          new TableDataItem(
            "1",
            "Shelf life (Short)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(25, 35),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "2",
            "Shelf life (Mid)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(20, 30),
              this.getRandomStatus()
            ))
          ),
          new TableDataItem(
            "3",
            "Shelf life (Long)",
            dayAxis.map(date => new StoreInfo(
              date,
              this.getRandomInRange(140, 160),
              this.getRandomStatus()
            ))
          )
        ];
    }

    return new PivotTableDataClass(dayAxis, tableData);
  }

  private getRandomInRange(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private getRandomStatus(): "default" | "warning" | "error" | "normal" {
    const statuses = ["default", "warning", "error", "normal"] as const;
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  private getShelfLifeText(shelfLife: number): string {
    if (shelfLife < 30) {
      return "Short";
    } else if (shelfLife < 90) {
      return "Medium";
    } else {
      return "Long";
    }
  }

  getSkuOutOfStock(request: SkuOutOfStockRequest): Promise<HeatmapResponse> {
    const mockHeatmapData = this.createMockHeatmapData();
    return Promise.resolve(mockHeatmapData);
  }

  // getAllSkus(request: AllSkuRequest): Promise<AllSkuResponse> {
  //   const mockData = this.createMockAllSkuData(request);
  //   return Promise.resolve(mockData);
  // }

  async getAllSkus(request: AllSkuRequest): Promise<AllSkuResponse> {
    const data = await this.repository.getAllSkus(request);
    return {
      items: data.data.map(item => {
        return {
          id: item.productId + "-" + item.storeId,
          productName: item.productName,
          store: item.storeName,
          // Giả lập dữ liệu salesVolume cho mỗi SKU
          salesVolume: Array.from({ length: 20 }, () => this.getRandomInRange(100, 200)),
          status: item.status as SkuStatus,
          tonKhoHienTai: item.currentInventory,
          poc: {
            min: item.minPog != "NaN" ? item.minPog : NaN,
            max: item.maxPog != "NaN" ? item.maxPog : NaN,
          },
          daysOnHand: {
            min: item.minDoh != "NaN" ? Number(item.minDoh.toFixed(2)) : NaN,
            max: item.maxDoh != "NaN" ? Number(item.maxDoh.toFixed(2)) : NaN,
          },
          quyLuong: item.conversionRate != "NaN" ? item.conversionRate + " " + item.baseUnit : "",
          duKien: item.expectedOrderQuantity != "NaN" ? item.expectedOrderQuantity.toString() : "",
          ngayNhap: item.expectedReceiveDate ? formatDateStringToDDMMYYYY(item.expectedReceiveDate) : "",
          shelfLife: this.getShelfLifeText(item.shelfLife),
          fsn: ["Fast", "Slow", "Non"][Math.floor(Math.random() * 3)],
          // Full chart data for expandable row
          salesVolumeHistory: this.createMockForecastData()
        }
      }),
      total: data.totalItems,
      page: data.page + 1,
      pageSize: data.size,
    };
  }

  private createMockHeatmapData(): HeatmapResponse {
    // xAxis: ngày từ 1 đến 30 trong tháng
    const xAxis = Array.from({ length: 30 }, (_, i) => (i + 1).toString());

    // yAxis: giờ từ 6 sáng đến 10 tối (6:00 - 22:00)
    const yAxis = Array.from({ length: 17 }, (_, i) => {
      const hour = i + 6;
      return `${hour.toString().padStart(2, '0')}:00`;
    });

    // Tạo data dưới dạng [dayIndex, hourIndex, value]
    const data: number[][] = [];

    // Duyệt qua từng ngày (0-29)
    for (let dayIndex = 0; dayIndex < 30; dayIndex++) {
      // Duyệt qua từng giờ (0-16, tương ứng với 6h-22h)
      for (let hourIndex = 0; hourIndex < 17; hourIndex++) {
        // Tạo random value từ 0 đến 100 (có thể điều chỉnh range tùy ý)
        const randomValue = Math.floor(Math.random() * 101);

        // Thêm vào data array dưới dạng [dayIndex, hourIndex, value]
        data.push([dayIndex, hourIndex, randomValue]);
      }
    }

    return {
      xAxis,
      yAxis,
      data
    };
  }

  private createMockAllSkuData(request: AllSkuRequest): AllSkuResponse {
    const { search = '', page = 1, pageSize = 50 } = request;

    // Mock data for SKUs
    const allSkus: SkuItem[] = [
      {
        id: '1',
        productName: 'KOOKSOONDANG - NƯỚC GẠNH TRUYỀN THỐNG',
        store: 'KFM_HCM_TDU - TMD...',
        salesVolume: [180, 170, 160, 175, 185, 190, 180, 175, 170, 165, 160, 155, 150, 145, 140, 135, 130, 125, 120, 115],
        status: 'Đang thiếu hàng',
        tonKhoHienTai: 30,
        poc: { min: 2, max: 34 },
        daysOnHand: { min: 34, max: 34 },
        quyLuong: '30kg',
        trong: '30',
        duKien: '30',
        ngayNhap: '12/12/2025',
        shelfLife: 'Short',
        fsn: 'Slow',
        salesVolumeHistory: this.createMockForecastData()
      },
      {
        id: '2',
        productName: 'KOOKSOONDANG - NƯỚC GẠNH TRUYỀN THỐNG',
        store: 'KFM_HCM_TDU - Bloc...',
        salesVolume: [160, 165, 170, 175, 180, 185, 190, 185, 180, 175, 170, 165, 160, 155, 150, 145, 140, 135, 130, 125],
        status: 'Dư hàng',
        tonKhoHienTai: 25,
        poc: { min: 2, max: 33 },
        daysOnHand: { min: 33, max: 33 },
        quyLuong: '25kg',
        trong: '25',
        duKien: '25',
        ngayNhap: '12/12/2025',
        shelfLife: 'Medium',
        fsn: 'Non',
        salesVolumeHistory: this.createMockForecastData()
      },
      {
        id: '3',
        productName: 'KOOKSOONDANG - NƯỚC GẠNH TRUYỀN THỐNG',
        store: 'KFM_HCM_TDU - A0.0...',
        salesVolume: [140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 185, 180, 175, 170, 165, 160, 155, 150, 145],
        status: 'Hết hàng',
        tonKhoHienTai: 150,
        poc: { min: 2, max: 11 },
        daysOnHand: { min: 11, max: 11 },
        quyLuong: '150kg',
        trong: '150',
        duKien: '150',
        ngayNhap: '12/12/2025',
        shelfLife: 'Long',
        fsn: 'Fast',
        salesVolumeHistory: this.createMockForecastData()
      },
      {
        id: '4',
        productName: 'KOOKSOONDANG - NƯỚC GẠNH TRUYỀN THỐNG',
        store: 'KFM_HCM_TDU - The ...',
        salesVolume: [120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 185, 180, 175, 170, 165],
        status: 'Đủ hàng',
        tonKhoHienTai: 120,
        poc: { min: 2, max: 22 },
        daysOnHand: { min: 22, max: 28 },
        quyLuong: '120kg',
        trong: '120',
        duKien: '120',
        ngayNhap: '12/12/2025',
        shelfLife: 'Medium',
        fsn: 'Fast',
        salesVolumeHistory: this.createMockForecastData()
      }
    ];

    // Generate more mock data
    const expandedSkus: SkuItem[] = [];
    for (let i = 0; i < 100; i++) {
      const baseIndex = i % allSkus.length;
      const baseSku = allSkus[baseIndex];
      expandedSkus.push({
        ...baseSku,
        id: `${i + 1}`,
        productName: `${baseSku.productName} - ${i + 1}`,
        tonKhoHienTai: this.getRandomInRange(20, 200),
        salesVolume: Array.from({ length: 20 }, () => this.getRandomInRange(100, 200)),
        status: this.getRandomSkuStatus(),
        salesVolumeHistory: this.createMockForecastData()
      });
    }

    // Filter by search
    let filteredSkus = expandedSkus;
    if (search) {
      filteredSkus = expandedSkus.filter(sku =>
        sku.productName.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSkus = filteredSkus.slice(startIndex, endIndex);

    return {
      items: paginatedSkus,
      total: filteredSkus.length,
      page,
      pageSize
    };
  }

  private getRandomSkuStatus(): SkuStatus {
    const statuses: SkuStatus[] = ['Đang thiếu hàng', 'Dư hàng', 'Hết hàng', 'Đủ hàng'];
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  private createMockForecastData(): ForecastData {
    const categories = Array.from({ length: 21 }, (_, i) => ({
      date: `2025-01-${String(i + 1).padStart(2, '0')}`,
      isWeekend: (i + 1) % 7 === 0 || (i + 1) % 7 === 6 ? 1 : 0,
      weatherCondition: i % 3 === 0 ? 'sunny' : i % 3 === 1 ? 'cloudy' : '',
      forecast: i > 15 // Last 5 days are forecast
    }));

    const saleVolumes = Array.from({ length: 21 }, () => this.getRandomInRange(100, 200));
    const saleVolumesPredict = Array.from({ length: 21 }, () => this.getRandomInRange(90, 180));
    const seriesData = Array.from({ length: 21 }, () => ({
      quantity: this.getRandomInRange(150, 250),
      skuPerDay: this.getRandomInRange(10, 30)
    }));

    return {
      categories,
      saleVolumes,
      saleVolumesPredict,
      voucherPrograms: Array.from({ length: 21 }, () => ''),
      seriesData
    };
  }

}

export default new InventoryServiceImpl(repository);
