import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import type { SkuOvertimeRequest } from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import inventoryService from '@/views/analytics/views/inventory-detail/services/InventoryService.ts'

export const useSkuOvertimeStore = defineStore('skuOvertime', () => {
  // State
  const data = ref<PivotTableData | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastRequest = ref<SkuOvertimeRequest | null>(null)

  // Computed
  const hasData = computed(() => data.value !== null)
  const hasError = computed(() => error.value !== null)
  const isEmpty = computed(() => !hasData.value && !loading.value)

  // Actions
  const loadSkuOvertime = async (request: SkuOvertimeRequest) => {
    try {
      loading.value = true
      error.value = null
      lastRequest.value = request
      data.value = await inventoryService.getSkuOvertime(request)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Đã xảy ra lỗi khi tải dữ liệu'
      data.value = null
    } finally {
      loading.value = false
    }
  }

  const refreshData = async () => {
    if (lastRequest.value) {
      await loadSkuOvertime(lastRequest.value)
    }
  }

  const clearData = () => {
    data.value = null
    error.value = null
    lastRequest.value = null
  }

  const clearError = () => {
    error.value = null
  }

  const dataColumns = computed(() => []);

  return {
    // State
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    lastRequest: readonly(lastRequest),
    dataColumns: readonly(dataColumns),

    // Computed
    hasData,
    hasError,
    isEmpty,

    // Actions
    loadSkuOvertime,
    refreshData,
    clearData,
    clearError,
  }
})
