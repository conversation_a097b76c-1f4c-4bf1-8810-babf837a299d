import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import type {
  AllSkuRequest,
  SkuItem
} from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import inventoryService from '@/views/analytics/views/inventory-detail/services/InventoryService.ts'

export const useAllSkuStore = defineStore('allSku', () => {
  // State
  const data = ref<SkuItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastRequest = ref<AllSkuRequest | null>(null)

  // Pagination state
  const pagination = ref({
    current: 1,
    pageSize: 50,
    total: 0
  })

  // Filter state
  const filters = ref({
    search: '',
    status: null as string | null
  })

  // Computed
  const hasData = computed(() => data.value.length > 0)
  const hasError = computed(() => error.value !== null)
  const isEmpty = computed(() => !hasData.value && !loading.value && !hasError.value)

  // Actions
  const loadAllSkus = async (request?: Partial<AllSkuRequest>) => {
    try {
      loading.value = true
      error.value = null

      const finalRequest: AllSkuRequest = {
        search: filters.value.search,
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        ...request
      }

      lastRequest.value = finalRequest
      const response = await inventoryService.getAllSkus(finalRequest)

      data.value = response.items
      pagination.value = {
        current: response.page,
        pageSize: response.pageSize,
        total: response.total
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Đã xảy ra lỗi khi tải dữ liệu SKU'
      data.value = []
    } finally {
      loading.value = false
    }
  }

  const refreshData = async () => {
    if (lastRequest.value) {
      await loadAllSkus(lastRequest.value)
    } else {
      await loadAllSkus()
    }
  }

  const updateSearch = async (searchValue: string) => {
    filters.value.search = searchValue
    pagination.value.current = 1 // Reset to first page when searching
    await loadAllSkus({
      search: searchValue,
      page: 1
    })
  }

  const updatePagination = async (page: number, pageSize?: number) => {
    pagination.value.current = page
    if (pageSize) {
      pagination.value.pageSize = pageSize
    }

    await loadAllSkus({
      page,
      pageSize: pagination.value.pageSize
    })
  }

  const clearData = () => {
    data.value = []
    error.value = null
    lastRequest.value = null
    pagination.value = {
      current: 1,
      pageSize: 50,
      total: 0
    }
    filters.value = {
      search: '',
      status: null
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Get SKU by ID
  const getSkuById = (id: string): SkuItem | undefined => {
    return data.value.find(sku => sku.id === id)
  }

  return {
    // State
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    lastRequest: readonly(lastRequest),
    pagination: readonly(pagination),
    filters: readonly(filters),

    // Computed
    hasData,
    hasError,
    isEmpty,

    // Actions
    loadAllSkus,
    refreshData,
    updateSearch,
    updatePagination,
    clearData,
    clearError,
    getSkuById
  }
})
