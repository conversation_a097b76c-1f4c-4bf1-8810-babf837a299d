import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import type {
  HeatmapResponse,
  SkuOutOfStockRequest,
} from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import inventoryService from '@/views/analytics/views/inventory-detail/services/InventoryService.ts'

export const useSkuOutOfStockStore = defineStore('skuOutOfStock', () => {
  // State
  const data = ref<HeatmapResponse | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastRequest = ref<SkuOutOfStockRequest | null>(null)

  // Computed
  const hasData = computed(() => data.value !== null)
  const hasError = computed(() => error.value !== null)
  const isEmpty = computed(() => !hasData.value && !loading.value)

  // Actions
  const loadSkuOutOfStock = async (request: SkuOutOfStockRequest) => {
    try {
      loading.value = true
      error.value = null
      lastRequest.value = request
      data.value = await inventoryService.getSkuOutOfStock(request)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Đã xảy ra lỗi khi tải dữ liệu'
      data.value = null
    } finally {
      loading.value = false
    }
  }

  const refreshData = async () => {
    if (lastRequest.value) {
      await loadSkuOutOfStock(lastRequest.value)
    }
  }

  const clearData = () => {
    data.value = null
    error.value = null
    lastRequest.value = null
  }

  const clearError = () => {
    error.value = null
  }

  const dataColumns = computed(() => []);

  return {
    // State
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    lastRequest: readonly(lastRequest),
    dataColumns: readonly(dataColumns),

    // Computed
    hasData,
    hasError,
    isEmpty,

    // Actions
    loadSkuOutOfStock,
    refreshData,
    clearData,
    clearError,
  }
})
