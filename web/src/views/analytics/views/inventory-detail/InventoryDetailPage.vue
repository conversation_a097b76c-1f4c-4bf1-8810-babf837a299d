<script setup lang="ts">
import InventoryDetailOverview from '@/views/analytics/views/inventory-detail/components/overview/InventoryDetailOverview.vue'
import InventoryHistorical from '@/views/analytics/views/inventory-detail/components/InventoryHistorical.vue'
import AllSkuTable from '@/views/analytics/views/inventory-detail/components/all-sku/AllSkuTable.vue'
import SkuPerformance from '@/views/analytics/views/inventory-detail/components/sku-performance/SkuPerformance.vue'
</script>

<template>
  <div id="inventory-detail">
    <SkuPerformance id="sku-performance"/>
    <InventoryDetailOverview id="inventory-overview" />
    <InventoryHistorical id="inventory-historical"/>
    <AllSkuTable id="all-sku" />
  </div>
</template>

<style>
#inventory-detail {
  display: grid;
  max-width: calc(100vw - 64px);
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(12, auto); /* auto để hàng tự điều chỉnh theo nội dung */
  gap: 16px;
  grid-template-areas:
    "sku sku ov ov ov ov ov ov"
    "sku sku ov ov ov ov ov ov"
    "sku sku hist hist hist hist hist hist"
    "sku sku hist hist hist hist hist hist"
    "sku sku hist hist hist hist hist hist"
    "sku sku hist hist hist hist hist hist"
    ". . all all all all all all"
    ". . all all all all all all"
    ". . all all all all all all"
    ". . all all all all all all";
}

#sku-performance {
  grid-area: sku;
}

#inventory-overview {
  grid-area: ov;
}

#inventory-historical {
  grid-area: hist;
}

#all-sku {
  grid-area: all;
}


@media (max-width: 1024px) {
  #inventory-detail {
    grid-template-columns: repeat(1, 2fr);
    grid-template-rows: repeat(12, auto);
    grid-template-areas:
      "sku ov"
      "hist hist"
      "all all";
  }
}

@media (max-width: 768px) {
  #inventory-detail {
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(12, auto);
    grid-template-areas:
      "sku"
      "ov"
      "hist"
      "all";
  }
}

</style>
