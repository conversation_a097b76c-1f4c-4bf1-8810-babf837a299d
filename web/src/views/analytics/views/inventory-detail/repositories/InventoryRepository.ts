import client from '@/client/HttpClientBuilder.ts'
import type { HttpClient } from '@/client/HttpClient'
import type { AllSkuRequest, AllSkuTableResponse,  } from '../domains/Request'

export abstract class InventoryRepository {
  abstract getAllSkus(request: AllSkuRequest): Promise<AllSkuTableResponse>;
}

export class InventoryRepositoryImpl extends InventoryRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getAllSkus(request: AllSkuRequest): Promise<AllSkuTableResponse> {
    return this.client
      .post<AllSkuTableResponse>('/api/inventory-detail/get-skus', {
        search: request.search,
        pageNumber: request.page ? request.page - 1 : 0,
        pageSize: request.pageSize,
      })
      .then((response) => response.data);
  }
}

export default new InventoryRepositoryImpl(client);
