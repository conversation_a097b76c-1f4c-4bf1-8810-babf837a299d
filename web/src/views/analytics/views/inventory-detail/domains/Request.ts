import { SkuOvertimeBreakdown } from '@/views/analytics/views/inventory-detail/components/historical/types.ts'
import type { TimeStampAsSeconds } from '@/views/analytics/views/replenishment/domains'
import type { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import type { ForecastData } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'

export type SkuOvertimeRequest = {
  breakdown: SkuOvertimeBreakdown
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  dateHistogram: DateHistogram
}

export type SkuOutOfStockRequest = {
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  dateHistogram: DateHistogram
}


export type HeatmapResponse = {
  xAxis: string[]
  yAxis: string[]
  data: any[][]
}

// All SKU types
export type AllSkuRequest = {
  search?: string
  page?: number
  pageSize?: number
  sortField?: string
  sortOrder?: 'ascend' | 'descend'
}

export type SkuStatus = '<PERSON><PERSON> thiếu hàng' | 'Dư hàng' | 'Hết hàng' | '<PERSON><PERSON> hàng'

export type SkuItem = {
  id: string
  productName: string
  store: string
  salesVolume: number[]  // Mini chart data for table display
  status: SkuStatus
  tonKhoHienTai: number
  poc: { min: number; max: number }
  daysOnHand: { min: number; max: number }
  quyLuong: string
  trong?: string
  duKien: string
  ngayNhap: string
  shelfLife: string
  fsn: string
  // Full chart data for expandable row
  salesVolumeHistory: ForecastData
}

export type AllSkuResponse = {
  items: SkuItem[]
  total: number
  page: number
  pageSize: number
}

export type SkuTableRow = {
  productId: string;
  productName: string;
  barcode: string;
  baseUnit: string;
  storeId: string;
  storeName: string;
  status: string;
  currentInventory: number;
  minPog: number | "NaN";
  maxPog: number | "NaN";
  minDoh: number | "NaN";
  maxDoh: number | "NaN";
  conversionRate: number | "NaN";
  expectedOrderQuantity: number | "NaN";
  expectedReceiveDate: string;
  shelfLife: number;
  fsn: string;
}

export type AllSkuTableResponse = {
  totalItems: number;
  totalPages: number;
  page: number;
  size: number;
  data: SkuTableRow[]
}
