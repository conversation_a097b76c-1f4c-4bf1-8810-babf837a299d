<template>
  <div class="all-sku-table-container">
    <!-- Loading State -->
    <div v-if="store.loading && !store.hasData" class="loading-container">
      <Spin size="large" tip="Đang tải dữ liệu SKU...">
        <div class="loading-placeholder">
          <Skeleton active :paragraph="{ rows: 8 }" />
        </div>
      </Spin>
    </div>

    <!-- Error State -->
    <div v-else-if="store.hasError" class="error-container">
      <Alert
        :message="store.error"
        type="error"
        show-icon
        closable
        @close="store.clearError"
      >
        <template #action>
          <Button size="small" type="primary" @click="handleRetry">
            Thử lại
          </Button>
        </template>
      </Alert>
    </div>

    <!-- Empty State -->
    <div v-else-if="store.isEmpty" class="empty-container">
      <Empty
        description="Không có dữ liệu SKU"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <Button type="primary" @click="handleLoadData">
          Tải dữ liệu
        </Button>
      </Empty>
    </div>

    <!-- Data State -->
    <div v-else class="data-container">
      <AllSkuTableContent
        :data-source="store.data as SkuItem[]"
        :loading="store.loading"
        :pagination="{
          current: store.pagination.current,
          pageSize: store.pagination.pageSize,
          total: store.pagination.total
        }"
        @search="handleSearch"
        @pagination-change="handlePaginationChange"
        @expand-change="handleExpandChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { Spin, Skeleton, Alert, Button, Empty } from 'ant-design-vue'
import { useAllSkuStore } from '@/views/analytics/views/inventory-detail/stores/AllSkuStore.ts'
import AllSkuTableContent from './AllSkuTableContent.vue'
import type { SkuItem } from '@/views/analytics/views/inventory-detail/domains/Request.ts'

// Store
const store = useAllSkuStore()

// Event handlers
const handleLoadData = async () => {
  await store.loadAllSkus()
}

const handleRetry = async () => {
  store.clearError()
  await store.refreshData()
}

const handleSearch = async (searchValue: string) => {
  await store.updateSearch(searchValue)
}

const handlePaginationChange = async (page: number, pageSize: number) => {
  await store.updatePagination(page, pageSize)
}

const handleExpandChange = (expandedRowKeys: (string | number)[]) => {
  console.log('Expanded rows:', expandedRowKeys)
  // Handle expand change if needed
}


// Lifecycle
onMounted(async () => {
  // Load initial data
  await handleLoadData()
})

onUnmounted(() => {
})

// Expose methods for parent component
defineExpose({
  refresh: handleRetry,
  loadData: handleLoadData,
  store
})
</script>

<style scoped>
.all-sku-table-container {
  width: 100%;
  min-height: 400px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
}

.loading-placeholder {
  width: 100%;
  max-width: 800px;
  padding: 24px;
}

.error-container {
  padding: 24px;
  background: white;
  border-radius: 8px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
}

.data-container {
  width: 100%;
}

/* Loading overlay for data refresh */
.data-container :deep(.ant-spin-container) {
  position: relative;
}

.data-container :deep(.ant-spin) {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}
</style>
