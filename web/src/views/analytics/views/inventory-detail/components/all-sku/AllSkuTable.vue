<template>
  <AllSkuTableContainer ref="containerRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AllSkuTableContainer from './AllSkuTableContainer.vue'

// Refs
const containerRef = ref<InstanceType<typeof AllSkuTableContainer>>()

// Expose methods for parent components
const refresh = () => {
  containerRef.value?.refresh()
}

const loadData = () => {
  containerRef.value?.loadData()
}

const getStore = () => {
  return containerRef.value?.store
}

defineExpose({
  refresh,
  loadData,
  getStore,
})
</script>
