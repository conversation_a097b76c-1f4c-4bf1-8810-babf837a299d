<template>
  <div class="all-sku-table-content">
    <!-- Toolbar slot for search -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <h3>ALL SKUS</h3>
      </div>
      <div class="toolbar-right">
        <Input.Search
          v-model:value="searchValue"
          placeholder="Nhập tên sản phẩm, siêu thị"
          style="width: 300px"
          @search="handleSearch"
          @change="handleSearchChange"
        />
        <Button type="text" :icon="h(FilterOutlined)" />
      </div>
    </div>

    <ATable
      ref="tableRef"
      :columns="tableColumns"
      :data-source="dataSource"
      :loading="loading"
      :scroll="{ x: 'max-content', y: 600 }"
      :pagination="{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: (page, pageSize) => emit('paginationChange', page, pageSize),
        onShowSizeChange: (current, size) => emit('paginationChange', current, size),
      }"
      :expandIcon="() => null"
      expandRowByClick
      @expand="handleExpandChange"
      row-key="id"
      :show-expand-column="showExpandColumn"
      size="small"
      bordered
      class="all-sku-table"
    >
      <template #expandedRowRender="{ record }">
        <SkuSalesVolumeChart class="expanded-chart" :record="record" :index="0" />
      </template>
    </ATable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, h } from 'vue'
import { Input, Button, Tag, Table as ATable, type TableColumnType } from 'ant-design-vue'
import { FilterOutlined, ExportOutlined } from '@ant-design/icons-vue'
import SkuSalesVolumeChart from './SkuSalesVolumeChart.vue'
import ProductNameCell
  from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import SaleVolumeSparkLine from '@/components/charts/SaleVolumeSparkLine.vue'
import type {
  SkuItem,
  SkuStatus
} from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import StatusCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/StatusCell.vue'
import ShelfLifeCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ShelfLifeCell.vue'

interface Props {
  dataSource: SkuItem[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
  }
  showExpandColumn?: boolean
}

interface Emits {
  search: [value: string]
  paginationChange: [page: number, pageSize: number]
  expandChange: [expandedRowKeys: (string | number)[]]
}

withDefaults(defineProps<Props>(), {
  loading: false,
  pagination: () => ({
    current: 1,
    pageSize: 50,
    total: 0
  }),
  showExpandColumn: false
})

const emit = defineEmits<Emits>()

// Search state
const searchValue = ref('')
const expandedRows = ref<(string | number)[]>([])

// Table ref
const tableRef = ref<InstanceType<typeof ATable>>()

// Status color mapping
const getStatusColor = (status: SkuStatus): string => {
  switch (status) {
    case 'Đang thiếu hàng':
      return '#B54708'
    case 'Dư hàng':
      return '#004EEB'
    case 'Hết hàng':
      return '#C11574'
    case 'Đủ hàng':
      return '#087443'
    default:
      return 'default'
  }
}

const getBackgroundStatusColor = (status: SkuStatus): string => {
  switch (status) {
    case 'Đang thiếu hàng':
      return '#FFFAEB'
    case 'Dư hàng':
      return '#EFF4FF'
    case 'Hết hàng':
      return '#FDF2FA'
    case 'Đủ hàng':
      return '#EDFCF2'
    default:
      return 'default'
  }
}

// Table columns configuration
const tableColumns = computed<TableColumnType[]>(() => [
  {
    title: '#',
    key: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: 'Product name',
    key: 'productName',
    dataIndex: 'productName',
    width: 300,
    ellipsis: true,
    resizable: true,
    customRender: ({ record }) => {
      const isExpanded = expandedRows.value.includes(record.id)
      return h(ProductNameCell, {
        productName: record.productName,
        expanded: {
          enabled: true,
          isExpanded: isExpanded,
        },
      })
    }
  },
  {
    title: 'Store',
    key: 'store',
    dataIndex: 'store',
    width: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: 'Sales volume',
    key: 'salesVolume',
    dataIndex: 'salesVolume',
    width: 154,
    align: 'center',
    customRender: () => {
      // Generate sample data for sparkline (in real app, this would come from record.salesVolume)
      const sampleData = [10, 15, 12, 18, 14, 20, 16, 8, 6, 4, 3, 2]
      return h(SaleVolumeSparkLine, {
        data: sampleData,
        width: 154,
        height: 24
      })
    }
  },
  {
    title: 'Status',
    key: 'status',
    dataIndex: 'status',
    width: 140,
    align: 'center',
    customRender: ({ text }) => h(StatusCell, { color: getStatusColor(text), backgroundColor: getBackgroundStatusColor(text),status: text }),
  },
  {
    title: 'Tồn kho hiện tại',
    key: 'tonKhoHienTai',
    dataIndex: 'tonKhoHienTai',
    width: 120,
    align: 'right'
  },
  {
    title: 'POC',
    key: 'poc',
    children: [
      {
        title: 'Min',
        key: 'pocMin',
        width: 80,
        align: 'right',
        customRender: ({ record }: { record: SkuItem }) => record.poc.min
      },
      {
        title: 'Max',
        key: 'pocMax',
        width: 80,
        align: 'right',
        customRender: ({ record }: { record: SkuItem }) => record.poc.max
      }
    ]
  },
  {
    title: 'Days on hand',
    key: 'daysOnHand',
    children: [
      {
        title: 'Min',
        key: 'dohMin',
        width: 80,
        align: 'right',
        customRender: ({ record }: { record: SkuItem }) => record.daysOnHand.min
      },
      {
        title: 'Max',
        key: 'dohMax',
        width: 80,
        align: 'right',
        customRender: ({ record }: { record: SkuItem }) => record.daysOnHand.max
      }
    ]
  },
  {
    title: 'Quy cách chia',
    key: 'quyLuong',
    dataIndex: 'quyLuong',
    width: 100,
    align: 'center'
  },
  {
    title: 'Dự kiến đặt',
    key: 'duKien',
    dataIndex: 'duKien',
    width: 80,
    align: 'right'
  },
  {
    title: 'Ngày nhập dự kiến',
    key: 'ngayNhap',
    dataIndex: 'ngayNhap',
    width: 120,
    align: 'center'
  },
  {
    title: 'Shelf life',
    key: 'shelfLife',
    dataIndex: 'shelfLife',
    width: 100,
    align: 'center',
    customRender: ({ text }) => h(ShelfLifeCell, { shelfLife: text }),
  },
  {
    title: 'FSN',
    key: 'fsn',
    dataIndex: 'fsn',
    width: 80,
    align: 'center'
  },
  {
    title: '',
    key: 'actions',
    fixed: 'right',
    width: 55,
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Button,
        {
          type: 'text',
          size: 'small',
          icon: h(ExportOutlined),
          onClick: (event: Event) => {
            event.stopPropagation()
            console.log('Action 2:', record.productName)
          }
        }
      )
    }
  }
])

// Event handlers
const handleSearch = (value: string) => {
  emit('search', value)
}

const handleSearchChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  searchValue.value = target.value
}

const handleExpandChange = (expanded: boolean, record: SkuItem) => {
  if (expanded) {
    expandedRows.value.push(record.id)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.id)
  }
  emit('expandChange', expandedRows.value)
}
</script>

<style scoped>
.all-sku-table-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 1px;
  height: 30px;
  width: 100px;
}

.mini-chart-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.2s ease;
}

.mini-chart-bar:hover {
  opacity: 0.8;
}

.expanded-chart {
  margin: 16px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.all-sku-table :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  cursor: pointer;
}
</style>
