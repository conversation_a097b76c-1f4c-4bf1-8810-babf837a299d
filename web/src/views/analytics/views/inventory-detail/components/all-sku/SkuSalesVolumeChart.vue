<template>
  <div class="sku-sales-volume-chart">
    <div class="chart-header">
      <h4>Sales volume history</h4>
    </div>
    <div class="chart-container">
      <ForecastChart 
        v-if="record.salesVolumeHistory" 
        :forecast-data="record.salesVolumeHistory" 
      />
      <div v-else class="no-chart">
        <Empty description="No chart data available" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue'
import ForecastChart from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/forecast-chart/ForecastChart.vue'
import type { SkuItem } from '@/views/analytics/views/inventory-detail/domains/Request.ts'

interface Props {
  record: SkuItem
  index: number
}

const props = defineProps<Props>()
</script>

<style scoped>
.sku-sales-volume-chart {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-container {
  min-height: 300px;
  background: white;
  border-radius: 4px;
  padding: 16px;
}

.no-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
