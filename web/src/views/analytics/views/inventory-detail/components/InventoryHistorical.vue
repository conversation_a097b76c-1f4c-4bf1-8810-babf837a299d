<script setup lang="ts">
import SkuOvertimeWidget from '@/views/analytics/views/inventory-detail/components/historical/SkuOvertimeWidget.vue'
import { ref } from 'vue'
import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { DateHistogramLabels } from '@/views/analytics/views/inventory-detail/const/DateHistogramLabels.ts'
import { DataDisplay } from '@/views/analytics/views/inventory-detail/enums/DataDisplay.ts'
import { DataDisplayLabels } from '@/views/analytics/views/inventory-detail/const/DataDisplayLabels.ts'
import SkuOutOfStockWidget
  from '@/views/analytics/views/inventory-detail/components/out-of-stock/SkuOutOfStockWidget.vue'

const dateHistogram = ref(DateHistogram.DayOf)

const dataDisplay = ref(DataDisplay.Chart)
</script>

<template>
  <div id="inventory-historical">
    <div id="inventory-historical--header">
      <ATypographyTitle :level="5">Inventory historical data</ATypographyTitle>
      <div class="inventory-historical--header--actions">
        <ARadioGroup v-model:value="dateHistogram">
          <ARadioButton :value="DateHistogram.DayOf">
            {{ DateHistogramLabels[DateHistogram.DayOf] }}
          </ARadioButton>

          <ARadioButton :value="DateHistogram.WeekOf">
            {{ DateHistogramLabels[DateHistogram.WeekOf] }}
          </ARadioButton>

          <ARadioButton :value="DateHistogram.MonthOf">
            {{ DateHistogramLabels[DateHistogram.MonthOf] }}
          </ARadioButton>

          <ARadioButton :value="DateHistogram.YearOf">
            {{ DateHistogramLabels[DateHistogram.YearOf] }}
          </ARadioButton>
        </ARadioGroup>
        <ARadioGroup v-model:value="dataDisplay">
          <ARadioButton v-for="label in DataDisplayLabels" :key="label[0]" :value="label[0]">
            <div
              class="radio-button-icon-container"
              :[`data`]="dataDisplay === label[0] ? 'active' : ''"
            >
              <component :is="label[1]" color="var(--fill-color)" />
              <a style="color: var(--fill-color)">{{ label[2] }}</a>
            </div>
          </ARadioButton>
        </ARadioGroup>
      </div>
    </div>
    <div id="sku-overtime-chart" class="chart-container">
      <SkuOvertimeWidget :display="dataDisplay" :date-histogram="dateHistogram" />
    </div>

    <div id="sku-out-of-stock-chart" class="chart-container">
      <SkuOutOfStockWidget :display="dataDisplay" :date-histogram="dateHistogram" />
    </div>
  </div>
</template>

<style>
#inventory-historical {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

#inventory-historical #inventory-historical--header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}


.inventory-historical--header--actions{
  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.radio-button-icon-container {
  --fill-color: #667085;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.radio-button-icon-container[data='active'] {
  --fill-color: #155eef;
}
</style>
