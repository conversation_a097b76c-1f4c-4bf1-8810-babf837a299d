<script setup lang="ts">

import TotalSkuPieChart from '@/views/analytics/views/inventory-detail/components/overview/total-sku-overview/TotalSkuPieChart.vue'
import { ShopOutlined } from '@ant-design/icons-vue'
import MetricCard from '@/views/analytics/components/MetricCard.vue'
import BreakdownSelect from '@/views/analytics/views/inventory-detail/components/BreakdownSelect.vue'
</script>

<template>
  <MetricCard card-color="#2970FF">
    <div class="card-content">
      <ATypography class="card-title">
        <div>
          <ShopOutlined />
          <span style="margin-left: 6px">Total SKU</span>
        </div>
        <BreakdownSelect />
      </ATypography>
      <AFlex flexDirection="row" align="center" justify="space-between">
        <span class="card-metric-text" :style="{ color: `#2970FF` }">123.4M</span>
      </AFlex>

      <ADivider dashed="dashed" />

      <TotalSkuPieChart />
    </div>
  </MetricCard>
</template>

<style scoped src="../overview.css"/>
