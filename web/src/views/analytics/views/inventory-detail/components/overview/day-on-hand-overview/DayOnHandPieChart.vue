<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'

interface Props {}

const props = defineProps<Props>()

const chartOptions = computed(() => ({
  chart: {
    type: 'pie',
    height: 150,
  },
  exporting: {
    enabled: false,
  },
  credits: {
    enabled: false,
  },
  title: {
    text: '',
    enabled: false,
  },

  plotOptions: {
    pie: {
      allowPointSelect: true,
      size: '140%',
      cursor: 'pointer',
      dataLabels: {
        enabled: false,
      },
      showInLegend: true,
    },
  },
  legend: {
    layout: 'vertical',
    align: 'left',
    verticalAlign: 'middle',
    borderWidth: 1,
    borderColor: '#EAECF0',
    borderRadius: 8,
  },
  colors: ['#F79009', '#155EEF', '#EE46BC'],
  series: [
    {
      name: 'Total SKU',
      colorByPoint: true,
      data: [
        {
          name: 'Shelf life (Long)',
          y: 33.0,
        },
        {
          name: 'Shelf life (Mid)',
          y: 61.0,
        },
        {
          name: 'Shelf life (Short)',
          y: 6.0,
        },
      ],
    },
  ],
}))
const windowWidth = ref(window.innerWidth)
const chartRef = ref<any>(null)

const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (chartRef.value && chartRef.value.chart) {
    setTimeout(() => {
      chartRef.value.chart.reflow()
    }, 100)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <highcharts ref="chartRef" :options="chartOptions" />
</template>

<style scoped></style>
