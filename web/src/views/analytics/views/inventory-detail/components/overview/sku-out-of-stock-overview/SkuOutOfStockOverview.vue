<script setup lang="ts">

import { h } from 'vue'
import { InfoCircleOutlined, ShopOutlined } from '@ant-design/icons-vue'
import SkuOutofStockPieChart
  from '@/views/analytics/views/inventory-detail/components/overview/sku-out-of-stock-overview/SkuOutofStockPieChart.vue'
import MetricCard from '@/views/analytics/components/MetricCard.vue'
</script>

<template>
  <MetricCard card-color="#F79009">
    <div class="card-content">
      <ATypography class="card-title">
        <div>
          <ShopOutlined />
          <span style="margin-left: 6px">Số SKU sắp hết hàng</span>
        </div>
      </ATypography>
      <AFlex flexDirection="row" align="center" justify="space-between">
        <div>
          <span class="card-metric-text" :style="{ color: `#F79009` }">20</span>
          <span class="card-content-text">/100</span>
        </div>
        <AButton type="text" :icon="h(InfoCircleOutlined)" />
      </AFlex>

      <ADivider dashed="dashed" />

      <SkuOutofStockPieChart />
    </div>
  </MetricCard>
</template>
<style scoped src="../overview.css"/>
