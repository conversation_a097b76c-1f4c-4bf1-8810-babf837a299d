<script setup lang="ts">
import DayOnHandOverview from '@/views/analytics/views/inventory-detail/components/overview/day-on-hand-overview/DayOnHandOverview.vue'
import TotalSkuOverview from '@/views/analytics/views/inventory-detail/components/overview/total-sku-overview/TotalSkuOverview.vue'
import SkuOutOfStockOverview from '@/views/analytics/views/inventory-detail/components/overview/sku-out-of-stock-overview/SkuOutOfStockOverview.vue'
import SelectStoreButton from '@/components/SelectStoreButton.vue'
</script>

<template>
  <div id="inventory-overview">
    <div id="inventory-overview--header">
      <ATypographyTitle :level="5">Inventory Metric</ATypographyTitle>
      <SelectStoreButton />
    </div>

    <div id="inventory-overview--body">
      <SkuOutOfStockOverview />
      <TotalSkuOverview />
      <DayOnHandOverview />
    </div>
  </div>
</template>

<style>
#inventory-overview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

#inventory-overview #inventory-overview--header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

#inventory-overview #inventory-overview--body {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;
}

@media (max-width: 768px) {
  #inventory-overview #inventory-overview--body {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  #inventory-overview #inventory-overview--body {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

