:deep(.ant-card-body) {
  padding: 0.75rem 1rem;
}

:deep(.ant-divider-dashed) {
  margin: 4px 0;
}

.metric-container {
  display: flex;
  flex-direction: column;
}

.metric-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  width: 100%;
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #667085;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-metric-text {
  font-size: 26px;
  font-weight: 700;
}

.card-content-text {
  font-size: 20px;
  font-weight: 700;
  color: #667085;
}

.metric-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #667085;
  padding: 0.5rem 0;
  border-radius: 8px;
}

.metric-detail {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.metric-detail-mid {
  border-left: 1px solid #eaecf0;
  border-right: 1px solid #eaecf0;
}

.metric-detail span {
  font-size: 14px;
  font-weight: 700;
  color: #000;
}

.dropdown-menu {
  color: #000;
  font-weight: 500;
  font-size: 14px;
}
