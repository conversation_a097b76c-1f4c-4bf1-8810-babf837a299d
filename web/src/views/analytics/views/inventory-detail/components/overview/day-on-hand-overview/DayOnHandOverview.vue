<script setup lang="ts">

import { h } from 'vue'
import { InfoCircleOutlined, ShopOutlined } from '@ant-design/icons-vue'
import MetricCard from '@/views/analytics/components/MetricCard.vue'
import DayOnHandPieChart
  from '@/views/analytics/views/inventory-detail/components/overview/day-on-hand-overview/DayOnHandPieChart.vue'
import BreakdownSelect from '@/views/analytics/views/inventory-detail/components/BreakdownSelect.vue'
</script>

<template>
  <MetricCard card-color="#0BA5EC">
    <div class="card-content">
      <ATypography class="card-title">
        <div>
          <ShopOutlined />
          <span style="margin-left: 6px">Days on hand (DoH)</span>
        </div>
        <BreakdownSelect />
      </ATypography>
      <AFlex flexDirection="row" align="center" justify="space-between">
        <div>
          <span class="card-metric-text" :style="{ color: `#0BA5EC` }">10</span>
          <span class="card-content-text"> day</span>
        </div>
        <AButton type="text" :icon="h(InfoCircleOutlined)" />
      </AFlex>

      <ADivider dashed="dashed" />

      <DayOnHandPieChart />
    </div>
  </MetricCard>
</template>
<style scoped src="../overview.css"/>
