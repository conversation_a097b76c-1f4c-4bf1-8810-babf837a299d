<script setup lang="ts">
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import { computed, onMounted, onUnmounted, ref } from 'vue'

interface Props {
  data: PivotTableData
}

const props = defineProps<Props>()

const chartOptions = computed(() => ({
  chart: {
    type: 'column',
    height: 286,
  },
  exporting: {
    enabled: false,
  },
  credits: {
    enabled: false,
  },
  title: {
    text: '',
    enabled: false,
  },
  xAxis: {
    categories: props.data.dayAxis,
  },
  yAxis: {
    title: {
      text: '',
    },
  },
  plotOptions: {
    column: {
      stacking: 'normal',
      borderWidth: 0,
      borderRadius: 0,
    },
  },
  colors: ['#16B364', '#F79009', '#00359E'],
  series: props.data.tableData.map((item) => {
    return {
      name: item.metricName,
      data: item.storeInfos.map((info) => info.value),
    }
  }),
}))
const windowWidth = ref(window.innerWidth)
const chartRef = ref<any>(null)

const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (chartRef.value && chartRef.value.chart) {
    setTimeout(() => {
      chartRef.value.chart.reflow()
    }, 100)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <highcharts ref="chartRef" :options="chartOptions" />
</template>

<style scoped></style>
