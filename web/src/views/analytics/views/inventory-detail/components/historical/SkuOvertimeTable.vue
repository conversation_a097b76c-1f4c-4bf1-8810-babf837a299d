<template>
  <div ref="tableContainerRef" class="table-hover-effects">
    <ATable
      ref="tableRef"
      :columns="columns"
      :data-source="dataSource"
      :loading="false"
      :scroll="{ x: 'max-content', y: 600 }"
      :pagination="tableConfig.pagination"
      size="middle"
      bordered
      :show-header="true"
      class="sku-overtime-table"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Table as ATable, type TablePaginationConfig } from 'ant-design-vue'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import type { TableColumnType } from 'ant-design-vue'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'
import type { TableConfig } from '@/components/table/types/TableTypes.ts'

type Props = {
  data: PivotTableData
}

const props = defineProps<Props>()

// State
const tableRef = ref()
const tableContainerRef = ref<HTMLDivElement>()
const totalRecords = computed(() => dataSource.value.length)

// Use hover effects
useTableHoverEffects(tableContainerRef)

// Table configuration
const tableConfig = computed<{ pagination: TablePaginationConfig }>(() => {
  return {
    size: 'middle',
    bordered: true,
    striped: true,
    hoverable: true,
    loading: false,
    stickyHeader: true,
    pagination:
      totalRecords.value > 10
        ? {
            current: 1,
            pageSize: 10,
            total: totalRecords.value,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: number, range: [number, number]) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }
        : false,
  } as { pagination: TablePaginationConfig }
})

const dataSource = computed(() => {
  if (!props.data || !props.data.tableData.length) {
    return []
  }

  const { tableData } = props.data
  return tableData.map((item) => ({
    id: item.id,
    metricName: item.metricName,
    ...Object.fromEntries(
      item.storeInfos.map((store) => [store.name.toLowerCase().replace(/\s+/g, '_'), store]),
    ),
    storeInfos: item.storeInfos,
  }))
})

// Table columns
const columns = computed((): TableColumnType[] => {
  if (!props.data || !props.data.tableData.length) {
    return []
  }

  const { tableData, dayAxis } = props.data

  // Lấy tên các cột data (stores hoặc dates)
  const dataColumns = dayAxis || [
    ...new Set(tableData.flatMap((item) => item.storeInfos.map((store) => store.name))),
  ]

  return [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      fixed: 'left',
      width: 50,
      align: 'center',
    },
    {
      title: 'Metric',
      dataIndex: 'metricName',
      key: 'metricName',
      fixed: 'left',
      width: 200,
      ellipsis: true,
    },
    ...dataColumns.map((columnName) => ({
      title: columnName,
      dataIndex: columnName.toLowerCase().replace(/\s+/g, '_'),
      key: columnName.toLowerCase().replace(/\s+/g, '_'),
      width: 130,
      align: 'center' as const,
      customRender: ({ record }: { record: any }) => {
        const storeInfo = record.storeInfos.find((info: any) => info.name === columnName)
        if (!storeInfo) return '-'

        return storeInfo.value
      },
    })),
  ]
})
</script>

<style scoped>
.table-hover-effects {
  background: white;
  border-radius: 8px;
}

.sku-overtime-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

.sku-overtime-table :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}
</style>

<style lang="css" src="/src/assets/table-hover.css"></style>
