<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import type { SelectProps } from 'ant-design-vue'
import { SkuOvertimeBreakdown } from '@/views/analytics/views/inventory-detail/components/historical/types.ts'
import SkuOvertimeChart from '@/views/analytics/views/inventory-detail/components/historical/SkuOvertimeChart.vue'
import { useSkuOvertimeStore } from '@/views/analytics/views/inventory-detail/stores/SkuOvertimeStore.ts'
import {
  DateRange,
  getDateRangeTimestamp,
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'

import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { DataDisplay } from '@/views/analytics/views/inventory-detail/enums/DataDisplay.ts'
import SkuOvertimeTable from '@/views/analytics/views/inventory-detail/components/historical/SkuOvertimeTable.vue'
import SkuOutOfStockTable from '@/views/analytics/views/inventory-detail/components/out-of-stock/SkuOutOfStockTable.vue'
import type {
  PivotTableData
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'

interface Props {
  display: DataDisplay
  dateHistogram: DateHistogram
}

const props = withDefaults(defineProps<Props>(), {
  display: DataDisplay.Chart,
  dateHistogram: DateHistogram.Day,
})

const breakdownOptions = computed<SelectProps['options']>(() => [
  {
    label: 'Shelf life',
    value: SkuOvertimeBreakdown.ShelfLife,
  },
  {
    label: 'Category',
    value: SkuOvertimeBreakdown.Category,
  },
  {
    label: 'Fast Slow Non',
    value: SkuOvertimeBreakdown.FastSlowNon,
  },
])

const breakdownValue = ref<SkuOvertimeBreakdown>(SkuOvertimeBreakdown.ShelfLife)

const store = useSkuOvertimeStore()

const loadData = () => {
  const { startDate, endDate } = getDateRangeTimestamp(DateRange.LAST_30_DAYS)
  store.loadSkuOvertime({
    breakdown: breakdownValue.value,
    startDate,
    endDate,
    dateHistogram: props.dateHistogram,
  })
}

// Watch for breakdown changes
watch(breakdownValue, () => {
  loadData()
})

// Watch for dateHistogram changes
watch(() => props.dateHistogram, () => {
  loadData()
})

onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="sku-overtime-widget" ref="containerRef">
    <div class="sku-overtime-widget__header">
      <h6 class="header-6">Total SKU overtime breakdown by</h6>
      <ASelect
        v-model:value="breakdownValue"
        :show-search="false"
        :options="breakdownOptions"
        :bordered="false"
        style="min-width: 10rem"
      />
    </div>
    <ASpin :spinning="store.loading">
      <AAlert
        v-if="store.hasError"
        :message="store.error"
        type="error"
        show-icon
        closable
        style="margin-bottom: 16px"
      />

      <SkuOvertimeChart
        class="sku-overtime-widget__body"
        v-else-if="store.hasData && display === DataDisplay.Chart"
        :data="store.data as PivotTableData"
        :display="display"
      />

      <SkuOvertimeTable
        :data="store.data as PivotTableData"
        v-else-if="store.hasData && display === DataDisplay.Table"
      />
      <AEmpty v-else description="Không có dữ liệu" />
    </ASpin>
  </div>
</template>

<style scoped>
.sku-overtime-widget {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1rem;
}

.sku-overtime-widget__header {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.sku-overtime-widget__body {
  flex: 1;
  min-height: 0;
}
</style>
