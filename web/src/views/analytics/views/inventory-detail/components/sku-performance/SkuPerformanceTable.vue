<script setup lang="ts">
import { h, ref } from 'vue'
import { BaseTable } from '@/components/table'
import type { BaseTableColumn, TableConfig } from '@/components/table/types/TableTypes'
import InventoryQuantityCell
  from '@/views/analytics/views/inventory-detail/components/sku-performance/InventoryQuantityCell.vue'

const columns: BaseTableColumn[] = [
  {
    title: 'Inventory Quantity',
    dataIndex: 'id',
    key: 'inventory',
    width: 150,
    align: 'center',
    customRender: ({ record }) => {
      return h(InventoryQuantityCell, {
        record: {
          start: record.start,
          end: record.end
        }
      })
    }
  },
  {
    title: 'Days on Hand (DoH)',
    dataIndex: 'doh',
    key: 'doh',
    width: 120,
    align: 'left',
    sortable: true,
    customRender: ({ text }) => {
      return h('span', {
        class: 'doh-cell',
        style: {
          fontWeight: '600',
          alignItems: 'left'
        }
      }, text)
    }
  }
]

const dataSource = [
  {
    id: 1,
    productName: 'iPhone 15 Pro Max',
    start: 500,
    end: 320,
    doh: '12d',
    category: 'electronics',
    status: 'In Stock'
  },
  {
    id: 2,
    productName: 'Samsung Galaxy S24',
    start: 300,
    end: 150,
    doh: '8d',
    category: 'electronics',
    status: 'Low Stock'
  },
  {
    id: 3,
    productName: 'Nike Air Max 270',
    start: 200,
    end: 180,
    doh: '15d',
    category: 'clothing',
    status: 'In Stock'
  },
  {
    id: 4,
    productName: 'MacBook Pro M3',
    start: 100,
    end: 0,
    doh: '0d',
    category: 'electronics',
    status: 'Out of Stock'
  },
  {
    id: 5,
    productName: 'Adidas Ultraboost 22',
    start: 250,
    end: 75,
    doh: '5d',
    category: 'clothing',
    status: 'Low Stock'
  },
  {
    id: 6,
    productName: 'IKEA Desk Lamp',
    start: 400,
    end: 380,
    doh: '25d',
    category: 'home',
    status: 'In Stock'
  },
  {
    id: 7,
    productName: 'Harry Potter Collection',
    start: 150,
    end: 120,
    doh: '18d',
    category: 'books',
    status: 'In Stock'
  },
  {
    id: 8,
    productName: 'Sony WH-1000XM5',
    start: 300,
    end: 45,
    doh: '3d',
    category: 'electronics',
    status: 'Low Stock'
  },
  {
    id: 9,
    productName: 'Levi\'s 501 Jeans',
    start: 180,
    end: 160,
    doh: '20d',
    category: 'clothing',
    status: 'In Stock'
  },
  {
    id: 10,
    productName: 'Kitchen Knife Set',
    start: 120,
    end: 0,
    doh: '0d',
    category: 'home',
    status: 'Out of Stock'
  },
  {
    id: 11,
    productName: 'iPad Pro 12.9',
    start: 200,
    end: 150,
    doh: '10d',
    category: 'electronics',
    status: 'In Stock'
  },
  {
    id: 12,
    productName: 'Programming Book Set',
    start: 80,
    end: 65,
    doh: '14d',
    category: 'books',
    status: 'In Stock'
  },
  {
    id: 13,
    productName: 'Office Chair',
    start: 90,
    end: 20,
    doh: '4d',
    category: 'home',
    status: 'Low Stock'
  },
  {
    id: 14,
    productName: 'Apple Watch Series 9',
    start: 250,
    end: 200,
    doh: '16d',
    category: 'electronics',
    status: 'In Stock'
  },
  {
    id: 15,
    productName: 'Winter Jacket',
    start: 300,
    end: 280,
    doh: '22d',
    category: 'clothing',
    status: 'In Stock'
  },
  {
    id: 16,
    productName: 'Coffee Maker',
    start: 150,
    end: 100,
    doh: '12d',
    category: 'home',
    status: 'In Stock'
  },
  {
    id: 17,
    productName: 'Science Fiction Novels',
    start: 60,
    end: 50,
    doh: '11d',
    category: 'books',
    status: 'In Stock'
  },
  {
    id: 18,
    productName: 'Gaming Mouse',
    start: 200,
    end: 30,
    doh: '2d',
    category: 'electronics',
    status: 'Low Stock'
  },
  {
    id: 19,
    productName: 'Running Shoes',
    start: 180,
    end: 160,
    doh: '19d',
    category: 'clothing',
    status: 'In Stock'
  },
  {
    id: 20,
    productName: 'Table Lamp',
    start: 100,
    end: 95,
    doh: '17d',
    category: 'home',
    status: 'In Stock'
  }
]
const tableRef = ref<HTMLDivElement | null>(null)

const tableConfig = ref({
  bordered: false,
  striped: false,
  pagination: false,
  // size: 'small'
} as TableConfig)


</script>

<template>
  <div class="table-container" ref="tableRef">
    <BaseTable
      :columns="columns"
      :data-source="dataSource"
      :config="tableConfig"
    />
  </div>
</template>

<style scoped>
.table-container {
  :deep(.ant-table-tbody > tr > td) {
    height: 20px !important;
    padding: 0 !important;
  }
  :deep(.ant-table-measure-row){
    display: none;
  }

  :deep(th.ant-table-cell){
    background: transparent;
    font-size: 12px;
    padding: 0;
  }
}
</style>
