<template>
  <div class="inventory-bar-cell">
    <Tooltip :title="tooltipContent" placement="top">
      <div class="progress-bar">
        <div class="progress-background"></div>
        <div
          class="progress-fill"
          :style="{
            width: `${percentage}%`,
          }"
        ></div>
      </div>
    </Tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Tooltip } from 'ant-design-vue'

interface Props {
  record: {
    start: number
    end: number
  }
}

const props = defineProps<Props>()

// Tính toán percentage (end/start)
const percentage = computed(() => {
  if (props.record.start === 0) return 0
  return Math.min((props.record.end / props.record.start) * 100, 100)
})


// Nội dung tooltip
const tooltipContent = computed(() => {
  const percentageText = percentage.value.toFixed(1)
  return `Đ<PERSON>u ngày: ${props.record.start} | Cuối ngày: ${props.record.end} | Còn lại: ${percentageText}%`
})
</script>

<style scoped>
.inventory-bar-cell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  overflow: hidden;
  cursor: pointer;
}

.progress-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  transition:
    width 0.3s ease,
    background-color 0.3s ease;
  background: #1890ff;
}

/* Hover effect */
.progress-bar:hover .progress-fill {
  opacity: 0.8;
}

.progress-bar:hover .progress-background {
  background-color: #e6f7ff;
}
</style>
