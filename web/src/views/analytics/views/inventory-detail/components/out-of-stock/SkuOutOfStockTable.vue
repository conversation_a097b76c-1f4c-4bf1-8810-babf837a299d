<template>
  <div ref="tableContainerRef" class="out-of-stock-table table-hover-effects">
    <ATable
      :columns="columns"
      :data-source="dataSource"
      :loading="false"
      :scroll="{ x: 'max-content', y: 600 }"
      :pagination="false"
      size="small"
      bordered
      class="heatmap-table"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Table as ATable } from 'ant-design-vue'
import type { HeatmapResponse } from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import type { TableColumnType } from 'ant-design-vue'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'

type Props = {
  data: HeatmapResponse
}

const props = defineProps<Props>()

// Refs
const tableContainerRef = ref<HTMLDivElement>()

// Use hover effects
useTableHoverEffects(tableContainerRef)

// Convert heatmap data to table format
const dataSource = computed(() => {
  if (!props.data || !props.data.data.length) {
    return []
  }

  const { yAxis, data } = props.data

  return yAxis.map((hour, hourIndex) => {
    const rowData: any = {
      key: hourIndex,
      hour: hour,
    }

    // Add data for each day
    props.data.xAxis.forEach((day, dayIndex) => {
      const dataPoint = data.find(([dIdx, hIdx]) => dIdx === dayIndex && hIdx === hourIndex)
      rowData[`day_${dayIndex}`] = dataPoint ? dataPoint[2] : 0
    })

    return rowData
  })
})

// Generate columns dynamically
const columns = computed((): TableColumnType[] => {
  if (!props.data || !props.data.xAxis.length) {
    return []
  }

  const { xAxis } = props.data

  return [
    {
      title: 'Time',
      dataIndex: 'hour',
      key: 'hour',
      fixed: 'left',
      width: 80,
      align: 'center',
    },
    ...xAxis.map((day, index) => ({
      title: `Day ${day}`,
      dataIndex: `day_${index}`,
      key: `day_${index}`,
      width: 80,
      align: 'center' as const
    })),
  ]
})
</script>

<style scoped>
.out-of-stock-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

.heatmap-table :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
}

.heatmap-table :deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
  text-align: center;
  font-size: 12px;
}

.heatmap-table :deep(.ant-table-tbody > tr > td:first-child) {
  background-color: #f0f0f0 !important;
  font-weight: 600;
}

/* Responsive table */
.heatmap-table :deep(.ant-table-container) {
  overflow-x: auto;
  max-width: 100%;
}

.heatmap-table :deep(.ant-table-body) {
  overflow-x: auto;
  max-width: 100%;
}
</style>

<style lang="css" src="/src/assets/table-hover.css"></style>
