<script setup lang="ts">
import { onMounted } from 'vue'
import {
  DateRange,
  getDateRangeTimestamp,
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'

import { DateHistogram } from '@/views/analytics/views/inventory-detail/enums/DateHistogram.ts'
import { DataDisplay } from '@/views/analytics/views/inventory-detail/enums/DataDisplay.ts'
import { useSkuOutOfStockStore } from '@/views/analytics/views/inventory-detail/stores/SkuOutOfStockStore.ts'
import SkuOutOfStockChart from '@/views/analytics/views/inventory-detail/components/out-of-stock/SkuOutOfStockChart.vue'
import SkuOutOfStockTable from '@/views/analytics/views/inventory-detail/components/out-of-stock/SkuOutOfStockTable.vue'
import type { HeatmapResponse } from '@/views/analytics/views/inventory-detail/domains/Request.ts'

interface Props {
  display: DataDisplay
  dateHistogram: DateHistogram
}

const props = withDefaults(defineProps<Props>(), {
  display: DataDisplay.Chart,
  dateHistogram: DateHistogram.Day,
})

const store = useSkuOutOfStockStore()

onMounted(() => {
  const { startDate, endDate } = getDateRangeTimestamp(DateRange.LAST_30_DAYS)
  store.loadSkuOutOfStock({
    startDate,
    endDate,
    dateHistogram: props.dateHistogram,
  })
})
</script>

<template>
  <div class="sku-out-of-stock" ref="containerRef">
    <ASpin :spinning="store.loading">
      <AAlert
        v-if="store.hasError"
        :message="store.error"
        type="error"
        show-icon
        closable
        style="margin-bottom: 16px"
      />

      <SkuOutOfStockChart
        class="sku-out-of-stock__body"
        v-else-if="store.hasData && display === DataDisplay.Chart"
        :data="store.data as HeatmapResponse"
        :display="display"
      />

      <SkuOutOfStockTable
        class="sku-out-of-stock__body"
        v-else-if="store.hasData && display === DataDisplay.Table"
        :data="store.data as HeatmapResponse"
      />

      <AEmpty v-else description="Không có dữ liệu" />
    </ASpin>
  </div>
</template>

<style scoped>
.sku-out-of-stock {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1rem;
}

.sku-out-of-stock__body {
  flex: 1;
  min-height: 0;
}
</style>
