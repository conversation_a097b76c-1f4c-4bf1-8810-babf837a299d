<script setup lang="ts">
import PROverview from '@/views/analytics/views/performance-report/components/overview/PROverview.vue'
import PTPerformance from '@/views/analytics/views/performance-report/components/pt-performance/PTPerformance.vue'
import HeatmapOssByHours
  from '@/views/analytics/views/performance-report/components/historical-data/charts/HeatmapOssByHours.vue'
import PRHistorical from '@/views/analytics/views/performance-report/components/historical-data/PRHistorical.vue'
</script>

<template>
  <div id="performance-report-page">
    <PROverview />
    <PRHistorical/>
    <PTPerformance />
  </div>
</template>

<style>
#performance-report-page {
  gap: 1.5rem;
  display: flex;
  flex-direction: column;
  max-width: calc(100vw - 64px);
}

</style>
