<template>
  <div class="table-container">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="paginationConfig"
      :scroll="{ x: 1200 }"
      size="middle"
    >
      <!-- Custom render cho cột Status -->
      <template #status="{ text }">
        <StatusCell
          :status="text"
          :background-color="getBackgroundColorStatus(text)"
          :color="getColorStaus(text)"
        />
      </template>

      <!-- Custom render cho cột Performance -->
      <template #performance="{ text }">
        <StatusCell
          :status="text"
          :background-color="getBackgroundColorStatus(text)"
          :color="getColorStaus(text)"
          :show-icon="false"
        />
      </template>

      <!-- Custom render cho cột Nộ<PERSON> nhận (link) -->
      <template #receiver="{ text }">
        <a href="#" class="receiver-link">{{ text }}</a>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import StatusCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/StatusCell.vue'

export default defineComponent({
  name: 'PRTable',
  components: {
    StatusCell,
  },
  setup() {
    const columns = ref([
      {
        title: '#',
        dataIndex: 'index',
        key: 'index',
        width: 52,
        align: 'center',
      },
      {
        title: 'Mã phiếu PT',
        dataIndex: 'code',
        key: 'code',
        minWidth: 141,
        width: 141,
      },
      {
        title: 'Nơi chuyển',
        dataIndex: 'sender',
        key: 'sender',
        minWidth: 200,
        width: 200,
        ellipsis: true,
      },
      {
        title: 'Nơi nhận',
        dataIndex: 'receiver',
        key: 'receiver',
        minWidth: 300,
        width: 300,
        ellipsis: true,
        slots: { customRender: 'receiver' },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 128,
        align: 'center',
        slots: { customRender: 'status' },
      },
      {
        title: 'Tổng số nhận/Tổng số chuyển',
        dataIndex: 'totalRatio',
        key: 'totalRatio',
        width: 133,
        align: 'right',
      },
      {
        title: 'Tồn kho trước/sau',
        dataIndex: 'stockRatio',
        key: 'stockRatio',
        width: 154,
        align: 'right',
      },
      {
        title: 'Performance',
        dataIndex: 'performance',
        key: 'performance',
        width: 136,
        align: 'center',
        slots: { customRender: 'performance' },
      },
    ])

    const dataSource = ref([
      {
        key: '1',
        index: 1,
        code: 'PT835093',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '2',
        index: 2,
        code: 'PT985232',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đang chuyển',
        totalRatio: '0/30',
        stockRatio: '0/10',
        performance: 'Not good (2/8)',
        performanceType: 'not-good',
      },
      {
        key: '3',
        index: 3,
        code: 'PT325456',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Not good (4/8)',
        performanceType: 'not-good',
      },
      {
        key: '4',
        index: 4,
        code: 'PT345445',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '5',
        index: 5,
        code: 'PT678432',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '6',
        index: 6,
        code: 'PT456456',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '7',
        index: 7,
        code: 'PT789334',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '8',
        index: 8,
        code: 'PT346546',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '9',
        index: 9,
        code: 'PT456456',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
      {
        key: '10',
        index: 10,
        code: 'PT456768',
        sender: 'KFM_HCM_TDU - TMDV Citihome...',
        receiver: 'KFM_HCM_TDU - TMDV Citihome Cát L...',
        status: 'Đã nhận',
        totalRatio: '30/30',
        stockRatio: '10/40',
        performance: 'Good (7/8)',
        performanceType: 'good',
      },
    ])

    const paginationConfig = ref({
      current: 1,
      pageSize: 50,
      total: 100,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) => `Trang ${range[0]} / ${total}`,
      pageSizeOptions: ['10', '20', '50', '100'],
    })

    return {
      columns,
      dataSource,
      paginationConfig,
    }
  },
  methods: {
    getBackgroundColorStatus(text: string) {
      if (text.includes('Good')) {
        return '#EDFCF2'
      }

      if (text.includes('Not good')) {
        return '#FFF1F3'
      }
      if (text.includes('Đã nhận')) {
        return '#EDFCF2'
      }

      if (text.includes('Đang chuyển')) {
        return '#EFF4FF'
      }
      return 'white'
    },

    getColorStaus(text: string) {
      if (text.includes('Good')) {
        return '#087443'
      }

      if (text.includes('Not good')) {
        return '#C01048'
      }
      if (text.includes('Đã nhận')) {
        return '#087443'
      }

      if (text.includes('Đang chuyển')) {
        return '#004EEB'
      }
      return 'black'
    },
  },
})
</script>

<style scoped>
.table-container :deep(.ant-table-container) {
  border: 1px solid #eaecf0;
}

.receiver-link {
  color: #1890ff;
  text-decoration: none;
}

.receiver-link:hover {
  text-decoration: underline;
}

.performance-good {
  color: #52c41a;
  font-weight: 500;
}

.performance-not-good {
  color: #ff4d4f;
  font-weight: 500;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-tag) {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
</style>
