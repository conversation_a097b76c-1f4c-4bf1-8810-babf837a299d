<script setup lang="ts">
import { Input } from 'ant-design-vue'
import { FileExcelFilled, FilterOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { h, ref } from 'vue'

const searchText = ref('')
const handleSearch = () => {
  //Do somethine
}
</script>

<template>
  <div class="pt-performance-header">
    <ATypographyTitle :level="5">PT Performance</ATypographyTitle>
    <div class="pt-performance-header--header-actions">
      <div class="pt-performance-header--header-actions--search-action">
        <Input
          v-model:value="searchText"
          placeholder="Search products..."
          style="width: 413px"
          @change="handleSearch"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </Input>

        <AButton type="text" :icon="h(FilterOutlined)" />
      </div>

      <AButton type="default" :icon="h(FileExcelFilled)">
        Xuất Excel
      </AButton>
    </div>
  </div>
</template>

<style scoped>
.pt-performance-header{
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pt-performance-header--header-actions{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.pt-performance-header--header-actions--search-action{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

</style>
