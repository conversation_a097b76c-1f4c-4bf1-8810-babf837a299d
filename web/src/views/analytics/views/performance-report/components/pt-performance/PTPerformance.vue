<script setup lang="ts">
import PtTable from '@/views/analytics/views/performance-report/components/pt-performance/PTTable.vue'
import PTPerformanceHeader from '@/views/analytics/views/performance-report/components/pt-performance/PTPerformanceHeader.vue'
</script>

<template>
  <div id="pt-performance">
    <PTPerformanceHeader />
    <PtTable />
  </div>
</template>

<style scoped>
#pt-performance {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
