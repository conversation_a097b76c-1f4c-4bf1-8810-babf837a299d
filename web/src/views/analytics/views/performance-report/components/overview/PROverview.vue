
<script setup lang="ts">
import MonitorMetricSegmentHeader from '@/views/analytics/views/replenishment/segments/monitor-metric/components/MonitorMetricSegmentHeader.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import PROverviewBody
  from '@/views/analytics/views/performance-report/components/overview/components/PROverviewBody.vue'
import PROverviewHeader
  from '@/views/analytics/views/performance-report/components/overview/components/PROverviewHeader.vue'

interface Props {
  dateRange?: DateRange
}

interface Emits {
  (e: 'update:dateRange', value: DateRange): void
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: DateRange.LAST_7_DAYS,
})

const emit = defineEmits<Emits>()

// Handler để emit value khi thay đổi
const handleDateRangeChange = (value: DateRange) => {
  emit('update:dateRange', value)
}
</script>

<template>
  <AFlex vertical gap="middle">
    <PROverviewHeader
      :date-range="props.dateRange"
      @update:date-range="handleDateRangeChange"
    />
    <PROverviewBody />
  </AFlex>
</template>
