<script setup lang="ts">
import { ArrowDownOutlined, InfoCircleOutlined, ShopOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'
import MetricCard from '@/views/analytics/components/MetricCard.vue'

const StoreOOSRate = 10
const StoreOOSHours = 10
const NumberPT = 20
const NumberSKUs = 20
</script>

<template>
  <div class="kpi-container">
    <MetricCard cardColor="#F79009">
      <div class="card-content">
        <ATypography class="card-title">
          <ShopOutlined />
          <span style="margin-left: 6px">Store OOS Rate</span>
        </ATypography>
        <AFlex flexDirection="row" align="center" justify="space-between" style="padding: 0 18px">
          <div>
            <span class="card-metric-text" :style="{ color: `#F79009` }">{{ StoreOOSRate }}</span>
            <span class="card-content-text">%</span>
          </div>

          <ATypography :style="{ fontSize: `14px`, color: 'red', fontWeight: 500 }"
            >-2.1%
            <ArrowDownOutlined />
          </ATypography>
        </AFlex>
      </div>
    </MetricCard>

    <MetricCard cardColor="#0BA5EC">
      <div class="card-content">
        <ATypography class="card-title">
          <ShopOutlined />
          <span style="margin-left: 6px">Store OOS Hours</span>
        </ATypography>
        <AFlex flexDirection="row" align="center" justify="space-between" style="padding: 0 18px">
          <div>
            <span class="card-metric-text" :style="{ color: `#0BA5EC` }">{{ StoreOOSHours }}</span>
            <span class="card-content-text"> h</span>
          </div>
        </AFlex>
      </div>
    </MetricCard>

    <MetricCard cardColor="#2970FF">
      <div class="card-content">
        <ATypography class="card-title">
          <ShopOutlined />
          <span style="margin-left: 6px">Number of PT</span>
        </ATypography>
        <AFlex flexDirection="row" align="center" justify="space-between" style="padding: 0 18px">
          <div>
            <span class="card-metric-text" :style="{ color: `#2970FF` }">{{ NumberPT }}</span>
            <span class="card-content-text"> PT</span>
          </div>
        </AFlex>
      </div>
    </MetricCard>

    <MetricCard cardColor="#15B79E">
      <div class="card-content">
        <ATypography class="card-title">
          <ShopOutlined />
          <span style="margin-left: 6px">Number of SKUs</span>
        </ATypography>
        <AFlex flexDirection="row" align="center" justify="space-between" style="padding: 0 18px">
          <div>
            <span class="card-metric-text" :style="{ color: `#15B79E` }">{{ NumberSKUs }}</span>
            <span class="card-content-text"> SKUs</span>
          </div>
        </AFlex>
      </div>
    </MetricCard>
  </div>
</template>

<style scoped>
.kpi-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  align-items: center;
}

.kpi-container :deep(.ant-card-body) {
  width: unset;
}

@media (max-width: 768px) {
  .kpi-container {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .kpi-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #667085;
}

.card-metric-text {
  font-size: 20px;
  font-weight: 700;
}

.card-content-text {
  font-size: 20px;
  font-weight: 700;
  color: #667085;
}
</style>
