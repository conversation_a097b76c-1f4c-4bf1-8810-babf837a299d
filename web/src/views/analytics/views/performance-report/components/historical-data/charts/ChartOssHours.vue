<script setup lang="ts">
import { Chart } from 'highcharts-vue'

const chartOptions = {
  chart: {
    type: 'column',
    backgroundColor: '#ffffff',
    height: 400,
    spacingTop: 20,
    spacingBottom: 20,
    spacingLeft: 20,
    spacingRight: 20,
  },

  title: {
    text: '',
    style: {
      display: 'none',
    },
  },

  xAxis: {
    categories: [
      '1/3',
      '2/3',
      '3/3',
      '4/3',
      '5/3',
      '6/3',
      '7/3',
      '8/3',
      '9/3',
      '10/3',
      '11/3',
      '12/3',
      '13/3',
      '14/3',
      '14/3',
      '15/3',
      '16/3',
      '17/3',
      '18/3',
      '19/3',
      '20/3',
      '21/3',
      '22/3',
      '23/3',
      '24/3',
      '25/3',
      '26/3',
      '27/3',
      '28/3',
    ],
    labels: {
      style: {
        fontSize: '11px',
        color: '#666',
      },
      rotation: 0,
    },
    lineWidth: 1,
    lineColor: '#e6e6e6',
    tickWidth: 0,
  },

  yAxis: [
    {
      title: {
        text: 'Inventory quantity',
        style: {
          color: '#666',
          fontSize: '12px',
        },
      },
      labels: {
        style: {
          color: '#666',
          fontSize: '11px',
        },
      },
      gridLineWidth: 1,
      gridLineColor: '#f0f0f0',
      min: 0,
      max: 600,
    },
    {
      title: {
        text: 'Revenue',
        style: {
          color: '#666',
          fontSize: '12px',
        },
      },
      labels: {
        style: {
          color: '#666',
          fontSize: '11px',
        },
      },
      opposite: true,
      min: 0,
      max: 200,
    },
  ],

  legend: {
    align: 'left',
    verticalAlign: 'bottom',
    layout: 'horizontal',
    itemStyle: {
      fontSize: '12px',
      color: '#333',
    },
    symbolWidth: 20,
    symbolHeight: 2,
    symbolRadius: 0,
    y: -10,
  },

  plotOptions: {
    column: {
      borderWidth: 0,
      groupPadding: 0.1,
      pointPadding: 0.05,
      borderRadius: 2,
    },
    line: {
      lineWidth: 2,
      marker: {
        enabled: false,
        radius: 3,
      },
    },
  },

  series: [
    {
      name: 'Inventory quantity',
      type: 'column',
      yAxis: 0,
      data: [
        { y: 410, color: '#4472C4' },
        { y: 480, color: '#4472C4' },
        { y: 380, color: '#4472C4' },
        { y: 420, color: '#4472C4' },
        { y: 460, color: '#4472C4' },
        { y: 520, color: '#E2B93B' },
        { y: 510, color: '#E2B93B' },
        { y: 460, color: '#4472C4' },
        { y: 420, color: '#4472C4' },
        { y: 480, color: '#4472C4' },
        { y: 460, color: '#4472C4' },
        { y: 470, color: '#4472C4' },
        { y: 520, color: '#E2B93B' },
        { y: 490, color: '#E2B93B' },
        { y: 470, color: '#4472C4' },
        { y: 400, color: '#4472C4' },
        { y: 440, color: '#4472C4' },
        { y: 470, color: '#4472C4' },
        { y: 420, color: '#4472C4' },
        { y: 500, color: '#E2B93B' },
        { y: 530, color: '#E2B93B' },
        { y: 300, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 320, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 280, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 310, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 290, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 330, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 350, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
        { y: 340, color: '#ffffff', borderWidth: 1, borderColor: '#155EEF' },
      ],
    },
    {
      name: 'Revenue',
      type: 'line',
      yAxis: 1,
      color: '#70AD47',
      data: [
        130, 140, 135, 150, 145, 140, 135, 130, 120, 100, 90, 85, 80, 75, 70, 65, 60, 80, 120, 110,
        90, 80, 100, 110, 95, 85, 90, 95, 120,
      ],
      lineWidth: 2,
      dashStyle: 'Solid',
    },
    {
      name: 'OOS',
      type: 'line',
      yAxis: 1,
      color: '#C65911',
      data: [
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        70,
        130,
        110,
        90,
        80,
        100,
        110,
        120,
        115,
        130,
        125,
        135,
      ],
      lineWidth: 2,
      dashStyle: 'Dash',
    },
  ],

  tooltip: {
    shared: true,
    useHTML: true,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#ccc',
    borderRadius: 8,
    shadow: true,
  },

  credits: {
    enabled: false,
  },
}
</script>

<template>
  <Chart :options="chartOptions as any"></Chart>
</template>

<style scoped></style>
