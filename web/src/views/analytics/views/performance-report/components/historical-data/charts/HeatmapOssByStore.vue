<script setup lang="ts">
import type { HeatmapResponse } from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import { type TooltipFormatterContextObject } from 'highcharts'
import Heatmap from '@/views/analytics/components/Heatmap.vue'

function customHeatmapTooltip(this: TooltipFormatterContextObject): string {
  const date = this.series.xAxis.categories?.[this.point.x] || ''
  const storeName = this.series.yAxis.categories?.[this.point.y ?? -1] || 'Cửa hàng'

  const colorDot = `<span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${this.point.color};margin-right:6px;"></span>`
  const seriesName = this.series.name || 'Giá trị'

  return `
    <div style="padding: 4px 8px;">
      <div style="margin-bottom: 4px; font-weight: 500; color: #101828;">
        ${colorDot}${seriesName}: ${this.point.value}
      </div>
      <div style="color: #667085; font-size: 13px;">
        ${date} - ${storeName}
      </div>
    </div>
  `
}

const createMockHeatmapData = (): HeatmapResponse => {
  // xAxis: ngày từ 1 đến 30 trong tháng
  const xAxis = Array.from({ length: 30 }, (_, i) => (i + 1).toString())

  // yAxis: tên các cửa hàng
  const yAxis = [
    'Cửa hàng Hà Nội',
    'Cửa hàng HCM',
    'Cửa hàng Đà Nẵng',
    'Cửa hàng Hải Phòng',
    'Cửa hàng Cần Thơ',
    'Cửa hàng Nha Trang',
    'Cửa hàng Vũng Tàu',
    'Cửa hàng Huế',
    'Cửa hàng Quy Nhon',
    'Cửa hàng Vinh'
  ]

  // Tạo data dưới dạng [dayIndex, storeIndex, value]
  const data: number[][] = []

  // Duyệt qua từng ngày (0-29)
  for (let dayIndex = 0; dayIndex < 30; dayIndex++) {
    // Duyệt qua từng cửa hàng (0-9)
    for (let storeIndex = 0; storeIndex < yAxis.length; storeIndex++) {
      // Tạo random value từ 0 đến 100 (có thể điều chỉnh range tùy ý)
      const randomValue = Math.floor(Math.random() * 101)

      // Thêm vào data array dưới dạng [dayIndex, storeIndex, value]
      data.push([dayIndex, storeIndex, randomValue])
    }
  }

  return {
    xAxis,
    yAxis,
    data,
  }
}

const heatmapResponse: HeatmapResponse = createMockHeatmapData()

</script>

<template>
  <Heatmap :data="heatmapResponse" :tooltip-formatter="customHeatmapTooltip" />
</template>

<style scoped></style>
