<script setup lang="ts">
import type { HeatmapResponse } from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import { type TooltipFormatterContextObject } from 'highcharts'
import Heatmap from '@/views/analytics/components/Heatmap.vue'

function customHeatmapTooltip(this: TooltipFormatterContextObject): string {
  const date = this.series.xAxis.categories?.[this.point.x] || ''
  const timeStart = this.series.yAxis.categories?.[this.point.y ?? -1] || '00:00'
  const hour = parseInt(timeStart.split(':')[0], 10)
  const timeEnd = `${(hour + 1).toString().padStart(2, '0')}:00`

  const colorDot = `<span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${this.point.color};margin-right:6px;"></span>`
  const seriesName = this.series.name || 'Giá trị'

  return `
    <div style="padding: 4px 8px;">
      <div style="margin-bottom: 4px; font-weight: 500; color: #101828;">
        ${colorDot}${seriesName}: ${this.point.value}
      </div>
      <div style="color: #667085; font-size: 13px;">
        ${date} ${timeStart} - ${timeEnd}
      </div>
    </div>
  `
}

const createMockHeatmapData = (): HeatmapResponse => {
  // xAxis: ngày từ 1 đến 30 trong tháng
  const xAxis = Array.from({ length: 30 }, (_, i) => (i + 1).toString())

  // yAxis: giờ từ 6 sáng đến 10 tối (6:00 - 22:00)
  const yAxis = Array.from({ length: 17 }, (_, i) => {
    const hour = i + 6
    return `${hour.toString().padStart(2, '0')}:00`
  })

  // Tạo data dưới dạng [dayIndex, hourIndex, value]
  const data: number[][] = []

  // Duyệt qua từng ngày (0-29)
  for (let dayIndex = 0; dayIndex < 30; dayIndex++) {
    // Duyệt qua từng giờ (0-16, tương ứng với 6h-22h)
    for (let hourIndex = 0; hourIndex < 17; hourIndex++) {
      // Tạo random value từ 0 đến 100 (có thể điều chỉnh range tùy ý)
      const randomValue = Math.floor(Math.random() * 101)

      // Thêm vào data array dưới dạng [dayIndex, hourIndex, value]
      data.push([dayIndex, hourIndex, randomValue])
    }
  }

  return {
    xAxis,
    yAxis,
    data,
  }
}

const heatmapResponse: HeatmapResponse = createMockHeatmapData()

</script>

<template>
  <Heatmap :data="heatmapResponse" :tooltip-formatter="customHeatmapTooltip" />
</template>

<style scoped></style>
