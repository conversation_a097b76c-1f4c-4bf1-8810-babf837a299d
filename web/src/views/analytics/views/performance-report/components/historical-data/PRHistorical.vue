<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { defineAsyncComponent } from 'vue'

const HeatmapByHours = defineAsyncComponent(() => import('./charts/HeatmapOssByHours.vue'))
const HeatmapByStore = defineAsyncComponent(() => import('./charts/HeatmapOssByStore.vue'))
const ChartOssRate = defineAsyncComponent(() => import('./charts/ChartOssRate.vue'))
const ChartOssHours = defineAsyncComponent(() => import('./charts/ChartOssHours.vue'))

const historicalDataViewOptions: SelectProps['options'] = [
  // {
  //   label: 'Sales volumes',
  //   value: 'sales_volumes',
  // },
  //
  {
    label: 'Store OSS Rate',
    value: 'store_oss_rate',
  },

  {
    label: 'Store OSS Hours',
    value: 'store_oss_hours',
  },
  // {
  //   label: 'Số lượng PT, SKUs',
  //   value: 'pt_and_skus',
  // },

  {
    label: 'Heat map OOS (Theo giờ)',
    value: 'heatmap_by_hours',
  },

  {
    label: 'Heat map OOS (Theo siêu thị)',
    value: 'heatmap_by_store',
  },
]

const historicalDataValue = ref('heatmap_by_hours')

const toComponent = computed(() => {
  switch (historicalDataValue.value) {
    case 'heatmap_by_hours':
      return HeatmapByHours
    case 'heatmap_by_store':
      return HeatmapByStore
    case 'store_oss_rate':
      return ChartOssRate
    case 'store_oss_hours':
      return ChartOssHours
    default:
      return void 0
  }
})
</script>

<template>
  <div class="pr-historical-data">
    <div class="pr-historical-data__header">
      <ATypographyTitle :level="5">Historical data</ATypographyTitle>

      <ASelect
        v-model:value="historicalDataValue"
        :show-search="false"
        :options="historicalDataViewOptions"
        :bordered="false"
        style="min-width: 10rem"
      />
    </div>

    <component v-if="toComponent" :is="toComponent" />
  </div>
</template>

<style scoped>
.pr-historical-data {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pr-historical-data__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
</style>
