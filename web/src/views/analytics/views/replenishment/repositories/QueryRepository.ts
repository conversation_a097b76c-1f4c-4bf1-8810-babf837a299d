
import type {
  GetCategoriesRequest,
  GetDailyInventorySoldRequest,
  GetDayOnHandRequest,
  GetSalesRequest,
  GetSkuListingRequest,
} from '@/views/analytics/views/replenishment/domains/requests/GetSalesRequest.ts'
import { type HttpClient } from '@/client/HttpClient.ts'
import type {
  CategoryListingResponse,
  DailyInventorySoldRow,
  DayOnHandRow,
  GetDailyInventorySoldResponse,
  GetDayOnHandResponse,
  GetSalesResponse, GetStoresResponse, SaleRow,
  SkuListingResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import client from '@/client/HttpClientBuilder.ts'

export abstract class QueryRepository {
  abstract getSales(request: GetSalesRequest): Promise<GetSalesResponse>

  abstract getDayOnHand(request: GetDayOnHandRequest): Promise<GetDayOnHandResponse>

  abstract getSkuListing(request: GetSkuListingRequest): Promise<SkuListingResponse>

  abstract getDailyInventorySold(
    request: GetDailyInventorySoldRequest,
  ): Promise<GetDailyInventorySoldResponse>

  abstract getCategoriesRequest(request: GetCategoriesRequest): Promise<CategoryListingResponse>

  abstract getStores(): Promise<GetStoresResponse>
}

export class QueryRepositoryImpl extends QueryRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getSales(request: GetSalesRequest): Promise<GetSalesResponse> {
    // return new MockDataGenerator().getSales(request)
    return this.client
      .post<GetSalesResponse>('/api/query', {
        ...request,
        name: 'get_sales_volume',
      })
      .then((response) => response.data)
  }

  async getDayOnHand(request: GetDayOnHandRequest): Promise<GetDayOnHandResponse> {
    // return new MockDataGenerator().getDayOnHand(request)
    return this.client
      .post<GetDayOnHandResponse>('/api/query', {
        ...request,
        name: 'get_days_on_hand'
      })
      .then((response) => response.data)
  }

  async getSkuListing(request: GetSkuListingRequest): Promise<SkuListingResponse> {
    const res = await this.client.post<SkuListingResponse>('/api/query', {
      ...request,
      name: 'get_sku_inventory',
    })
    return res.data
  }

  async getDailyInventorySold(
    request: GetDailyInventorySoldRequest,
  ): Promise<GetDailyInventorySoldResponse> {
    // return new MockDataGenerator().getDailyInventorySold(request)
    const res = await this.client
      .post<GetDailyInventorySoldResponse>('/api/query', {
        ...request,
        name: 'get_inventory_sold'
      })
    return res.data
  }

  async getCategoriesRequest(request: GetCategoriesRequest): Promise<CategoryListingResponse> {
    // return new MockDataGenerator().getCategories(request)
    return this.client
      .post<CategoryListingResponse>('/api/query', {
        ...request,
        name: 'get_categories',
      })
      .then((res) => res.data)
  }

  getStores(): Promise<GetStoresResponse> {
    return this.client
      .post<GetStoresResponse>('/api/query', {
        name: 'get_stores',
      })
      .then((res) => res.data)
  }
}

export default new QueryRepositoryImpl(client)
