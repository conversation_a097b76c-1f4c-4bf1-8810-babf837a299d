import type { PredictSaleVolumeRequest } from '@/views/analytics/views/replenishment/domains'
import type {
  PredictSaleVolumeResponse,
  PredictSaleVolumeRow,
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import type { HttpClient } from '@/client/HttpClient.ts'
import client from '@/client/HttpClientBuilder.ts'

export abstract class PredictRepository {
  abstract predictSaleVolumes(request: PredictSaleVolumeRequest): Promise<PredictSaleVolumeResponse>
}

export class PredictRepositoryImpl extends PredictRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async predictSaleVolumes(request: PredictSaleVolumeRequest): Promise<PredictSaleVolumeResponse> {
    return this.client
      .get<PredictSaleVolumeResponse>('/api/predict/sold', {
        params: {
          start_date: request.startDate,
          end_date: request.endDate,
          store: request.store,
          sku: request.sku,
        }
      })
      .then((res) => res.data)
  }
}

const mockData: PredictSaleVolumeResponse = {
  rows: [
    {
      date: '2025-07-04',
      predictedQuantitySold: 1.172305322297301,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-05',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-06',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-07',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-08',
      predictedQuantitySold: 1.172305322297301,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-09',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-10',
      predictedQuantitySold: 1.133177132626557,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-11',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-12',
      predictedQuantitySold: 1.151269545117285,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-13',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-14',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-15',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-16',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
    {
      date: '2025-07-17',
      predictedQuantitySold: 0,
      store: '669f541d2abb6e0007f7955b',
      sku: '014100079521',
    },
  ],
}

export default new PredictRepositoryImpl(client)
