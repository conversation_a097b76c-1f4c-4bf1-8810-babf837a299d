
import type { CategoryListingResponse, CategoryRow } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import type { TreeSelectNode } from '@/stores/sidebarStore.ts'

export class CategoryListingResponseAdapter {
  static adapt(response: CategoryListingResponse): TreeSelectNode[] {
    return response.rows.map((category: CategoryRow) => {
      const node: TreeSelectNode = {
        value: category.id,
        title: `${category.name} (${category.totalChildren})`,
        selectable: true,
        disabled: false,
        loaded: false
      }

      // Nếu có children, tạo mảng children với placeholder để hiển thị expand arrow
      if (category.totalChildren > 0) {
        // Tạo một placeholder node để AntD Tree hiển thị expand arrow
        node.children = [{
          value: `${category.id}_placeholder`,
          title: 'Loading...',
          selectable: false,
          disabled: true
        }]
      } else {
        // Không có children, không set children property
        node.children = undefined
      }

      return node
    })
  }
}
