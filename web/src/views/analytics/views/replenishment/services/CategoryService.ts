import type { GetCategoriesRequest } from '@/views/analytics/views/replenishment/domains'
import type { TreeSelectNode } from '@/stores/sidebarStore.ts'
import repository, {
  type QueryRepository,
} from '@/views/analytics/views/replenishment/repositories/QueryRepository.ts'
import { CategoryListingResponseAdapter } from './adapters/CategoryListingResponseAdapter.ts'

export abstract class CategoryService {
  abstract getCategoryListing(request: GetCategoriesRequest): Promise<TreeSelectNode[]>
}

export class CategoryServiceImpl extends CategoryService {
  private readonly repository: QueryRepository

  constructor(repository: QueryRepository) {
    super()
    this.repository = repository
  }

  async getCategoryListing(request: GetCategoriesRequest): Promise<TreeSelectNode[]> {
    const response = await this.repository.getCategoriesRequest(request)
    return CategoryListingResponseAdapter.adapt(response)
  }
}

export default new CategoryServiceImpl(repository)
