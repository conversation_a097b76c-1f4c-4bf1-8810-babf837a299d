import type { QueryResponse } from '@/views/analytics/views/replenishment/domains/responses/QueryResponse.ts'

export type SaleRow = {
  totalQuantitySold: number
  totalOpeningInventory: number
  voucherProgram: string
  weatherCondition: string
  saleDate: string
  store: string
  isWeekend: number
}

export type DayOnHandRow = {
  currentInventory: number
  store: string
  daysOnHand: number
}

export type SkuRow = {
  sku: string
  total_latest_inventory: number
}

export type ForecastCategory = {
  date: string
  isWeekend: number
  weatherCondition: string
  forecast: boolean
}

export type SeriesDataItem = {
  quantity: number
  skuPerDay: number
}

export type ForecastData = {
  categories: ForecastCategory[]
  saleVolumes: number[]
  saleVolumesPredict: number[]
  voucherPrograms: string[]
  seriesData: SeriesDataItem[]
}

export type DailyInventorySoldRow = {
  date: string
  sold: number
  inventory: number
  doh?: number
  forecastSold?: number
  request?: number
}

export type CategoryRow = {
  level: number
  name: string
  id: string
  totalChildren: number
  parentId?: string
}

export type PredictSaleVolumeRow = {
  date: string,
  store: string,
  sku: string,
  predictedQuantitySold: number,
}

export type StoreRow = {
  id: string,
  name: string,
  storeType: number
}

export type GetSalesResponse = QueryResponse<SaleRow>

export type GetDayOnHandResponse = QueryResponse<DayOnHandRow>

export type SkuListingResponse = QueryResponse<SkuRow>

export type CategoryListingResponse = QueryResponse<CategoryRow>

export type GetDailyInventorySoldResponse = QueryResponse<DailyInventorySoldRow>

export type PredictSaleVolumeResponse = QueryResponse<PredictSaleVolumeRow>

export type GetStoresResponse = QueryResponse<StoreRow>
