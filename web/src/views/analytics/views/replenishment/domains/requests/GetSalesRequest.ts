export type TimeStampAsSeconds = number

export type GetSalesRequest = {
  storeId: string
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  sku: string
}

export type GetDayOnHandRequest = {
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  sku: string
}

export type GetSkuListingRequest = {
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
}

export type GetDailyInventorySoldRequest = {
  store: string
  sku: string
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
}

export type GetCategoriesRequest = {
  parentId?: string
}

export type PredictSaleVolumeRequest ={
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds,
  sku: string,
  store: string,
}
