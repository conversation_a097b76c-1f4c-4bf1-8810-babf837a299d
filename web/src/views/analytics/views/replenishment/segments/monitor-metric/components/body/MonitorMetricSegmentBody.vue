
<script setup lang="ts">

import KPICard from '@/views/analytics/views/replenishment/segments/monitor-metric/components/body/KPICard.vue'
import { ScheduleOutlined } from '@ant-design/icons-vue'
</script>

<template>
  <div class="kpi-container">
    <KPICard title="Tổng số giờ hết hàng" value="0" color="orange">
      <ScheduleOutlined :style="{ fontSize: '14px' }" />
    </KPICard>
    <KPICard title="Tổng tiền lost sales" value="0" color="blue">
      <ScheduleOutlined :style="{ fontSize: '14px' }"/>
    </KPICard>
    <KPICard title="Forecast performance" value="0" color="cyan">
      <ScheduleOutlined :style="{ fontSize: '14px' }"/>
    </KPICard>
  </div>
</template>

<style scoped>
.kpi-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: center;
}

@media (max-width: 768px) {
  .kpi-container {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .kpi-container {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
