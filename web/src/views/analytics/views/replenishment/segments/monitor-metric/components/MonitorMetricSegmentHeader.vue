<script setup lang="ts">
import { computed } from 'vue'
import { DateRange, DateRangeLabels } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator'

// <PERSON><PERSON><PERSON> nghĩa props và emits cho v-model
interface Props {
  dateRange?: DateRange
}

interface Emits {
  (e: 'update:dateRange', value: DateRange): void
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: DateRange.LAST_7_DAYS
})

const emit = defineEmits<Emits>()

// Computed cho v-model
const dateRangeValue = computed({
  get: () => props.dateRange,
  set: (value: DateRange) => emit('update:dateRange', value)
})
</script>

<template>
  <div class="header-container">
    <ATypographyTitle :level="5">Monitor metric</ATypographyTitle>
    <ARadioGroup v-model:value="dateRangeValue">
      <ARadioButton :value="DateRange.LAST_7_DAYS">
        {{ DateRangeLabels[DateRange.LAST_7_DAYS] }}
      </ARadioButton>
      <ARadioButton :value="DateRange.LAST_30_DAYS">
        {{ DateRangeLabels[DateRange.LAST_30_DAYS] }}
      </ARadioButton>
      <ARadioButton :value="DateRange.MONTH_TO_DATE">
        {{ DateRangeLabels[DateRange.MONTH_TO_DATE] }}
      </ARadioButton>
    </ARadioGroup>
  </div>
</template>


<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
