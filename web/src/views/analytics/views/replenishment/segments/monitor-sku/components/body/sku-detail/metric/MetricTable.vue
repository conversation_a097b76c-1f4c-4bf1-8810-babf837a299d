<script setup lang="ts">
import { computed, inject } from 'vue'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import type { useSkuMetricStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import { usePivotTable } from '@/hooks/usePivotTable.ts'

interface Props {
  metricData?: PivotTableData
  productId: string
  storeId: string
}

const props = defineProps<Props>()

// Inject store từ parent component
const skuMetricStore = inject<ReturnType<typeof useSkuMetricStore>>('skuMetricStore')

if (!skuMetricStore) {
  throw new Error('skuMetricStore not provided')
}

// Computed data từ store hoặc props
const loading = computed(() =>
  skuMetricStore.isLoadingFor(props.productId, props.storeId, DateRange.LAST_7_DAYS),
)
const errorMessage = computed(() =>
  skuMetricStore.getErrorFor(props.productId, props.storeId, DateRange.LAST_7_DAYS),
)
const metricDetail = computed((): PivotTableData | undefined => {
  return (
    props.metricData ||
    skuMetricStore.getCachedData(props.productId, props.storeId, DateRange.LAST_7_DAYS) ||
    emptyMetricDetail.value
  )
})

const emptyMetricDetail = computed(() => {
  return {
    dayAxis: [],
    tableData: [
      {
        id: '1',
        metricName: 'Inventory quantity',
        storeInfos: [],
      },
      {
        id: '2',
        metricName: 'DOH',
        storeInfos: [],
      },
      {
        id: '3',
        metricName: 'Sức bán thực tế',
        storeInfos: [],
      },
      {
        id: '4',
        metricName: 'Sức bán dự đoán',
        storeInfos: [],
      },
      {
        id: '5',
        metricName: 'Request',
        storeInfos: [],
      },
    ],
  }
})

// Sử dụng hook pivot table với Metric specific options
const { tableRef, availableWidth, columns, dataSource, scrollConfig } = usePivotTable(
  metricDetail,
  {
    statusClassPrefix: 'metric-status',
    metricColumnWidth: 180,
    dataColumnWidth: 120,
  },
)
</script>

<template>
  <div
    ref="tableRef"
    class="pivot-table-container table-hover-effects"
    :style="{ maxWidth: `${availableWidth}px` }"
  >
    <ASpin :spinning="loading">
      <AAlert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        style="margin-bottom: 16px"
      />
      <ATable
        :columns="columns"
        :data-source="dataSource"
        :scroll="scrollConfig"
        :pagination="false"
        row-key="id"
        class="pivot-table"
      />
    </ASpin>
  </div>
</template>

<style scoped src="/src/assets/pivot-tables.css"></style>
<style lang="css" src="/src/assets/table-hover.css"></style>
