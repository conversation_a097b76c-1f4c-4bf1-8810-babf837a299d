import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { ForecastData } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse'
import type { GetSalesRequest } from '@/views/analytics/views/replenishment/domains'
import { DateRange, getDateRangeTimestamp } from './DateRangeCalculator'
import skuDetailService from '@/views/analytics/views/replenishment/segments/monitor-sku/core/services/SkuDetailService'

export const useSkuForecastStore = defineStore('skuForecast', () => {
  // State - quản lý loading và error cho từng request
  const loadingStates = ref<Record<string, boolean>>({})
  const errorStates = ref<Record<string, string | null>>({})
  const predictingStates = ref<Record<string, boolean>>({})

  // Cache - key format: "skuName-storeId-dateRange"
  const cache = ref<Record<string, ForecastData>>({})

  // Computed
  const isLoading = computed(() => {
    return Object.values(loadingStates.value).some((loading) => loading)
  })

  const hasAnyError = computed(() => {
    return Object.values(errorStates.value).some((error) => error !== null)
  })

  // Helper function to generate the cache key
  const generateCacheKey = (skuName: string, storeId: string, dateRange: DateRange): string => {
    return `${skuName}-${storeId}-${dateRange}`
  }

  // Actions
  const fetchSaleVolumeData = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS,
  ): Promise<ForecastData | null> => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)

    // Check cache first
    if (cache.value[cacheKey]) {
      return cache.value[cacheKey]
    }

    // Set the loading state for this specific request
    loadingStates.value[cacheKey] = true
    errorStates.value[cacheKey] = null

    try {
      const { startDate, endDate } = getDateRangeTimestamp(dateRange)
      const request: GetSalesRequest = {
        sku: skuName,
        storeId: storeId,
        startDate,
        endDate,
      }

      const data = await skuDetailService.getForecastDetail(request)

      // Cache the result
      cache.value[cacheKey] = data

      // Sau khi fetch data thành công, gọi predict API
      // fetchPredictData(skuName, storeId, dateRange)

      return data
    } catch (err) {
      errorStates.value[cacheKey] =
        err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải dữ liệu forecast'
      return null
    } finally {
      loadingStates.value[cacheKey] = false
    }
  }

  // Get cached data for a specific SKU, store and date range
  const getCachedData = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
  ): ForecastData | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return cache.value[cacheKey] || null
  }

  // Check if data exists in the cache
  const hasCachedData = (skuName: string, storeId: string, dateRange: DateRange): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return Boolean(cache.value[cacheKey])
  }

  // Check loading state for a specific SKU, store and date range
  const isLoadingFor = (skuName: string, storeId: string, dateRange: DateRange): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return loadingStates.value[cacheKey] || false
  }

  // Check predicting state for a specific SKU, store and date range
  const isPredictingFor = (skuName: string, storeId: string, dateRange: DateRange): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return predictingStates.value[cacheKey] || false
  }

  // Get error for specific SKU, store and date range
  const getErrorFor = (skuName: string, storeId: string, dateRange: DateRange): string | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return errorStates.value[cacheKey] || null
  }

  // Refresh specific forecast data
  const refreshSaleVolumeData = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS,
  ): Promise<ForecastData | null> => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)

    // Remove from cache
    delete cache.value[cacheKey]
    delete errorStates.value[cacheKey]
    delete predictingStates.value[cacheKey]

    // Fetch again
    return await fetchSaleVolumeData(skuName, storeId, dateRange)
  }

  // Clear all cache
  const clearCache = () => {
    cache.value = {}
    loadingStates.value = {}
    errorStates.value = {}
    predictingStates.value = {}
  }

  // Clear cache for specific SKU (all stores and date ranges)
  const clearSkuCache = (skuName: string) => {
    const keysToDelete = Object.keys(cache.value).filter((key) => key.startsWith(`${skuName}-`))

    keysToDelete.forEach((key) => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
      delete predictingStates.value[key]
    })
  }

  // Clear cache for specific SKU and store (all date ranges)
  const clearSkuStoreCache = (skuName: string, storeId: string) => {
    const keysToDelete = Object.keys(cache.value).filter((key) =>
      key.startsWith(`${skuName}-${storeId}-`),
    )

    keysToDelete.forEach((key) => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
      delete predictingStates.value[key]
    })
  }

  // Clear error for specific SKU, store and date range
  const clearError = (skuName: string, storeId: string, dateRange: DateRange) => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    errorStates.value[cacheKey] = null
  }

  return {
    // Computed
    isLoading,
    hasAnyError,

    // Actions
    fetchSaleVolumeData,
    getCachedData,
    hasCachedData,
    isLoadingFor,
    isPredictingFor,
    getErrorFor,
    refreshSaleVolumeData,
    clearCache,
    clearSkuCache,
    clearSkuStoreCache,
    clearError,
  }
})
