
import type {
  ForecastData,
  PredictSaleVolumeResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'

export class PredictSaleVolumeResponseAdapter {
  adapt(response: PredictSaleVolumeResponse): ForecastData {
    if (!response.rows || response.rows.length === 0) {
      return {
        categories: [],
        saleVolumes: [],
        saleVolumesPredict: [],
        voucherPrograms: [],
        seriesData: []
      }
    }

    const categories = response.rows.map(row => ({
      date: row.date, // Convert timestamp to date string
      isWeekend: this.isWeekend(new Date(row.date)) ? 1 : 0,
      weatherCondition: '',
      forecast: true // Đây là dữ liệu dự đoán
    }))

    const saleVolumesPredict = response.rows.map(row => row.predictedQuantitySold)

    // Tạo series data từ sale volumes và số lượng SKU per day
    const seriesData = response.rows.map(_ => ({
      quantity: 0,
      skuPerDay: 1 // Default value, có thể cần điều chỉnh theo logic business
    }))

    return {
      categories,
      saleVolumes: [],
      saleVolumesPredict,
      voucherPrograms: [],
      seriesData
    }
  }

  private isWeekend(date: Date): boolean {
    const day = date.getDay()
    return day === 0 || day === 6 // Sunday = 0, Saturday = 6
  }
}
