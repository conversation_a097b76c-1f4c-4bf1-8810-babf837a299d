import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import {
  DateRange,
  getDateRangeTimestamp
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import skuDetailService from '@/views/analytics/views/replenishment/segments/monitor-sku/core/services/SkuDetailService'
import type { GetDailyInventorySoldRequest } from '@/views/analytics/views/replenishment/domains'

export const useSkuMetricStore = defineStore('skuMetric', () => {
  // State - sử dụng Record thay vì Map để có thể reactive
  const loadingStates = ref<Record<string, boolean>>({})
  const errorStates = ref<Record<string, string | null>>({})

  // Cache - sử dụng Record thay vì Map
  const cache = ref<Record<string, PivotTableData>>({})

  // Computed
  const isLoading = computed(() => {
    return Object.values(loadingStates.value).some(loading => loading)
  })

  const hasAnyError = computed(() => {
    return Object.values(errorStates.value).some(error => error !== null)
  })

  // Helper function to generate cache key
  const generateCacheKey = (skuName: string, storeId: string, dateRange: DateRange): string => {
    return `${skuName}-${storeId}-${dateRange}`
  }

  // Actions
  const fetchSkuMetric = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS
  ): Promise<PivotTableData | null> => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)

    // Check cache first
    if (cache.value[cacheKey]) {
      return cache.value[cacheKey]
    }

    // Set loading state for this specific request
    loadingStates.value[cacheKey] = true
    errorStates.value[cacheKey] = null

    try {
      // Tạo request theo định dạng GetDailyInventorySoldRequest
      const { startDate, endDate } = getDateRangeTimestamp(dateRange)
      const request: GetDailyInventorySoldRequest = {
        sku: skuName,
        store: storeId,
        startDate,
        endDate
      }

      const data = await skuDetailService.getMetricDetail(request)

      // Cache the result
      cache.value[cacheKey] = data

      return data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải metric detail'
      errorStates.value[cacheKey] = errorMessage
      return null
    } finally {
      loadingStates.value[cacheKey] = false
    }
  }

  // Get cached data for specific SKU, store and date range
  const getCachedData = (skuName: string, storeId: string, dateRange: DateRange): PivotTableData | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return cache.value[cacheKey] || null
  }

  // Check if data exists in cache
  const hasCachedData = (skuName: string, storeId: string, dateRange: DateRange): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return cacheKey in cache.value
  }

  // Check loading state for specific SKU, store and date range
  const isLoadingFor = (skuName: string, storeId: string, dateRange: DateRange): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return loadingStates.value[cacheKey] || false
  }

  // Get error for specific SKU, store and date range
  const getErrorFor = (skuName: string, storeId: string, dateRange: DateRange): string | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    return errorStates.value[cacheKey] || null
  }

  // Refresh specific SKU metric
  const refreshSkuMetric = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS
  ): Promise<PivotTableData | null> => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)

    // Remove from cache
    delete cache.value[cacheKey]
    delete errorStates.value[cacheKey]

    // Fetch again
    return await fetchSkuMetric(skuName, storeId, dateRange)
  }

  // Clear all cache
  const clearCache = () => {
    cache.value = {}
    loadingStates.value = {}
    errorStates.value = {}
  }

  // Clear cache for specific SKU (all stores and date ranges)
  const clearSkuCache = (skuName: string) => {
    const keysToDelete: string[] = []

    Object.keys(cache.value).forEach(key => {
      if (key.startsWith(`${skuName}-`)) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
    })
  }

  // Clear cache for a specific store (all SKUs and date ranges)
  const clearStoreCache = (storeId: string) => {
    const keysToDelete: string[] = []

    Object.keys(cache.value).forEach(key => {
      if (key.includes(`-${storeId}-`)) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
    })
  }

  // Clear error for specific SKU, store and date range
  const clearError = (skuName: string, storeId: string, dateRange: DateRange) => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange)
    errorStates.value[cacheKey] = null
  }

  return {
    // Computed
    isLoading,
    hasAnyError,

    // Actions
    fetchSkuMetric,
    getCachedData,
    hasCachedData,
    isLoadingFor,
    getErrorFor,
    refreshSkuMetric,
    clearCache,
    clearSkuCache,
    clearStoreCache,
    clearError
  }
})
