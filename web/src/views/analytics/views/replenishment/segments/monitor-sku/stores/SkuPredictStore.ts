import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ForecastData } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse'
import type { PredictSaleVolumeRequest } from '@/views/analytics/views/replenishment/domains'
import { DateRange, getDateRangeTimestamp } from './DateRangeCalculator'
import skuDetailService from '@/views/analytics/views/replenishment/segments/monitor-sku/core/services/SkuDetailService'
import { message } from 'ant-design-vue'

export const useSkuPredictStore = defineStore('skuPredict', () => {
  // State - quản lý loading và error cho từng request
  const loadingStates = ref<Record<string, boolean>>({})
  const errorStates = ref<Record<string, string | null>>({})

  // Cache - key format: "skuName-storeId-dateRange-predictDays"
  const cache = ref<Record<string, ForecastData>>({})

  // Computed
  const isLoading = computed(() => {
    return Object.values(loadingStates.value).some(loading => loading)
  })

  const hasAnyError = computed(() => {
    return Object.values(errorStates.value).some(error => error !== null)
  })

  // Helper function to generate cache key
  const generateCacheKey = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ): string => {
    return `${skuName}-${storeId}-${dateRange}-${predictDays}days`
  }

  // Actions
  const fetchPredictData = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS,
    predictDays?: number
  ): Promise<ForecastData | null> => {
    const { startDate, endDate } = getDateRangeTimestamp(dateRange)

    // Tính toán số ngày predict nếu không được cung cấp
    const originalDays = Math.ceil((endDate - startDate) / (24 * 60 * 60))
    const actualPredictDays = predictDays ?? originalDays

    const cacheKey = generateCacheKey(skuName, storeId, dateRange, actualPredictDays)

    // Check cache first
    if (cache.value[cacheKey]) {
      return cache.value[cacheKey]
    }

    // Set loading state for this specific request
    loadingStates.value[cacheKey] = true
    errorStates.value[cacheKey] = null

    try {
      // Tính toán extended end date cho predict
      const extendedEndDate = endDate + (actualPredictDays * 24 * 60 * 60)

      const predictRequest: PredictSaleVolumeRequest = {
        sku: skuName,
        store: storeId,
        startDate,
        endDate: extendedEndDate
      }

      // Sử dụng Promise để wrap callback-based API thành async/await
      const data = await new Promise<ForecastData>((resolve, reject) => {
        skuDetailService.getPredictSalesVolumes(
          predictRequest,
          (predictData: ForecastData) => {
            // onCompleted
            console.log('predictData', predictData)
            resolve(predictData)
          },
          (error: Error) => {
            // onFailure
            reject(error)
          }
        )
      })

      // Cache the result
      cache.value[cacheKey] = data

      return data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải dữ liệu predict'
      errorStates.value[cacheKey] = errorMessage

      // Show error message but don't throw (non-blocking)
      message.error(`Dự đoán dữ liệu thất bại: ${errorMessage}`)

      return null
    } finally {
      loadingStates.value[cacheKey] = false
    }
  }

  // Non-blocking predict fetch - fire and forget
  const fetchPredictDataNonBlocking = (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS,
    predictDays?: number
  ): void => {
    // Fire and forget - không cần await
    fetchPredictData(skuName, storeId, dateRange, predictDays).catch(() => {
      // Error đã được handle trong fetchPredictData
    })
  }

  // Get cached data for specific SKU, store, date range and predict days
  const getCachedData = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ): ForecastData | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange, predictDays)
    return cache.value[cacheKey] || null
  }

  // Check if data exists in cache
  const hasCachedData = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange, predictDays)
    return Boolean(cache.value[cacheKey])
  }

  // Check loading state for specific parameters
  const isLoadingFor = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ): boolean => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange, predictDays)
    return loadingStates.value[cacheKey] || false
  }

  // Get error for specific parameters
  const getErrorFor = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ): string | null => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange, predictDays)
    return errorStates.value[cacheKey] || null
  }

  // Refresh specific predict data
  const refreshPredictData = async (
    skuName: string,
    storeId: string,
    dateRange: DateRange = DateRange.LAST_7_DAYS,
    predictDays?: number
  ): Promise<ForecastData | null> => {
    const { startDate, endDate } = getDateRangeTimestamp(dateRange)
    const originalDays = Math.ceil((endDate - startDate) / (24 * 60 * 60))
    const actualPredictDays = predictDays ?? originalDays

    const cacheKey = generateCacheKey(skuName, storeId, dateRange, actualPredictDays)

    // Remove from cache
    delete cache.value[cacheKey]
    delete errorStates.value[cacheKey]

    // Fetch again
    return await fetchPredictData(skuName, storeId, dateRange, actualPredictDays)
  }

  // Clear all cache
  const clearCache = () => {
    cache.value = {}
    loadingStates.value = {}
    errorStates.value = {}
  }

  // Clear cache for specific SKU (all stores, date ranges and predict days)
  const clearSkuCache = (skuName: string) => {
    const keysToDelete = Object.keys(cache.value).filter(key => key.startsWith(`${skuName}-`))

    keysToDelete.forEach(key => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
    })
  }

  // Clear cache for specific SKU and store (all date ranges and predict days)
  const clearSkuStoreCache = (skuName: string, storeId: string) => {
    const keysToDelete = Object.keys(cache.value).filter(key => key.startsWith(`${skuName}-${storeId}-`))

    keysToDelete.forEach(key => {
      delete cache.value[key]
      delete loadingStates.value[key]
      delete errorStates.value[key]
    })
  }

  // Clear error for specific parameters
  const clearError = (
    skuName: string,
    storeId: string,
    dateRange: DateRange,
    predictDays: number
  ) => {
    const cacheKey = generateCacheKey(skuName, storeId, dateRange, predictDays)
    errorStates.value[cacheKey] = null
  }

  return {
    // Computed
    isLoading,
    hasAnyError,

    // Actions
    fetchPredictData,
    fetchPredictDataNonBlocking,
    getCachedData,
    hasCachedData,
    isLoadingFor,
    getErrorFor,
    refreshPredictData,
    clearCache,
    clearSkuCache,
    clearSkuStoreCache,
    clearError
  }
})
