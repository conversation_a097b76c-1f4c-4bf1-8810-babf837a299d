
<script setup lang="ts">
import { onMounted, provide, watch } from 'vue'
import { useSKUStore } from './stores/SkuStore.ts'
import { DateRange } from './stores/DateRangeCalculator'
import MonitorSkuSegmentHeader from '@/views/analytics/views/replenishment/segments/monitor-sku/components/MonitorSkuSegmentHeader.vue'
import SkuTable from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/SkuTable.vue'
import { useSkuDetailStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuDetailStore.ts'
import { useSkuMetricStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore.ts'
import {
  useSkuForecastStore
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuSaleVolumeStore.ts'
type Props = {
  dateRange?: DateRange
}
const props = withDefaults(defineProps<Props>(), {
  dateRange: DateRange.LAST_7_DAYS,
})

const skuStore = useSKUStore()
const storeStore = useStoreStore()
const skuDetailStore = useSkuDetailStore()
const skuMetricStore = useSkuMetricStore()
const skuForecastStore = useSkuForecastStore()

// Provide stores cho các component con
provide('skuDetailStore', skuDetailStore)
provide('skuMetricStore', skuMetricStore)
provide('skuForecastStore', skuForecastStore)
provide('storeStore', storeStore)

onMounted(() => {
  // Load initial data
  skuStore.fetchSkuList()
  storeStore.fetchStores()
})

watch(
  () => [props.dateRange] as const,
  async ([newDateRange]) => {
    await skuStore.fetchSkuList(newDateRange)
  },
  { deep: true }
)

</script>

<template>
  <AFlex vertical>
    <MonitorSkuSegmentHeader />
    <!-- SKU Table -->
    <SkuTable
      :records="skuStore.skuList"
      :loading="skuStore.loading"
      :error="skuStore.error"
    />
  </AFlex>
</template>
