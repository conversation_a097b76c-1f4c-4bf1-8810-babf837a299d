<script setup lang="ts">

import { computed, inject, ref } from 'vue'
import type { SelectProps } from 'ant-design-vue'
import type { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore.ts'
interface Props {
  storeId?: string
}

interface Emits {
  (e: 'update:storeId', value: string | undefined): void
}

const algorithm = ref<string | undefined>('XGBoost')
const storeStore = inject<ReturnType<typeof useStoreStore>>('storeStore')
if (!storeStore) {
  throw new Error('storeStore not provided')
}
const algorithmOptions = ref<SelectProps['options']>([
  { key: 'XGBoost', label: 'XGBoost' },
  { key: 'XGBoost_2', label: 'XGBoost 2' },
  { key: 'XGBoost_3', label: 'XGBoost 3' },
]);

const props = withDefaults(defineProps<Props>(), {
  storeId: ''
})


const emit = defineEmits<Emits>()

const store = computed({
  get: () => props.storeId,
  set: (value: string | undefined) => emit('update:storeId', value)
})

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

</script>

<template>
  <AFlex align="center" justify="space-between">
    <AFlex gap="small" align="center">
      <ATypographyTitle :level="5">Forecast Algorithm</ATypographyTitle>
      <ASelect
        v-model:value="algorithm"
        show-search
        placeholder="Select an algorithm"
        style="width: 200px"
        :options="algorithmOptions"
        :filter-option="filterOption"
      />
    </AFlex>

    <ASelect
      v-model:value="store"
      show-search
      placeholder="Select a store"
      style="width: 400px"
      :options="storeStore.storeOptions"
      :filter-option="filterOption"
    />
  </AFlex>
</template>
