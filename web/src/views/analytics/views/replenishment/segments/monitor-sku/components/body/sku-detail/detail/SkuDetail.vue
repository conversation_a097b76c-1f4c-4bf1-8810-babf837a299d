<script setup lang="ts">
import { computed, inject } from 'vue'
import SkuDetailTable from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/detail/SkuDetailTable.vue'
import type { useSkuDetailStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuDetailStore.ts'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'

// Props
interface Props {
  productId: string
  dateRange?: DateRange
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: DateRange.LAST_7_DAYS,
})

// Inject store từ parent component
const skuDetailStore = inject<ReturnType<typeof useSkuDetailStore>>('skuDetailStore')

if (!skuDetailStore) {
  throw new Error('skuDetailStore not provided')
}

// Computed cho SKU hiện tại
const currentSkuDetail = computed(() => {
  return skuDetailStore.getCachedData(props.productId, props.dateRange) ?? emptySkuDetail.value
})

const emptySkuDetail = computed(() => {
  return {
    dayAxis: [],
    tableData: [
      {
        id: '1',
        metricName: 'Current Inventory',
        storeInfos: [],
      },
      {
        id: '2',
        metricName: 'Days on Hand',
        storeInfos: [],
      },
      {
        id: '3',
        metricName: 'Inventory Status',
        storeInfos: [],
      },
    ],
  } as PivotTableData
})

const isLoading = computed(() => {
  return skuDetailStore.isLoadingFor(props.productId, props.dateRange)
})

const errorMessage = computed(() => {
  return skuDetailStore.getErrorFor(props.productId, props.dateRange)
})

const handleClearError = () => {
  if (props.productId) {
    skuDetailStore.clearError(props.productId, props.dateRange)
  }
}
</script>

<template>
  <AFlex vertical gap="small">
    <AFlex justify="space-between" align="center">
      <ATypographyTitle :level="5">Detail</ATypographyTitle>
    </AFlex>
    <ASpin :spinning="isLoading">
      <!-- Hiển thị error message nếu có -->
      <AAlert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        closable
        @close="handleClearError"
        style="margin-bottom: 16px"
      />

      <!-- Hiển thị empty state khi không có data -->
      <AEmpty v-else-if="!isLoading && !currentSkuDetail" description="Không có dữ liệu detail" />

      <!-- Hiển thị table khi có data -->
      <SkuDetailTable v-else-if="currentSkuDetail" :sku-detail="currentSkuDetail" />
    </ASpin>
  </AFlex>
</template>

<style scoped></style>
