<script setup lang="ts">
import { computed, inject, watch } from 'vue'
import type { useSkuForecastStore } from '../../../../stores/SkuSaleVolumeStore.ts'
import { useSkuPredictStore } from '../../../../stores/SkuPredictStore.ts'
import { DateRange } from '../../../../stores/DateRangeCalculator'
import type { ForecastData } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse'
import ForecastChart from './ForecastChart.vue'
import ForecastSkuDetailHeader
  from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/forecast-chart/ForecastSkuDetailHeader.vue'

// Props
interface Props {
  productId: string
  storeId?: string
  dateRange?: DateRange
}

interface Emits {
  (e: 'update:storeId', value: string | undefined): void
}

const props = withDefaults(defineProps<Props>(), {
  storeId: '',
  dateRange: DateRange.LAST_7_DAYS,
})

const emit = defineEmits<Emits>()

const currentStoreId = computed({
  get: () => props.storeId,
  set: (value: string | undefined) => emit('update:storeId', value)
})

// Inject store từ parent component
const skuForecastStore = inject<ReturnType<typeof useSkuForecastStore>>('skuForecastStore')

if (!skuForecastStore) {
  throw new Error('skuForecastStore not provided')
}

// Initialize predict store
const skuPredictStore = useSkuPredictStore()

// Computed cho forecast data hiện tại
const currentSaleVolumeData = computed(() => {
  return skuForecastStore.getCachedData(props.productId, props.storeId, props.dateRange)
})

// Computed cho predict data hiện tại
const currentPredictData = computed(() => {
  const DEFAULT_PREDICT_DAYS = 7
  return skuPredictStore.getCachedData(props.productId, props.storeId, props.dateRange, DEFAULT_PREDICT_DAYS)
})

// Computed cho merged data
const mergedForecastData = computed((): ForecastData | null => {
  const saleData = currentSaleVolumeData.value
  const predictData = currentPredictData.value

  if (!saleData) {
    return null
  }

  // Merge data theo công thức yêu cầu
  const mergedData: ForecastData = {
    // 1. categories - lấy từ predict nếu có, không thì từ sale
    categories: predictData?.categories || saleData.categories,

    // 2. saleVolumes - lấy từ sale
    saleVolumes: saleData.saleVolumes,

    // 3. saleVolumesPredict - lấy từ predict, rỗng nếu chưa có
    saleVolumesPredict: predictData?.saleVolumesPredict || [],

    // 4. voucherPrograms - lấy từ sale (voucher bỏ qua nhưng vẫn giữ để tương thích)
    voucherPrograms: saleData.voucherPrograms,

    // 5. seriesData - lấy từ sale
    seriesData: saleData.seriesData
  }

  return mergedData
})

const isLoading = computed(() => {
  return skuForecastStore.isLoadingFor(props.productId, props.storeId, props.dateRange)
})

const errorMessage = computed(() => {
  return skuForecastStore.getErrorFor(props.productId, props.storeId, props.dateRange)
})

const handleClearError = () => {
  if (props.productId && props.storeId) {
    skuForecastStore.clearError(props.productId, props.storeId, props.dateRange)
  }
}

// Watch currentSaleVolumeData để trigger predict fetch
watch(currentSaleVolumeData, (saleData) => {
  if (saleData && props.productId && props.storeId) {
    const DEFAULT_PREDICT_DAYS = 7
    // Non-blocking fetch predict data
    skuPredictStore.fetchPredictDataNonBlocking(
      props.productId,
      props.storeId,
      props.dateRange,
      DEFAULT_PREDICT_DAYS
    )
  }
}, { immediate: true })
</script>

<template>
  <AFlex vertical gap="small">
    <ForecastSkuDetailHeader v-model:store-id="currentStoreId" />

    <ASpin :spinning="isLoading" style="min-height: 500px">
      <!-- Hiển thị error message nếu có -->
      <AAlert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        closable
        @close="handleClearError"
        style="margin-bottom: 16px"
      />

      <!-- Hiển thị empty state khi không có data -->
      <AEmpty
        v-else-if="!isLoading && !mergedForecastData"
        description="Không có dữ liệu forecast"
      />

      <!-- Hiển thị chart khi có data -->
      <ForecastChart
        class="forecast-chart"
        v-else-if="mergedForecastData"
        :forecast-data="mergedForecastData"
      />
    </ASpin>
  </AFlex>
</template>

<style scoped></style>

<style>
.forecast-chart {
  box-shadow: 0 0 3px 0 rgba(16, 24, 40, 0.15);
  border-radius: 8px;
  height: 500px;
}
</style>
