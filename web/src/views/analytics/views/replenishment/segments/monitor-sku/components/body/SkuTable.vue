<script setup lang="ts">
import { ref, h, computed, nextTick, watch } from 'vue'
import type { SKUModel } from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/models/SKU.model'
import SkuDetail from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/SkuDetail.vue'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import { useAvailableWidthProvider } from '@/hooks/userContainerWidth.ts'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects'

const props = defineProps<{
  records: SKUModel[]
  loading?: boolean
  error?: string | null
}>()

const expandedRows = ref<string[]>([])
const tableRef = ref<HTMLDivElement | null>(null)
const containerRef = ref<HTMLDivElement>()

// Sử dụng hook để provide available width cho child components
const { updateAvailableWidth } = useAvailableWidthProvider(containerRef, {
  left: 32, // margin-left của .main-table-detail
  padding: 32
})

// Use hover effects
useTableHoverEffects(containerRef)

// Tính toán height động
const ROW_HEIGHT = 54 // Height của mỗi row (bao gồm border)
const HEADER_HEIGHT = 55 // Height của table header
const EXPANDED_ROW_HEIGHT = 400 // Estimated height cho expanded row
const MIN_HEIGHT = 400 // Minimum height
const MAX_HEIGHT = 600 // Maximum height để tránh table quá cao

const calculateTableHeight = computed(() => {
  const baseHeight = HEADER_HEIGHT
  const dataRowsHeight = props.records.length * ROW_HEIGHT
  const expandedRowsHeight = expandedRows.value.length * EXPANDED_ROW_HEIGHT

  const totalHeight = baseHeight + dataRowsHeight + expandedRowsHeight

  // Giới hạn trong khoảng min-max
  return Math.min(Math.max(totalHeight, MIN_HEIGHT), MAX_HEIGHT)
})

const columns = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
  },
  {
    title: 'Product name',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
    ellipsis: true,
    customRender: ({ text, record }: { text: string; record: SKUModel }) => {
      const isExpanded = expandedRows.value.includes(record.id)
      return h(ProductNameCell, {
        productName: text,
        expanded: {
          enabled: true,
          isExpanded: isExpanded,
        },
      })
    },
  },
  {
    title: 'Inventory quantity',
    children: [
      {
        title: 'DC',
        dataIndex: 'inventoryDC',
        key: 'inventoryDC',
        width: 100,
      },
      {
        title: 'Store',
        dataIndex: 'inventoryStore',
        key: 'inventoryStore',
        width: 100,
      },
    ],
  },
  {
    title: 'Forecast',
    children: [
      {
        title: 'Week',
        dataIndex: 'forecastWeek',
        key: 'forecastWeek',
        width: 100,
      },
      {
        title: '1 Month',
        dataIndex: 'forecastMonth',
        key: 'forecastMonth',
        width: 100,
      },
      {
        title: '3 Month',
        dataIndex: 'forecast3Month',
        key: 'forecast3Month',
        width: 100,
      },
      {
        title: '6 Month',
        dataIndex: 'forecast6Month',
        key: 'forecast6Month',
        width: 100,
      },
      {
        title: '1 Year',
        dataIndex: 'forecastYear',
        key: 'forecastYear',
        width: 100,
      },
    ],
  },
]

const handleExpandRow = (expanded: boolean, record: SKUModel) => {
  if (expanded) {
    expandedRows.value.push(record.id)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.id)
  }

  // Update available width khi expand/collapse vì có thể ảnh hưởng đến layout
  nextTick(() => {
    updateAvailableWidth()
  })
}

// Watch để update height khi data thay đổi
watch(() => props.records.length, () => {
  nextTick(() => {
    updateAvailableWidth()
  })
})
</script>

<template>
  <div
    ref="containerRef"
    class="main-table-container table-hover-effects"
    :style="{ height: `${calculateTableHeight}px` }"
  >
    <!-- Error Display -->
    <AAlert
      v-if="props.error"
      :message="props.error"
      type="error"
      show-icon
      closable
      style="margin-bottom: 16px"
    />

    <!-- Table -->
    <ATable
      ref="tableRef"
      :columns="columns"
      :scroll="{ x: 'max-content' }"
      :data-source="props.records"
      :expandIcon="() => null"
      expandRowByClick
      @expand="handleExpandRow"
      :loading="props.loading"
      row-key="id"
      :show-expand-column="false"
      class="main-table"
    >
      <template #expandedRowRender="{ record }">
        <SkuDetail class="main-table-detail" :product-id="record.productName" />
      </template>
    </ATable>
  </div>
</template>

<style scoped>
.main-table-container {
  position: relative;
  width: 100%;
  transition: height 0.3s ease;
}

.main-table {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  scrollbar-width: none;
}

/* Đảm bảo expanded row detail không làm tràn */
.main-table-detail {
  margin-left: 32px;
  max-width: calc(100% - 32px);
  overflow-x: auto;
}

/* Empty state styling */
.main-table :deep(.ant-empty) {
  margin: 40px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-table-detail {
    margin-left: 16px;
    max-width: calc(100% - 16px);
  }
}
</style>

<style scoped src="/src/assets/pivot-tables.css"></style>
<style lang="css" src="/src/assets/table-hover.css"></style>
