<script setup lang="ts">
import { watch, onMounted, inject, ref } from 'vue'
import MonitorMetricSegment from '@/views/analytics/views/replenishment/segments/monitor-metric/MonitorMetricSegment.vue'
import SkuDetail from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/detail/SkuDetail.vue'
import SkuMetric from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/metric/SkuMetric.vue'
import ForecastDetail from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/sku-detail/forecast-chart/ForecastDetail.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import type { useSkuDetailStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuDetailStore.ts'
import { useSkuMetricStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import type { useSkuForecastStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuSaleVolumeStore.ts'
import type { useStoreStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/StoreStore.ts'
const validStoreProvided = () => {
  if (!skuDetailStore) {
    throw new Error('skuDetailStore not provided')
  }

  if (!skuMetricStore) {
    throw new Error('skuMetricStore not provided')
  }
  if (!skuForecastStore) {
    throw new Error('skuForecastStore not provided')
  }

  if (!storeStore) {
    throw new Error('storeStore not provided')
  }
}

// Props
interface Props {
  productId: string
}

const props = withDefaults(defineProps<Props>(), {
})

// Tạo ref value cho dateRange với enum
const dateRange = ref<DateRange>(DateRange.LAST_7_DAYS)

const skuDetailStore = inject<ReturnType<typeof useSkuDetailStore>>('skuDetailStore')
const skuMetricStore = inject<ReturnType<typeof useSkuMetricStore>>('skuMetricStore')
const skuForecastStore = inject<ReturnType<typeof useSkuForecastStore>>('skuForecastStore')
const storeStore = inject<ReturnType<typeof useStoreStore>>('storeStore')

validStoreProvided();

const storeId = ref(storeStore!.stores[0]?.id ?? '')
// Function để fetch data từ 3 stores
const fetchAllData = async (productId: string, storeId: string, dateRangeValue: DateRange) => {
  if (productId && storeId) {
    await Promise.all([
      skuDetailStore!.fetchSkuDetail(productId, dateRangeValue),
      skuMetricStore!.fetchSkuMetric(productId, storeId, dateRangeValue),
      skuForecastStore!.fetchSaleVolumeData(productId, storeId, dateRangeValue),
    ])
  }
}

onMounted(async () => {
  await fetchAllData(props.productId, storeId.value, dateRange.value)
})

watch(
  () => [props.productId, storeId.value, dateRange.value] as const,
  async ([newProductId, newStoreId, newDateRange]) => {
    console.log('newProductId', newStoreId)
    await fetchAllData(newProductId, newStoreId, newDateRange)
  },
  { deep: true },
)


</script>

<template>
  <AFlex vertical gap="middle">
    <MonitorMetricSegment v-model:date-range="dateRange" />
    <SkuDetail :productId="productId" :date-range="dateRange" />
    <ForecastDetail :productId="productId" v-model:storeId="storeId" :date-range="dateRange" />
    <SkuMetric :productId="productId" :storeId="storeId" :date-range="dateRange" />
  </AFlex>
</template>

<style></style>
