<script setup lang="ts">
import { RightOutlined, ExportOutlined } from '@ant-design/icons-vue'

interface Props {
  productName: string
  expanded?: {
    enabled?: boolean
    isExpanded?: boolean
  }
  link?: string
}

defineProps<Props>()
</script>

<template>
  <div class="product-name-cell">
    <RightOutlined
      v-if="expanded?.enabled ?? false"
      :class="['expand-icon', { 'is-expanded': expanded?.isExpanded ?? false }]"
    />

    <a :href="link" v-if="link" target="_blank" rel="noopener noreferrer">
      <ExportOutlined />
    </a>
    <span class="product-name">{{ productName }}</span>
  </div>
</template>

<style scoped>
.product-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.is-expanded {
  transform: rotate(90deg);
}

.product-name {
  margin-left: 4px;
}
</style>
