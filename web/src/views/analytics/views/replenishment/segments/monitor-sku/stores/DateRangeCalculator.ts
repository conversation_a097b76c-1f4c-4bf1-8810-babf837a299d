export enum DateRange {
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  MONTH_TO_DATE = 'MONTH_TO_DATE',
  NEXT_7_DAYS = 'NEXT_7_DAYS',
}

export const DateRangeLabels: Record<DateRange, string> = {
  [DateRange.LAST_7_DAYS]: '7 ngày gần nhất',
  [DateRange.LAST_30_DAYS]: '30 ngày gần nhất',
  [DateRange.MONTH_TO_DATE]: 'Từ đầu tháng đến nay',
  [DateRange.NEXT_7_DAYS]: '7 ngày tiếp theo'
}

export const getDateRangeTimestamp = (range: DateRange): { startDate: number; endDate: number } => {
  const now = new Date()
  const nowAsSecond: number = Math.floor(now.getTime() / 1000) // Convert to seconds

  switch (range) {
    case DateRange.LAST_7_DAYS:
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      return { startDate: Math.floor(last7Days.getTime() / 1000), endDate: nowAsSecond }

    case DateRange.LAST_30_DAYS:
      const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      return { startDate: Math.floor(last30Days.getTime() / 1000), endDate: nowAsSecond }

    case DateRange.MONTH_TO_DATE:
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      return { startDate: Math.floor(monthStart.getTime() / 1000), endDate: nowAsSecond }
    case DateRange.NEXT_7_DAYS:
      const next7Days = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      return { startDate: nowAsSecond, endDate: next7Days.getDate() }
    default:
      return { startDate: nowAsSecond, endDate: nowAsSecond }
  }
}
