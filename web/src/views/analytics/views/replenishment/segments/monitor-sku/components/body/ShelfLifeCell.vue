<script setup lang="ts">
import { computed } from 'vue'
import {ArrowUpOutlined, ArrowDownOutlined, ArrowRightOutlined } from '@ant-design/icons-vue';
const props = defineProps<{
  shelfLife: 'Short' | 'Medium' | 'Long'
}>()

const color = computed(() => {
  switch (props.shelfLife) {
    case 'Short':
      return '#C01048'
    case 'Medium':
      return '#004EEB'
    case 'Long':
      return '#087443'
    default:
      return 'black'
  }
})

const backgroundColor = computed(() => {
  switch (props.shelfLife) {
    case 'Short':
      return '#FDF2FA'
    case 'Medium':
      return '#EFF4FF'
    case 'Long':
      return '#EDFCF2'
    default:
      return 'white'
  }
})

const icon = computed(() => {
  switch (props.shelfLife) {
    case 'Short':
      return ArrowDownOutlined
    case 'Medium':
      return ArrowRightOutlined
    case 'Long':
      return ArrowUpOutlined
    default:
      return ArrowRightOutlined
  }
})
</script>

<template>
  <div class="shelf-life-cell">
    <component :is="icon" />
    {{ shelfLife }}
  </div>
</template>

<style scoped>
.shelf-life-cell {
  color: v-bind(color);
  background-color: v-bind(backgroundColor);
  padding: 2px 8px;
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}
</style>
