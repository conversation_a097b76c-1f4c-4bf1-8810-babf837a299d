<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { Voucher } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/Voucher.ts'
import type {
  ForecastCategory,
  ForecastData
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'

// Constants for voucher configuration
const VOUCHER_MARKER_WIDTH = 32
const VOUCHER_MARKER_HEIGHT = 32
const VOUCHER_Y_OFFSET = 12
const VOUCHER_BORDER_WIDTH = 1
const VOUCHER_HOVER_BORDER_WIDTH = 1

// Constants for chart configuration
const COLUMN_POINT_PADDING = 0.01
const COLUMN_GROUP_PADDING = 0.05
const FORECAST_BORDER_WIDTH = 0.5
const PATTERN_STROKE_WIDTH = 1
const PATTERN_SIZE = 3

// Constants for colors
const VOUCHER_BORDER_COLOR = '#EAECF0'
const VOUCHER_HOVER_FILL_COLOR = 'rgba(234, 236, 240, 0.1)'
const VOUCHER_HOVER_BORDER_COLOR = '#98A2B3'
const VOUCHER_COLOR = 'transparent'
const SUNNY_COLOR = '#FFD700'
const DEFAULT_COLOR = '#1E90FF'
const FORECAST_STROKE_COLOR = '#1E90FF'
const ACTUAL_LINE_COLOR = '#28a745'
const FORECAST_LINE_COLOR = '#ff5722'

const RAINY_ICON = '❄️'
const SUNNY_ICON = '☀️'

const props = defineProps<{
  forecastData: ForecastData,
}>()

const createVoucherIcon = () => {
  return `data:image/svg+xml;base64,${btoa(`
  <svg width="24" height="24" viewBox="0 0 19 18" fill="white"  xmlns="http://www.w3.org/2000/svg">
<rect x="0.833496" y="0.5" width="17" height="17" rx="8.5" stroke="#EAECF0"/>
<rect width="8" height="8" transform="translate(5.3335 5)" fill="white" style="mix-blend-mode:multiply"/>
<path d="M7.8335 9C8.10964 9 8.3335 8.77614 8.3335 8.5C8.3335 8.22386 8.10964 8 7.8335 8C7.55735 8 7.3335 8.22386 7.3335 8.5C7.3335 8.77614 7.55735 9 7.8335 9Z" fill="#667085"/>
<path d="M9.3335 12.5C9.30059 12.5002 9.26798 12.4939 9.23752 12.4814C9.20706 12.469 9.17936 12.4507 9.156 12.4275L6.481 9.75C6.3871 9.65666 6.33405 9.52989 6.3335 9.3975V7.5C6.3335 7.36739 6.38617 7.24021 6.47994 7.14645C6.57371 7.05268 6.70089 7 6.8335 7H8.731C8.86339 7.00056 8.99016 7.0536 9.0835 7.1475L11.761 9.8225C11.7844 9.84574 11.803 9.87339 11.8157 9.90386C11.8284 9.93432 11.8349 9.967 11.8349 10C11.8349 10.033 11.8284 10.0657 11.8157 10.0961C11.803 10.1266 11.7844 10.1543 11.761 10.1775L9.511 12.4275C9.48764 12.4507 9.45993 12.469 9.42947 12.4814C9.39901 12.4939 9.3664 12.5002 9.3335 12.5ZM6.8335 7.5V9.3975L9.3335 11.8975L11.231 10L8.731 7.5H6.8335Z" fill="#667085"/>
<path d="M12.261 8.3225L9.5835 5.6475C9.49016 5.5536 9.36339 5.50056 9.231 5.5H7.3335C7.20089 5.5 7.07371 5.55268 6.97994 5.64645C6.88617 5.74021 6.8335 5.86739 6.8335 6V6.5H7.3335V6H9.231L11.731 8.5L11.406 8.8225L11.761 9.1775L12.261 8.6775C12.2844 8.65426 12.303 8.62661 12.3157 8.59614C12.3284 8.56568 12.3349 8.533 12.3349 8.5C12.3349 8.467 12.3284 8.43432 12.3157 8.40386C12.303 8.37339 12.2844 8.34574 12.261 8.3225Z" fill="#667085"/>
</svg>
  `)}`
}

const createVoucherMarkerConfig = () => ({
  symbol: `url(${createVoucherIcon()})`,
  width: VOUCHER_MARKER_WIDTH,
  height: VOUCHER_MARKER_HEIGHT,
  lineColor: VOUCHER_BORDER_COLOR,
  lineWidth: VOUCHER_BORDER_WIDTH,
  states: {
    hover: {
      fillColor: VOUCHER_HOVER_FILL_COLOR,
      lineColor: VOUCHER_HOVER_BORDER_COLOR,
      lineWidth: VOUCHER_HOVER_BORDER_WIDTH,
    },
  },
})

const createVoucherDataPoint = (voucher: any, index: number) => {
  const columnHeight = props.forecastData.seriesData[index].quantity + props.forecastData.seriesData[index].skuPerDay
  const yPosition = columnHeight + VOUCHER_Y_OFFSET

  return {
    x: index,
    y: yPosition,
    marker: createVoucherMarkerConfig(),
    voucher: voucher.voucher,
    date: voucher.date,
    color: VOUCHER_COLOR,
  }
}

const voucherSeries = computed(() => {
  return []
})

const bottomSeries = computed(() => props.forecastData.seriesData.map((d, i) => {
  const isForecast = props.forecastData.categories[i].forecast
  const weather = props.forecastData.categories[i].weatherCondition
  return {
    y: d.quantity,
    color: isForecast
      ? {
        pattern: {
          path: {
            d: 'M 3 0 L 0 3',
            stroke: FORECAST_STROKE_COLOR,
            strokeWidth: PATTERN_STROKE_WIDTH,
          },
          width: PATTERN_SIZE,
          height: PATTERN_SIZE,
          backgroundColor: '#ffffff',
        },
      }
      : weather === 'sunny'
        ? SUNNY_COLOR
        : DEFAULT_COLOR,
    borderWidth: isForecast ? FORECAST_BORDER_WIDTH : 0,
    borderColor: DEFAULT_COLOR,
  }
}))

const topSeries = computed(() => props.forecastData.seriesData.map((d, i) => {
  const isForecast = props.forecastData.categories[i].forecast
  return {
    y: d.skuPerDay,
    color: isForecast ? 'white' : 'transparent',
    borderWidth: isForecast ? FORECAST_BORDER_WIDTH : 0,
    borderColor: DEFAULT_COLOR,
  }
}))

const renderCategory = (category: ForecastCategory) => {
  const {weatherCondition, date} = category
  switch (weatherCondition) {
    case 'sunny':
      return SUNNY_ICON
    case 'cloudy':
      return RAINY_ICON
    default:
      return `${date.split('-')[2]}/${date.split('-')[1]}`  // Format DD/MM
  }
}

const chartOptions = computed(() => ({
  chart: {
    type: 'column',
  },
  exporting: {
    enabled: false,
  },
  credits: {
    enabled: false,
  },
  title: {
    text: '',
    enabled: false,
  },
  xAxis: {
    categories: props.forecastData.categories.map((c) => renderCategory(c)),
  },
  yAxis: {
    title: {
      text: 'Inventory Quantity',
    },
  },
  plotOptions: {
    column: {
      stacking: 'normal',
      borderWidth: 0,
      pointPadding: COLUMN_POINT_PADDING,
      groupPadding: COLUMN_GROUP_PADDING,
    },
    spline: {
      marker: {
        enabled: false,
      },
    },
    scatter: {
      marker: {
        states: {
          lineWidth: 3,
          lineColor: 'green',
          hover: {
            enabled: true,
          },
        },
      },
      tooltip: {
        followPointer: true,
      },
    },
  },
  series: [
    {
      name: 'Inventory Quantity',
      type: 'column',
      data: bottomSeries.value,
    },
    {
      name: 'SKU per day',
      type: 'column',
      data: topSeries.value,
    },
    {
      name: 'Sức bán thực tế',
      type: 'spline',
      color: ACTUAL_LINE_COLOR,
      data: props.forecastData.saleVolumes,
    },
    {
      name: 'Sức bán dự đoán',
      type: 'spline',
      color: FORECAST_LINE_COLOR,
      data: props.forecastData.saleVolumesPredict,
    },
    {
      name: 'Vouchers',
      type: 'scatter',
      data: voucherSeries.value,
      showInLegend: false,
      enableMouseTracking: true,
    },
  ],
}))

const windowWidth = ref(window.innerWidth)
const chartRef = ref<any>(null)

// // Watch cho thay đổi toàn bộ forecastData (khi change date range)
// watch(
//   () => props.forecastData,
//   (newData) => {
//     if (chartRef.value && chartRef.value.chart && newData) {
//       console.log('🔄 Updating chart với data mới:', newData.categories.length, 'ngày')
//
//       // Update toàn bộ chart options thay vì chỉ một phần
//       chartRef.value.chart.update(chartOptions.value, true, true)
//     }
//   },
//   { deep: true, immediate: false }
// )
//
// // Watch riêng cho predict data (khi có predict mới)
// watch(
//   () => props.forecastData.saleVolumesPredict,
//   (newPredictData, oldPredictData) => {
//     if (chartRef.value && chartRef.value.chart && newPredictData && newPredictData.length > 0) {
//       // Chỉ update predict nếu đây không phải là thay đổi toàn bộ data
//       const currentCategoriesLength = props.forecastData.categories.length
//
//       if (newPredictData.length > currentCategoriesLength) {
//         // Case: Predict data dài hơn current data - extend chart
//         const additionalDays = newPredictData.length - currentCategoriesLength
//         const extendedCategories = [...props.forecastData.categories]
//
//         // Tạo categories cho những ngày tiếp theo
//         for (let i = 0; i < additionalDays; i++) {
//           const lastDate = extendedCategories[extendedCategories.length - 1]?.date
//           if (lastDate) {
//             const nextDate = new Date(lastDate)
//             nextDate.setDate(nextDate.getDate() + 1)
//             extendedCategories.push({
//               isWeekend: nextDate.getDay() === 0 || nextDate.getDay() === 6 ? 1 : 0,
//               date: nextDate.toISOString().split('T')[0],
//               forecast: true,
//               weatherCondition: 'sunny'
//             })
//           }
//         }
//
//         // Update xAxis categories
//         chartRef.value.chart.xAxis[0].update({
//           categories: extendedCategories.map((c) => renderCategory(c))
//         })
//
//         // Extend other series với null values
//         const nullExtension = new Array(additionalDays).fill(null)
//
//         chartRef.value.chart.series[0].update({
//           data: [...bottomSeries.value, ...nullExtension]
//         })
//         chartRef.value.chart.series[1].update({
//           data: [...topSeries.value, ...nullExtension]
//         })
//         chartRef.value.chart.series[2].update({
//           data: [...props.forecastData.saleVolumes, ...nullExtension]
//         })
//       }
//
//       // Update predict series
//       const predictSeriesIndex = 3
//       chartRef.value.chart.series[predictSeriesIndex].update({
//         data: newPredictData
//       }, true, true)
//     }
//   },
//   { deep: true }
// )



const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (chartRef.value && chartRef.value.chart) {
    setTimeout(() => {
      chartRef.value.chart.reflow()
    }, 100)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <highcharts ref="chartRef" :options="chartOptions"></highcharts>
</template>
