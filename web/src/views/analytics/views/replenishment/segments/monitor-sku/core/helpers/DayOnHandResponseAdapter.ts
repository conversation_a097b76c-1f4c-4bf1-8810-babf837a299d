import type { GetDayOnHandResponse, DayOnHandRow } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import { PivotTableData, TableDataItem, StoreInfo, type Status } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'

export class DayOnHandResponseAdapter {
  toPivotTableData(response: GetDayOnHandResponse): PivotTableData {
    const { rows } = response
    const dayAxis = this.toDayAxis(rows)
    const tableData = this.toTableData(rows)

    return new PivotTableData(dayAxis, tableData)
  }

  private toDayAxis(rows: DayOnHandRow[]): string[] {
    // Tạo dayAxis từ storeId hoặc có thể là các ngày trong tương lai
    // Vì DayOnHandRow chỉ có thông tin về inventory hiện tại, ta sẽ tạo một mảng đại diện
    const uniqueStoreIds = [...new Set(rows.map(row => row.store))]
    return uniqueStoreIds.length > 0 ? uniqueStoreIds : []
  }

  private toTableData(rows: DayOnHandRow[]): TableDataItem[] {
    // Group rows by metrics - tạo các metrics khác nhau từ data
    const metrics = [
      {
        id: '1',
        metricName: 'Current Inventory',
        getValue: (row: DayOnHandRow) => row.currentInventory,
        getStatus: (row: DayOnHandRow): Status => this.getInventoryStatus(row.currentInventory)
      },
      {
        id: '2',
        metricName: 'Days on Hand',
        getValue: (row: DayOnHandRow) => row.daysOnHand,
        getStatus: (row: DayOnHandRow): Status => this.getDaysOnHandStatus(row.daysOnHand)
      },
      {
        id: '3',
        metricName: 'Inventory Status',
        getValue: (row: DayOnHandRow) => this.getInventoryStatusText(row.currentInventory, row.daysOnHand),
        getStatus: (row: DayOnHandRow): Status => this.getOverallStatus(row.currentInventory, row.daysOnHand)
      }
    ]

    return metrics.map(metric => {
      const storeInfos = rows.map(row => new StoreInfo(
        row.store,
        metric.getValue(row),
        metric.getStatus(row)
      ))

      return new TableDataItem(metric.id, metric.metricName, storeInfos)
    })
  }

  private getInventoryStatus(inventory: number): Status {
    if (inventory <= 0) return 'error'
    if (inventory < 10) return 'warning'
    return 'normal'
  }

  private getDaysOnHandStatus(daysOnHand: number): Status {
    if (daysOnHand <= 0) return 'error'
    if (daysOnHand < 7) return 'warning'
    if (daysOnHand > 30) return 'warning'
    return 'normal'
  }

  private getOverallStatus(inventory: number, daysOnHand: number): Status {
    if (inventory <= 0 || daysOnHand <= 0) return 'error'
    if (inventory < 10 || daysOnHand < 7) return 'warning'
    return 'normal'
  }

  private getInventoryStatusText(inventory: number, daysOnHand: number): string {
    if (inventory <= 0) return 'Out of Stock'
    if (inventory < 10) return 'Low Stock'
    if (daysOnHand < 7) return 'Restock Soon'
    if (daysOnHand > 30) return 'Overstock'
    return 'In Stock'
  }
}
