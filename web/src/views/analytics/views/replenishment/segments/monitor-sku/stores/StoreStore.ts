import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { StoreRow } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import skuService from '@/views/analytics/views/replenishment/segments/monitor-sku/core/services/SkuService'

export const useStoreStore = defineStore('stores', () => {
  // State
  const stores = ref<StoreRow[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const storeMap = ref<Record<string, StoreRow>>({});

  // Getters
  const storeOptions = computed(() =>
    stores.value.map((store) => ({
      label: store.name,
      value: store.id,
    })),
  )

  const hasStores = computed(() => stores.value.length > 0)

  // Actions
  const fetchStores = async () => {
    if (loading.value) return

    loading.value = true
    error.value = null

    try {
      stores.value = await skuService.getStores();
      storeMap.value = Object.fromEntries(stores.value.map(store => [store.id, store]));
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải danh sách stores'
      console.error('Error fetching stores:', err)
    } finally {
      loading.value = false
    }
  }

  const clearStores = () => {
    stores.value = []
    error.value = null
  }

  const getStoreById = (id: string) => {
    return storeMap.value[id] || null
  }

  const getStoreByName = (name: string) => {
    return stores.value.find((store) => store.name === name)
  }

  return {
    // State
    stores,
    loading,
    error,

    // Getters
    storeOptions,
    hasStores,

    // Actions
    fetchStores,
    clearStores,
    getStoreById,
    getStoreByName,
  }
})
