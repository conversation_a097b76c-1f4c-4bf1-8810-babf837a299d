<script setup lang="ts">
withDefaults(
  defineProps<{
    status: string
    color: string
    backgroundColor: string

    showIcon?: boolean
  }>(),
  {
    showIcon: true,
  },
)
</script>

<template>
  <div class="status-cell">
    <div class="status-icon" v-if="showIcon" />
    <p class="status-text">{{ status }}</p>
  </div>
</template>

<style scoped>
.status-cell {
  padding: 2px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 16px;
  flex-direction: row;
  background: v-bind(backgroundColor);
}

.status-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: v-bind(color);
}

.status-text {
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0;
  text-align: center;
  color: v-bind(color);
  margin-bottom: 0;
}
</style>
