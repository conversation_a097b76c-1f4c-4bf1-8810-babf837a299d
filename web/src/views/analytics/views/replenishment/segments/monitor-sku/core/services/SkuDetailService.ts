import  { type PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import queryRepository, {
  QueryRepository,
} from '@/views/analytics/views/replenishment/repositories/QueryRepository.ts'
import type {
  GetDailyInventorySoldRequest, GetDayOnHandRequest,
  GetSalesRequest,
  PredictSaleVolumeRequest
} from '@/views/analytics/views/replenishment/domains'
import type {
  ForecastData,
  GetSalesResponse,
  GetDayOnHandResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import {
  SalesResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/SalesResponseAdapter.ts'
import {
  DayOnHandResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/DayOnHandResponseAdapter.ts'
import {
  DailyInventorySoldResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/DailyInventorySoldResponseAdapter.ts'
import predictRepository, { type PredictRepository } from '@/views/analytics/views/replenishment/repositories/PredictRepository.ts'
import {
  PredictSaleVolumeResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/PredictSaleVolumeResponseAdapter.ts'

export abstract class SkuDetailService {
  abstract getSkuDetailByProductId(request: GetSalesRequest): Promise<PivotTableData>

  abstract getForecastDetail(request: GetSalesRequest): Promise<ForecastData>

  abstract getMetricDetail(request: GetDailyInventorySoldRequest): Promise<PivotTableData>

  abstract getPredictSalesVolumes(request: PredictSaleVolumeRequest, onCompleted: (data: ForecastData) => void, onFailure: (error: Error) => void): Promise<void>
}

export class SkuDetailServiceImpl extends SkuDetailService {
  private readonly queryRepository: QueryRepository
  private readonly predictRepository: PredictRepository

  private readonly dayOnHandAdapter: DayOnHandResponseAdapter
  private readonly salesAdapter: SalesResponseAdapter
  private readonly dailyInventorySoldAdapter: DailyInventorySoldResponseAdapter
  private readonly predictSaleVolumeResponseAdapter: PredictSaleVolumeResponseAdapter

  constructor(queryRepository: QueryRepository, predictRepository: PredictRepository) {
    super()
    this.queryRepository = queryRepository
    this.predictRepository = predictRepository

    this.dayOnHandAdapter = new DayOnHandResponseAdapter()
    this.salesAdapter = new SalesResponseAdapter()
    this.dailyInventorySoldAdapter = new DailyInventorySoldResponseAdapter()
    this.predictSaleVolumeResponseAdapter = new PredictSaleVolumeResponseAdapter()
  }

  async getMetricDetail(request: GetDailyInventorySoldRequest): Promise<PivotTableData> {
    const response = await this.queryRepository.getDailyInventorySold(request)
    return this.dailyInventorySoldAdapter.toPivotTableData(response);
  }

  async getSkuDetailByProductId(request: GetDayOnHandRequest): Promise<PivotTableData> {
    const response: GetDayOnHandResponse = await this.queryRepository.getDayOnHand(request)
    return this.dayOnHandAdapter.toPivotTableData(response)
  }

  async getForecastDetail(request: GetSalesRequest): Promise<ForecastData> {
    const sales: GetSalesResponse = await this.queryRepository.getSales(request)
    return this.salesAdapter.toForecastData(sales)
  }

 async getPredictSalesVolumes(request: PredictSaleVolumeRequest, onCompleted: (data: ForecastData) => void, onFailure: (error: Error) => void): Promise<void> {
    try{
      const response = await this.predictRepository.predictSaleVolumes(request);
      const forecastData = this.predictSaleVolumeResponseAdapter.adapt(response);
      onCompleted(forecastData);
    } catch (error: any) {
      onFailure(error);
    }
  }
}

export default new SkuDetailServiceImpl(queryRepository, predictRepository)
