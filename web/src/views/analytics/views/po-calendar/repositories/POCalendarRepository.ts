import client from '@/client/HttpClientBuilder.ts'
import type { HttpClient } from '@/client/HttpClient'
import type { VendorResponse } from '../domains/responses/VendorResponse'
import type { POMonitorMetricsRequest } from '../domains/requests/POMonitorMetricsRequest'
import type { POMonitorMetricsResponse } from '../domains/responses/POMonitorMetricsResponse'
import type { POCalendarRequest } from '../domains/requests/POCalendarRequest'
import type { POCalendarRow } from '../domains/responses/POCalendarRow'

export abstract class POCalendarRepository {
  abstract getVendors(): Promise<VendorResponse[]>;
  abstract getMonitorMetrics(request: POMonitorMetricsRequest): Promise<POMonitorMetricsResponse>;
  abstract getListPOCalendar(request: POCalendarRequest): Promise<POCalendarRow[]>;
}

export class POCalendarRepositoryImpl extends POCalendarRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getVendors(): Promise<VendorResponse[]> {
    return this.client
      .get<VendorResponse[]>('/api/po-calendar/get-vendors')
      .then((response) => response.data);
  }

  async getMonitorMetrics(request: POMonitorMetricsRequest): Promise<POMonitorMetricsResponse> {
    return this.client
      .post<POMonitorMetricsResponse>('/api/po-calendar/get-monitor-metrics', request)
      .then((response) => response.data);
  }

  async getListPOCalendar(request: POCalendarRequest): Promise<POCalendarRow[]> {
    return this.client
      .post<POCalendarRow[]>('/api/po-calendar/get-list', request)
      .then((response) => response.data);
  }
}

export default new POCalendarRepositoryImpl(client);
