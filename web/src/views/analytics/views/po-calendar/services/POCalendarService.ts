import type { POCalendarRequest } from "../domains/requests/POCalendarRequest";
import type { POMonitorMetricsRequest } from "../domains/requests/POMonitorMetricsRequest";
import type { POCalendarRow } from "../domains/responses/POCalendarRow";
import type { POMonitorMetricsResponse } from "../domains/responses/POMonitorMetricsResponse";
import type { VendorResponse } from "../domains/responses/VendorResponse";
import type { POCalendarRepository } from "../repositories/POCalendarRepository";
import repository from "../repositories/POCalendarRepository";

export abstract class POCalendarService{
  abstract getVendors(): Promise<VendorResponse[]>;
  abstract getMonitorMetrics(request: POMonitorMetricsRequest): Promise<POMonitorMetricsResponse>;
  abstract getListPOCalendar(request: POCalendarRequest): Promise<POCalendarRow[]>;
}

export class POCalendarServiceImpl extends POCalendarService {
  private readonly repository: POCalendarRepository

  constructor(repository: POCalendarRepository) {
    super()
    this.repository = repository
  }

  async getVendors(): Promise<VendorResponse[]> {
    return this.repository.getVendors();
  }

  async getMonitorMetrics(request: POMonitorMetricsRequest): Promise<POMonitorMetricsResponse> {
    return this.repository.getMonitorMetrics(request);
  }

  async getListPOCalendar(request: POCalendarRequest): Promise<POCalendarRow[]> {
    return this.repository.getListPOCalendar(request);
  }
}

export default new POCalendarServiceImpl(repository);
