<script setup lang="ts">
import type { POCalendarRow } from '../../../../domains/responses/POCalendarRow';
import { POStatus } from '../../types/POCalendarType';

const props = defineProps<{
  date: string;
  data: POCalendarRow;
}>();

const getStatusText = (status: POStatus): string => {
  switch (status) {
    case POStatus.OPEN:
      return 'Đang mở';
    case POStatus.COMPLETED:
      return 'Đã hoàn thành';
    case POStatus.CANCELLED:
      return 'Đã hủy';
    case POStatus.RELEASED:
      return 'Released';
    default:
      return 'Unknown';
  }
};

const formatDate = (date: string): string => {
  if (!date) return '';
  const parts = date.split('-');
  return `${parts[2]}/${parts[1]}/${parts[0]}`;
};

//date: dd/MM/yyyy
const getDayOfWeekAndDate = (date: string) => {
  const parts = formatDate(date).split('/');
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1;
  const year = parseInt(parts[2], 10);

  const dateObj = new Date(year, month, day);

  const weekdayNum = dateObj.getDay();

  const weekdays = [
    'Chủ nhật',
    'Thứ 2',
    'Thứ 3',
    'Thứ 4',
    'Thứ 5',
    'Thứ 6',
    'Thứ 7'
  ];

  const weekday = weekdays[weekdayNum];
  return `${weekday}, Ngày ${date}`;
}

</script>

<template>
  <ATooltip placement="leftTop" color="#F9FAFB" overlayClassName="delivery-tooltip">
    <template #title>
      <div class="tooltip-content">
        <div class="tooltip-header">
          <ATypography style="font-size: 14px; color: #101828; font-weight: 500;">{{ props.data.storeName }}</ATypography>
          <ATypography style="color: #667085; font-size: 12px; font-weight: 400;">{{ getDayOfWeekAndDate(props.date) }}</ATypography>
        </div>
        <div class="tooltip-body">
          <div>
            <div class="body-column"><span class="text-column">Mã phiếu PO:</span></div>
            <div><span class="pt-code">{{ props.data.code }}</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">Nơi chuyển:</span> </div>
            <div><span class="text-value">{{ props.data.vendorName }}</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">SKU về Store:</span></div>
            <div><span class="text-value">{{ props.data.totalSku }} SKU, {{props.data.totalItems}} items</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">Ngày tạo:</span></div>
            <div><span class="text-value">{{ formatDate(props.data.createdDate) }}</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">Ngày giao hàng dự kiến:</span></div>
            <div><span class="text-value">{{ formatDate(props.data.expectedDeliveryDate) }}</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">Ngày nhận:</span></div>
            <div><span class="text-value">{{ formatDate(props.data.completedDate) }}</span></div>
          </div>
          <div>
            <div class="body-column"><span class="text-column">Trạng thái:</span></div>
            <div class="status-value" :class="[`status-${props.data.status}`]">
              <span class="status-text" :class="[`status-${props.data.status}`]">
                <span class="status-dot" :class="[`status-${props.data.status}`]"></span>
                {{ getStatusText(props.data.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <slot />
  </ATooltip>
</template>

<style scoped>
.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  line-height: 1.5;
  color: #1f2937 !important;
  padding: 8px;
}

.tooltip-header {
  display: flex;
  flex-direction: column;
}

.tooltip-body {
  background-color: #fff;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pt-code {
  color: #1890ff;
  font-weight: 500;
  font-size: 14px;
}

.text-column {
  font-weight: 400;
  font-size: 14px;
  color: #475467;
}

.text-value {
  font-weight: 500;
  font-size: 14px;
  color: #101828;
}

.body-column {
  min-width: 160px;
}

.status-value {
  padding: 4px 8px;
  border-radius: 16px;
}

.status-value.status-completed {
  background-color: #EDFCF2;
}

.status-value.status-pending {
  background-color: #fffaee;
}

.status-value.status-delayed {
  background-color: #fff1f1;
}

.status-text {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.status-text.status-1 {
  color: #faad14;
}

.status-text.status-3 {
  color: #52c41a;
}

.status-text.status-5 {
  color: #ff4d4f;
}

.status-text.status-7 {
  color: #52c41a;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
}

.status-dot.status-1 {
  background-color: #faad14;
}

.status-dot.status-3 {
  background-color: #52c41a;
}

.status-dot.status-5 {
  background-color: #ff4d4f;
}

.status-dot.status-7 {
  background-color: #52c41a;
}
</style>

<style>
.delivery-tooltip .ant-tooltip-inner {
  width: 400px !important;
  max-width: 80vw !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

.delivery-tooltip .ant-tooltip-arrow-content {
  background-color: #F0F0F0;
}

.delivery-tooltip .tooltip-body > div {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
}

@media (max-width: 768px) {
  .delivery-tooltip .ant-tooltip-inner {
    width: 95vw !important;
  }
}
</style>
