<script setup lang="ts">
import { ref, computed, nextTick, watch, h } from 'vue'
import { useAvailableWidthProvider } from '@/hooks/userContainerWidth.ts'
import BaseTable from '@/components/table/BaseTable.vue';
import type { BaseTableColumn, TableConfig } from '@/components/table/types/TableTypes';
import type { CalendarDelivery } from '../../types/POCalendarType';
import { storeToRefs } from 'pinia';
import { usePOCalendarStore } from '../../../../stores/POCalendarStore';
import { Flex as AFlex } from 'ant-design-vue'
import SaleVolumeSparkLine from '@/components/charts/SaleVolumeSparkLine.vue'
import DeliveryTooltip from './DeliveryTooltip.vue';
import { convertDateToYYYYMMDD } from '@/utils/DateUtils';
import { useSelectedStore } from '@/views/analytics/views/inventory-overview/stores/SelectedStoreStore';
import type { StoreSalesVolumeRow } from '@/views/analytics/views/pt-calendar/domains/responses/StoreSalesVolumeRow';
import salesVolumeService from '@/views/analytics/views/pt-calendar/services/SalesVolumeService';

const props = defineProps<{
  records: CalendarDelivery[];
}>();

const { selectedStoreIds } = storeToRefs(useSelectedStore());

const storeSalesVolumes = ref<StoreSalesVolumeRow[]>([]);

watch(selectedStoreIds, async (newStoreIds) => {
  if (newStoreIds.length > 0) {
    try {
      const toDate = new Date();
      const fromDate = new Date(toDate.getTime() - 14 * 24 * 60 * 60 * 1000);
      storeSalesVolumes.value = await salesVolumeService.getStoreSalesVolume({
        storeIds: newStoreIds,
        fromDate: convertDateToYYYYMMDD(fromDate),
        toDate: convertDateToYYYYMMDD(toDate)
      });
    } catch (error) {
      console.error('Failed to fetch store sales volumes:', error);
    }
  } else {
    storeSalesVolumes.value = [];
  }
}, { immediate: true, deep: true });

type PaginationInfo = {
  current: number; // Trang hiện tại
  pageSize: number; // Số lượng bản ghi trên mỗi trang
  total: number; // Tổng số bản ghi
};

// Pagination state
const pagination = ref<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
});

// Record state
const loading = ref(false);
const error = ref<string | null>(null);

const tableRef = ref<HTMLDivElement | null>(null)
const containerRef = ref<HTMLDivElement>()

// Sử dụng hook để provide available width cho child components
const { updateAvailableWidth } = useAvailableWidthProvider(containerRef, {
  left: 32, // margin-left của .main-table-detail
  padding: 32
});

// Hàm xử lý khi người dùng chuyển trang
const handleTableChange = (paginationInfo: PaginationInfo) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
};

// Tính toán height động
const ROW_HEIGHT = 80 // Height của mỗi row (bao gồm border)
const HEADER_HEIGHT = 55 // Height của table header
const MIN_HEIGHT = 400 // Minimum height
const MAX_HEIGHT = 900 // Maximum height để tránh table quá cao

const getCurrentPageItems = () => {
  const startIndex = (pagination.value.current - 1) * pagination.value.pageSize;
  const endIndex = Math.min(startIndex + pagination.value.pageSize, props.records.length);
  return props.records.slice(startIndex, endIndex);
};

const calculateTableHeight = computed(() => {
  const baseHeight = HEADER_HEIGHT
  const currentPageItems = getCurrentPageItems();
  const dataRowsHeight = currentPageItems.length * ROW_HEIGHT

  const totalHeight = baseHeight + dataRowsHeight

  // Giới hạn trong khoảng min-max
  return Math.min(Math.max(totalHeight, MIN_HEIGHT), MAX_HEIGHT)
})

const { firstDayOfWeek } = storeToRefs(usePOCalendarStore());

const getWeekDays = () => {
  const days = [];
  const startDate = new Date(firstDayOfWeek.value);
  const weekdayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    days.push({
      date: convertDateToYYYYMMDD(date),
      weekday: weekdayNames[date.getDay()],
      day: date.getDate(),
      isToday: date.toDateString() === new Date().toDateString(),
    });
  }
  return days;
};

const dayColumns = computed<BaseTableColumn[]>(() => {
  const weekDays = getWeekDays();
  return weekDays.map(day => ({
    title: day.date,
    dataIndex: day.date,
    key: day.date,
    width: 220,
    ellipsis: true,
    customRender: ({ record }: { record: CalendarDelivery }) => {
      const delivery = record.days.find(d => d.date === day.date);
      if (!delivery || delivery.poOrders.length === 0) return '-';
      const dotStyle = {
        width: '4px',
        height: '4px',
        borderRadius: '50%',
        marginRight: '8px',
        backgroundColor: "#155EEF"
      };
      const containerStyle = {
        flexDirection: 'column',
        gap: '8px',
      };
      const itemStyle = {
        alignItems: 'center',
      };
      const poStyle = {
        color: '#155EEF',
        fontWeight: '500',
        fontSize: '14px',
      };
      const infoStyle = {
        color: '#101828',
        fontWeight: '500',
        fontSize: '14px',
      }

      return h(AFlex, { style: containerStyle }, () => {
        return delivery.poOrders.map(poOrder => {
          return [
            h(AFlex, { style: itemStyle },() => [
              h('div', { style: dotStyle }),
              h('div', {}, [
                h(DeliveryTooltip, { data: poOrder, date: delivery.date }, [
                  h('a', { style: poStyle }, poOrder.code)
                ]),
                h('span', { style: infoStyle }, ` (${poOrder.totalSku} SKU)`),
              ]),
            ]),
          ];
        }).flat(); // Làm phẳng mảng để hiển thị tất cả các dòng
      });
    },
  }));
})

const columns = computed<BaseTableColumn[]>(() => [
  {
    title: '#',
    dataIndex: 'idx',
    key: 'idx',
    width: 60,
    align: 'center',
    fixed: 'left',
  },
  {
    title: 'Siêu thị',
    dataIndex: 'storeName',
    key: 'storeName',
    width: 200,
    align: 'left',
    fixed: 'left',
    // ellipsis: true,
    customRender: ({ record }: { record: CalendarDelivery }) => {
      return h('span', { style: { color: '#155EEF' } }, record.storeName);
    }
  },
  {
    title: 'Sales volume',
    key: 'salesVolume',
    width: 200,
    align: 'left',
    fixed: 'left',
    // ellipsis: true,
    customRender: ({ record }: { record: CalendarDelivery }) => {
      const data = storeSalesVolumes.value.find(sv => sv.storeId === record.storeId);
      return h(SaleVolumeSparkLine, {
        data: data ? data.salesVolume : [],
        width: 154,
        height: 24
      })
    }
  },
  ...dayColumns.value,
])

const tableConfig = computed<TableConfig>(() =>{
  return {
    bordered: true,
    hoverable: true,
    size: 'middle',
    loading: loading.value,
    scroll: {
      y: 900
    },
    pagination: {
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
      total: pagination.value.total,
      onChange: (page, pageSize) => handleTableChange({ ...pagination.value, current: page, pageSize }),
    }
  }
})

// Watch để update height khi data thay đổi
watch(() => props.records.length, () => {
  nextTick(() => {
    updateAvailableWidth()
  })
})
</script>

<template>
  <div
    ref="containerRef"
    class="main-table-container"
    :style="{ height: `${calculateTableHeight}px` }"
  >
    <!-- Error Display -->
    <AAlert
      v-if="error"
      :message="error"
      type="error"
      show-icon
      closable
      style="margin-bottom: 16px"
    />

    <!-- Table -->
    <BaseTable
      ref="tableRef"
      :columns="columns"
      :data-source="records"
      :config="tableConfig"
      row-key="id"
      class="main-table"
    >
    </BaseTable>
  </div>
</template>

<style scoped>
.main-table-container {
  position: relative;
  width: 100%;
  transition: height 0.3s ease;
}

.main-table {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  scrollbar-width: none;
}

/* Đảm bảo expanded row detail không làm tràn */
.main-table-detail {
  margin-left: 32px;
  max-width: calc(100% - 32px);
  overflow-x: auto;
}

/* Empty state styling */
.main-table :deep(.ant-empty) {
  margin: 40px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-table-detail {
    margin-left: 16px;
    max-width: calc(100% - 16px);
  }
}
</style>

<style scoped src="/src/assets/pivot-tables.css"></style>
