<script setup lang="ts">
import CalendarSegmentBody from './components/body/CalendarSegmentBody.vue';
import CalendarSegmentHeader from './components/CalendarSegmentHeader.vue';

</script>

<template>
  <AFlex class="calendar-container">
    <CalendarSegmentHeader />
    <CalendarSegmentBody />
  </AFlex>
</template>

<style scoped>
.calendar-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
