<script setup lang="ts">
import { computed, ref } from 'vue'
import type { SelectProps } from 'ant-design-vue'
import { MetricBreakdownOptions } from './types'

const breakdownOptions = computed<SelectProps['options']>(() => [
  {
    label: 'Inventory quantity',
    value: MetricBreakdownOptions.InventoryQuantity,
  },
  {
    label: 'Sales volume',
    value: MetricBreakdownOptions.SalesVolume,
  },
  {
    label: 'Days on hand',
    value: MetricBreakdownOptions.DaysOnHand,
  },
  {
    label: 'Out of stock',
    value: MetricBreakdownOptions.OutOfStock,
  },
])

const breakdownValue = ref<MetricBreakdownOptions>(MetricBreakdownOptions.InventoryQuantity)

</script>

<template>
  <ASelect
    v-model:value="breakdownValue"
    :show-search="false"
    :options="breakdownOptions"
    :bordered="false"
    style="min-width: 8rem;"
  />
</template>

<style scoped>

</style>
