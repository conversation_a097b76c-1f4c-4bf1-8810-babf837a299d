import type { POCalendarRow } from "../../../domains/responses/POCalendarRow";

export type CalendarDelivery = {
  idx: number;
  storeId: string;
  storeName: string;
  days: {
    date: string; // Formatted as DD/MM/YYYY
    isToday: boolean;
    poOrders: POCalendarRow[]; // List of purchase orders for the day
  }[]
}

export enum POStatus {
  OPEN = 1,
  COMPLETED = 3,
  CANCELLED = 5,
  RELEASED = 7,
}
