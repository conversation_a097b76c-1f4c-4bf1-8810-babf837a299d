<script setup lang="ts">
import SelectStoreButton from '@/components/SelectStoreButton.vue';

</script>

<template>
  <AFlex class="header-container">
    <ATypographyTitle  :level="5">Monitor Metric</ATypographyTitle>
    <SelectStoreButton />
  </AFlex>

</template>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-selected-text {
  font-weight: 700;
  color: #007bff;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: start;
  }
}
</style>
