import { defineStore } from 'pinia';
import { ref } from 'vue';
import { POCalendarDisplay } from '../enums/POCalendarDisplay';

export const usePOCalendarStore = defineStore('POCalendar', () => {
  // State
  const displayType = ref<POCalendarDisplay>(POCalendarDisplay.Calendar);
  const firstDayOfWeek = ref<Date>(new Date());
  const selectedVendorId = ref<string | null>(null);

  function setDisplayType(type: POCalendarDisplay) {
    displayType.value = type;
  }

  return {
    // State
    selectedVendorId,
    firstDayOfWeek,
    displayType,

    // Actions
    setDisplayType,
  };
});
