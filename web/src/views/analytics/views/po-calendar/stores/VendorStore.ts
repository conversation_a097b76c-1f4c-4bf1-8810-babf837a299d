import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import poCalendarService from '../services/POCalendarService';
import type { VendorResponse } from '../domains/responses/VendorResponse';

export const useVendorStore = defineStore('Vendor', () => {
  // State
  const vendors = ref<VendorResponse[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const vendorMap = ref<Record<string, VendorResponse>>({});

  // Getters
  const vendorOptions = computed(() =>
    vendors.value.map((vendor) => ({
      label: vendor.name,
      value: vendor.id,
    })),
  )

  const hasVendors = computed(() => vendors.value.length > 0)

  // Actions
  const fetchVendors = async () => {
    if (loading.value) return

    loading.value = true
    error.value = null

    try {
      vendors.value = await poCalendarService.getVendors();
      vendorMap.value = Object.fromEntries(vendors.value.map(vendor => [vendor.id, vendor]));
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải danh sách vendors'
      console.error('Error fetching vendors:', err)
    } finally {
      loading.value = false
    }
  }

  const clearVendors = () => {
    vendors.value = []
    error.value = null
  }

  const getVendorById = (id: string) => {
    return vendorMap.value[id] || null
  }

  const getVendorByName = (name: string) => {
    return vendors.value.find((vendor) => vendor.name === name)
  }

  return {
    // State
    vendors,
    loading,
    error,

    // Getters
    vendorOptions,
    hasVendors,

    // Actions
    fetchVendors,
    clearVendors,
    getVendorById,
    getVendorByName,
  }
});
