<script setup lang="ts">
import { onMounted } from 'vue';
import MonitorMetricSegment from './segments/monitor-metric-segment/MonitorMetricSegment.vue';
import { useStoreStore } from '../replenishment/segments/monitor-sku/stores/StoreStore';
import CalendarSegment from './segments/calendar-segment/CalendarSegment.vue';
import { useVendorStore } from './stores/VendorStore';

const { fetchStores } = useStoreStore();
const { fetchVendors } = useVendorStore();

onMounted(() => {
  fetchStores();
  fetchVendors();
});

</script>

<template>
  <div class="po-calendar-page">
    <MonitorMetricSegment />

    <CalendarSegment />
  </div>
</template>

<style scoped>
.po-calendar-page {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
