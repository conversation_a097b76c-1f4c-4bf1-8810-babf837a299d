<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'
import SidebarAction from '@/views/analytics/components/SidebarAction.vue'
import Appbar from '@/views/analytics/components/Appbar.vue'
import Sidebar from '@/views/analytics/components/Sidebar.vue'
import { useSidebarStore } from '@/stores/sidebarStore'

const router = useRouter()
const route = useRoute()
const sidebarStore = useSidebarStore()

const findParentRoute = (routeName: string) => {
  for (const tab of tabs.value) {
    if (tab.key === routeName) return routeName
    if (tab.children?.some((child) => child.key === routeName)) {
      return tab.key
    }
  }
  return routeName
}

const activeTab = computed(() => {
  const currentRouteName: string = route.name?.toString() ?? ''
  return findParentRoute(currentRouteName)
})

const collapsed = computed(() => sidebarStore.isCollapsed)

const tabs = computed(() => {
  return [
    {
      key: 'InventoryOverview',
      title: 'Inventory Overview',
    },
    {
      key: 'pt-calendar',
      title: 'PT Calendar',
    },
    {
      key: 'po-calendar',
      title: 'PO Calendar',
    },
    {
      key: 'InventoryDetail2',
      title: 'Inventory Detail',
      children: [
        {
          key: 'ViewBySKU',
          title: 'Chiều SKU',
        },
        {
          key: 'ViewByForecast',
          title: 'Chiều Forecast',
        },
        {
          key: 'ViewByStore',
          title: 'Chiều Store',
        },
      ],
    },
    {
      key: 'Replenishment',
      title: 'Replenishment',
    },
    {
      key: 'performance-report',
      title: 'Performance Report',
    },
  ]
})

const onTabChange = (key: string) => {
  router.push({ name: key }) // tab1 -> Tab1
}
</script>

<template>
  <ALayout>
    <Appbar />
    <ALayout :style="{ background: `white`, marginTop: `48px` }">
      <AFlex style="flex: 1; height: calc(100vh - 48px)">
        <Sidebar />
        <AFlex
          vertical
          :style="{
            flex: 1,
            marginLeft: collapsed ? '0px' : '320px',
            transition: 'margin-left 0.3s ease',
            height: '100%',
            overflow: 'hidden',
          }"
          class="main-content"
        >
          <SidebarAction />
          <div class="tabs-wrapper">
            <ATabs
              :activeKey="activeTab"
              @change="onTabChange"
              size="middle"
              class="tabs-container"
            >
              <ATabPane v-for="tab in tabs" :key="tab.key">
                <template #tab>
                  <span>{{ tab.title }}</span>
                </template>
              </ATabPane>
            </ATabs>
          </div>
          <div class="tab-container">
            <router-view />
          </div>
        </AFlex>
      </AFlex>
    </ALayout>
  </ALayout>
</template>

<style>
.main-content {
  height: calc(100vh - 48px);
  overflow: hidden;
}

.tab-container {
  padding: 1rem 1.5rem;
  flex: 1;
  overflow-y: auto;
  height: 0;
}

.tabs-wrapper {
  max-width: 100vw;
  padding: 0 1rem;
  overflow-x: auto;
  overflow-y: hidden;
  flex-shrink: 0;
  scrollbar-width: none;
}

.tabs-container {
  min-width: max-content;
}

.tabs-container .ant-tabs-nav-list {
  padding: 0 1rem;
  min-width: max-content;
}

.tabs-container .ant-tabs-nav {
  margin: 0;
}

.tabs-container .ant-tabs-tab {
  white-space: nowrap;
  flex-shrink: 0;
}

/* Responsive cho tabs */
@media (max-width: 768px) {
  .tabs-container .ant-tabs-nav-list {
    padding: 0 0.5rem;
  }

  .tabs-container .ant-tabs-tab {
    margin-right: 8px;
  }
}
</style>
