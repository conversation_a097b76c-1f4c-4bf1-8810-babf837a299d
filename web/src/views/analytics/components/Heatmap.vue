<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { HeatmapResponse } from '@/views/analytics/views/inventory-detail/domains/Request.ts'
import { type TooltipFormatterContextObject } from 'highcharts'

interface Props {
  data: HeatmapResponse
  tooltipFormatter?: (this: TooltipFormatterContextObject) => string
}

const props = defineProps<Props>()
const PLOT_BORDER_WIDTH = 1;
const PLOT_BORDER_COLOR = '#D0D5DD';

const AXIS_TEXT_COLOR = '#667085';
const MIN_COLOR = '#84ADFF';
const MAX_COLOR = '#D92D20';

const chartOptions = computed(() => ({
  chart: {
    type: 'heatmap',
    plotBorderWidth: PLOT_BORDER_WIDTH,
    plotBorderColor: PLOT_BORDER_COLOR,

  },

  exporting: {
    enabled: false,
  },
  credits: {
    enabled: false,
  },

  title: null,

  xAxis: {
    categories: props.data.xAxis,
    title: null,
    lineColor: 'transparent',
    labels: {
      style: {
        color: AXIS_TEXT_COLOR
      },
    },
  },

  yAxis: {
    categories: props.data.yAxis,
    title: null,
    labels: {
      style: {
        color: AXIS_TEXT_COLOR
      },
    },
  },

  colorAxis: {
    min: 0,
    minColor: MIN_COLOR,
    maxColor: MAX_COLOR,
  },

  legend: {
    enabled: false,
  },

  tooltip: {
    useHTML: true,
    borderRadius: 8,
    backgroundColor: 'white',
    borderColor: '#ccc',
    shadow: false,
    style: {
      color: '#333',
      fontSize: '14px'
    },
    formatter: props.tooltipFormatter
  },

  plotOptions: {
    heatmap: {
      borderWidth: PLOT_BORDER_WIDTH,
      borderColor: '#ffffff',
      dataLabels: {
        enabled: false,
      },
    },
  },

  series: [
    {
      name: 'Số lượng SKU hết hàng',
      data: props.data.data,
    },
  ],
}))
const windowWidth = ref(window.innerWidth)
const chartRef = ref<any>(null)

const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (chartRef.value && chartRef.value.chart) {
    setTimeout(() => {
      chartRef.value.chart.reflow()
    }, 100)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

</script>

<template>
  <highcharts ref="chartRef" :options="chartOptions" />
</template>

<style scoped></style>
