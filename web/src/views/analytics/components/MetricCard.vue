<script setup lang="ts">

defineProps<{
  cardColor: string
}>()

</script>

<template>
  <ACard size="default" class="metric-card-container"  :style="{ '--card-color': cardColor }">
    <slot />
  </ACard>
</template>

<style scoped>
.metric-card-container{
  flex: 1;
  border-top-width: 4px;
  border-top-color: var(--card-color);
  overflow: hidden
}

.metric-card-container .ant-card-body{
  padding: 1rem 0.75rem;
  width: 100%;
}
</style>
