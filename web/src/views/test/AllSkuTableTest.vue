<template>
  <div class="all-sku-table-test">
    <div class="test-header">
      <h1>All SKU Table Test</h1>
      <div class="test-actions">
        <Button @click="handleRefresh" :loading="isRefreshing">
          Refresh Data
        </Button>
        <Button @click="handleLoadData">
          Load Data
        </Button>
      </div>
    </div>

    <div class="test-content">
      <AllSkuTable ref="allSkuTableRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from 'ant-design-vue'
import AllSkuTable from '@/views/analytics/views/inventory-detail/components/all-sku/AllSkuTable.vue'

// Refs
const allSkuTableRef = ref<InstanceType<typeof AllSkuTable>>()
const isRefreshing = ref(false)

// Event handlers
const handleRefresh = async () => {
  isRefreshing.value = true
  try {
    await allSkuTableRef.value?.refresh()
  } finally {
    isRefreshing.value = false
  }
}

const handleLoadData = () => {
  allSkuTableRef.value?.loadData()
}

// Get store for debugging
const getStore = () => {
  return allSkuTableRef.value?.getStore()
}

// Expose for debugging in console
defineExpose({
  getStore,
  refresh: handleRefresh,
  loadData: handleLoadData
})
</script>

<style scoped>
.all-sku-table-test {
  min-height: 100vh;
  background: #f0f2f5;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #d9d9d9;
  margin-bottom: 16px;
}

.test-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.test-actions {
  display: flex;
  gap: 8px;
}

.test-content {
  padding: 0 24px;
}
</style>
