# Build stage
FROM node:18-alpine AS build
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm i

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine
WORKDIR /app

# Install serve to serve static files
RUN npm install -g serve

# Copy built application from build stage
COPY --from=build /app/dist ./dist

# Copy .env file for production configuration
COPY .env .env

# Expose port 9081
EXPOSE 9081

# Start the application
CMD ["serve", "-s", "dist", "-l", "9081"]
